import { NextApiRequest, NextApiResponse } from "next";
import { supabaseClient } from "utils/supabase/client";

const handleError = (res: NextApiResponse, message: string, error: any) => {
  console.error(message, error);
  return res.status(500).json({
    message,
    error: error.message,
  });
};

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== "GET") {
    return res.status(405).json({ message: "Method not allowed" });
  }

  try {
    const { role } = req.query;

    if (!role || !["staff", "admin", "volunteer"].includes(role as string)) {
      return res.status(403).json({
        message:
          "Unauthorized. Only staff, admin or volunteer can fetch category data.",
      });
    }

    const { data: categoryData, error: categoryError } = await supabaseClient
      .from("category")
      .select(
        `
        *,
        products (
          id
        )
      `
      )
      .order("updated_at", { ascending: true })
      .order("created_at", { ascending: false });

    if (categoryError) {
      return res.status(500).json({
        message: "Error getting product category data from the database",
        error: categoryError.message,
      });
    }

    return res.status(200).json(categoryData || []);
  } catch (error) {
    return handleError(
      res,
      "Failed to process the request due to an unexpected error.",
      error
    );
  }
}
