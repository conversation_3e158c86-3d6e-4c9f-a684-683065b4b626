import { Pencil, Plus, X } from "lucide-react";
import { CustomButton } from "@/components/ui/custom-button";
import { Input } from "@/components/ui/input";
import { Search } from "lucide-react";
import React, { useState } from "react";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { useAlert } from "context/useAlert";
import { useCouponsData } from "context/useCoupons";
import { DataTable } from "@/components/data-table";
import { formatDate } from "utils";
import { Coupon } from "types";
import { useAuthCheck } from "hooks/useAuthCheck";
import { RequiredLabel } from "@/components/ui/required-label";

export default function CouponManagement() {
  const { user, token } = useAuthCheck();
  const { showAlert } = useAlert();
  const { couponsData, fetchingCouponsData } = useCouponsData();
  const [showForm, setShowForm] = useState(false);
  const [selectedCoupon, setSelectedCoupon] = useState<Coupon | null>(null);
  const [isDisabled, setIsDisabled] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    code: "",
    startDate: new Date().toISOString().split("T")[0],
    endDate: "",
    status: "active",
    pointValue: 1,
    referredBy: "",
    couponLimit: 1,
  });

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value,
    });
  };

  const handleStatusChange = (value) => {
    setFormData({
      ...formData,
      status: value,
    });
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    try {
      setIsDisabled(true);
      const endpoint = selectedCoupon
        ? "/api/admin/coupons/put"
        : "/api/admin/coupons/post";

      const response = await fetch(endpoint, {
        method: selectedCoupon ? "PUT" : "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({
          ...formData,
          userRole: user?.user_metadata?.role,
          ...(selectedCoupon && {
            id: selectedCoupon.id,
          }),
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to submit form");
      }

      const responseData = await response.json();
      if (!response.ok) {
        throw new Error(
          responseData.message ||
            (selectedCoupon
              ? "Failed to update coupon"
              : "Failed to create coupon")
        );
      }

      showAlert(
        selectedCoupon
          ? "Coupon updated successfully!"
          : "Coupon created successfully!",
        "success"
      );

      setFormData({
        name: "",
        description: "",
        code: "",
        startDate: new Date().toISOString().split("T")[0],
        endDate: "",
        status: "active",
        pointValue: 1,
        referredBy: "",
        couponLimit: 1,
      });
      setShowForm(false);
    } catch (error) {
      console.error("Error submitting form:", error);
    } finally {
      setIsDisabled(false);
    }
  };

  const handleEdit = async (coupon: Coupon) => {
    setSelectedCoupon(coupon);
    setFormData({
      name: coupon.coupon_name,
      description: coupon.coupon_description || "",
      code: coupon.coupon_code,
      startDate: coupon.start_date,
      endDate: coupon.end_date,
      status: coupon.status,
      pointValue: coupon.point_value,
      referredBy: coupon.referred_by,
      couponLimit: coupon.coupon_limit,
    });
    setShowForm(true);
  };

  const filterCoupons = (coupons: typeof couponsData) => {
    return coupons.filter((coupon) => {
      const matchesSearch =
        coupon.coupon_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        coupon.coupon_code.toLowerCase().includes(searchTerm.toLowerCase()) ||
        coupon.coupon_description
          .toLowerCase()
          .includes(searchTerm.toLowerCase());

      return matchesSearch;
    });
  };

  const headers = [
    "Name",
    "Code",
    "Usage Count",
    "Coupon Limit",
    "Referred To",
    "Start Date",
    "End Date",
    "Status",
    "Point Value",
    "Actions",
  ];
  const renderRow = (coupon) => (
    <tr key={coupon.id} className="border-b">
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
        {coupon.coupon_name}
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
        {coupon.coupon_code}
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
        {coupon.usage_count ?? 0}
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
        {coupon.coupon_limit}
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
        {coupon.referred_by}
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
        {formatDate(coupon.start_date)}
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
        {formatDate(coupon.end_date)}
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
        <span
          className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
            coupon.status === "active"
              ? "bg-green-100 text-green-800"
              : coupon.status === "expired"
              ? "bg-red-100 text-red-800"
              : coupon.status === "inactive"
              ? "bg-yellow-100 text-yellow-800"
              : "bg-gray-100 text-gray-800"
          }`}
        >
          {coupon.status.charAt(0).toUpperCase() + coupon.status.slice(1)}
        </span>
      </td>
      <td className="p-4">{coupon.point_value}</td>
      <td className="p-4">
        <div className="flex space-x-2 justify-center">
          <CustomButton
            variant="ghost"
            size="icon"
            onClick={() => handleEdit(coupon)}
          >
            <Pencil className="h-4 w-4" />
          </CustomButton>
          {/* <CustomButton
            variant="ghost"
            size="icon"
            className="text-destructive"
            onClick={() => handleDeleteClick(coupon)}
          >
            <Trash2 className="h-4 w-4" />
          </CustomButton> */}
        </div>
      </td>
    </tr>
  );

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-semibold">Coupon Management</h2>
        <CustomButton
          variant="outline"
          onClick={() => {
            setShowForm(!showForm);
            setSelectedCoupon(null);
            setFormData({
              name: "",
              description: "",
              code: "",
              startDate: new Date().toISOString().split("T")[0],
              endDate: "",
              status: "active",
              pointValue: 1,
              referredBy: "",
              couponLimit: 1,
            });
          }}
          disabled={isDisabled}
        >
          <Plus className="h-4 w-4 mr-2" />
          Add New Coupon
        </CustomButton>
      </div>

      <div className="flex gap-4 items-center mb-6">
        <div className="relative flex-1">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
          <Input
            placeholder="Search coupon..."
            className="pl-8"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
      </div>

      {showForm && (
        <CouponForm
          formData={formData}
          handleSubmit={handleSubmit}
          isDisabled={isDisabled}
          setShowForm={setShowForm}
          selectedCoupon={selectedCoupon}
          handleInputChange={handleInputChange}
          handleStatusChange={handleStatusChange}
        />
      )}

      <DataTable
        headers={headers}
        data={filterCoupons(couponsData)}
        renderRow={renderRow}
        isLoading={fetchingCouponsData}
      />
    </div>
  );
}

function CouponForm({
  formData,
  handleSubmit,
  isDisabled,
  setShowForm,
  selectedCoupon,
  handleInputChange,
  handleStatusChange,
}) {
  return (
    <Card className="mb-6">
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <h2 className="text-lg font-medium">
          {selectedCoupon ? "Edit Coupon" : "Add New Coupon"}
        </h2>
        <CustomButton
          variant="ghost"
          size="sm"
          onClick={() => setShowForm(false)}
          disabled={isDisabled}
        >
          <X className="h-4 w-4" />
        </CustomButton>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <RequiredLabel htmlFor="name">Coupon Name</RequiredLabel>
              <Input
                id="name"
                name="name"
                placeholder="Enter coupon name"
                value={formData.name}
                onChange={handleInputChange}
                required
              />
            </div>

            <div className="space-y-2">
              <RequiredLabel htmlFor="code">Coupon Code</RequiredLabel>
              <Input
                id="code"
                name="code"
                placeholder="Enter unique coupon code"
                value={formData.code}
                onChange={handleInputChange}
                required
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              name="description"
              placeholder="Enter coupon description"
              className="resize-none"
              value={formData.description}
              onChange={handleInputChange}
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <RequiredLabel htmlFor="startDate">Start Date</RequiredLabel>
              <Input
                id="startDate"
                name="startDate"
                type="date"
                value={formData.startDate}
                onChange={handleInputChange}
                required
              />
            </div>

            <div className="space-y-2">
              <RequiredLabel htmlFor="endDate">End Date</RequiredLabel>
              <Input
                id="endDate"
                name="endDate"
                type="date"
                min={formData.startDate || ""}
                value={formData.endDate}
                onChange={handleInputChange}
                required
              />
            </div>

            <div className="space-y-2">
              <RequiredLabel htmlFor="status">Status</RequiredLabel>
              <Select
                value={formData.status}
                onValueChange={handleStatusChange}
                defaultValue="active"
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="inactive">Inactive</SelectItem>
                  <SelectItem value="expired">Expired</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <RequiredLabel htmlFor="pointValue">Point Value</RequiredLabel>
              <Input
                id="pointValue"
                name="pointValue"
                type="number"
                min="1"
                placeholder="Enter point value"
                value={formData.pointValue}
                onChange={handleInputChange}
                required
              />
            </div>

            <div className="space-y-2">
              <RequiredLabel htmlFor="couponLimit">Coupon Limit</RequiredLabel>
              <Input
                id="couponLimit"
                name="couponLimit"
                type="number"
                min="1"
                placeholder="Enter coupon limit"
                value={formData.couponLimit}
                onChange={handleInputChange}
                required
              />
            </div>

            <div className="space-y-2">
              <RequiredLabel htmlFor="pointValue">Referred To</RequiredLabel>
              <Input
                id="referredBy"
                name="referredBy"
                type="text"
                placeholder="Enter referred by"
                value={formData.referredBy}
                onChange={handleInputChange}
                required
              />
            </div>
          </div>

          <div className="flex justify-end gap-2">
            <CustomButton
              type="button"
              variant="outline"
              onClick={() => setShowForm(false)}
              disabled={isDisabled}
            >
              Cancel
            </CustomButton>
            <CustomButton type="submit" disabled={isDisabled}>
              {selectedCoupon ? "Update Coupon" : "Create Coupon"}
            </CustomButton>
          </div>
        </form>
      </CardContent>
    </Card>
  );
}
