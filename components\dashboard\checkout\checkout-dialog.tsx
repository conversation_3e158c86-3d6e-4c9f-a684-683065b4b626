import ClientSearch from "@/components/client-search";
import { Card, CardContent } from "@/components/ui/card";
import { CustomButton } from "@/components/ui/custom-button";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import ModalAlert from "@/components/ui/modal-alert";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useAlert } from "context/useAlert";
import { useCheckedInToday } from "context/useCheckedInToday";
import { useClientSearch } from "context/useClientSearchContext";
import { useHeldOrderData } from "context/useHeldOrderData";
import { useSelectClient } from "context/useSelectClientContext";
import { useAuth<PERSON>heck } from "hooks/useAuthCheck";
import { useLocation } from "hooks/useLocation";
import { Search, X } from "lucide-react";
import { HeldOrder, OrderItem } from "pages/checkout";
import { Fragment, useEffect, useState } from "react";
import { CheckoutStatusType } from "types";
import {
  formatDate,
  getCurrentActiveMembership,
  getInitials,
  getNoteStyles,
} from "utils";
import { supabaseClient } from "utils/supabase/client";
import { CancelOrderDialog } from "./cancel-order-dialog";
import { HoldOrderDialog } from "./hold-order-dialog";
import { Member } from "./member-search";
import { NewOrder } from "./new-order";
import OnholdOrderContent from "./onhold-order-content";
import { ReceiptModal } from "./receipt-modal";
import ReturnItemsContent from "./return-items-content";

export function CheckoutContent() {
  const { user, token } = useAuthCheck();
  const { city, region, country, error } = useLocation();

  const { showAlert } = useAlert();
  const { refreshData } = useCheckedInToday();
  const { refreshSelectedClient, selectedClient } = useSelectClient();
  const { heldOrderData, refreshData: refreshHeldOrderData } =
    useHeldOrderData();

  const [showReceiptModal, setShowReceiptModal] = useState(false);
  const [showHoldDialog, setShowHoldDialog] = useState(false);
  const [showCancelDialog, setShowCancelDialog] = useState(false);
  const [showModal, setShowModal] = useState(false);
  const [modalConfig, setModalConfig] = useState({
    title: "",
    message: "",
    type: "error" as "warning" | "error" | "success" | "info",
    confirmText: "OK",
  });
  const [showDialog, setShowDialog] = useState(true);

  const [activeTab, setActiveTab] = useState("new-order");

  const [cart, setCart] = useState<OrderItem[]>([]);
  const [selectedMember, setSelectedMember] = useState<Member | undefined>();
  const [orderNumber, setOrderNumber] = useState<string | null>(null);
  const [completingOrder, setCompletingOrder] = useState(false);
  const totalPoints = cart.reduce(
    (sum, item) => sum + item.points * item.quantity,
    0
  );

  const [checkoutStatus, setCheckoutStatus] =
    useState<CheckoutStatusType>("not-checked-in");

  const handlePrintReceipt = () => {
    console.log("Printing receipt...");
  };

  const handleMemberSelect = (member: Member) => {
    setSelectedMember(member);
    setCheckoutStatus(member.checkoutStatus);
  };

  const handleCompleteTransaction = async () => {
    setCompletingOrder(true);
    const currentActiveMembership = getCurrentActiveMembership(
      selectedMember?.memberships
    );

    try {
      const response = await fetch("/api/user-data/checkout/complete", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({
          orderId: orderNumber,
          member_id: selectedMember?.id,
          total_points_used: totalPoints,
          items: cart,
          on_hold: false,
          role: user?.user_metadata?.role,
          checkout_by: user?.id,
          membership_id: currentActiveMembership?.id,
          store_location: !error ? `${city}, ${region}, ${country}` : error,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || "Failed to complete transaction");
      }

      showAlert(data.message, "success");
      await refreshData();
      await refreshSelectedClient();
      setShowReceiptModal(false);
      setCart([]);
      setSelectedMember(undefined);
      setCheckoutStatus("not-checked-in");
    } catch (error) {
      console.error("Transaction error:", error);
      showAlert(
        error instanceof Error
          ? error.message
          : "Failed to complete transaction",
        "error"
      );
    } finally {
      setCompletingOrder(false);
    }
  };

  const handleConfirmHold = async () => {
    if (selectedMember) {
      setCart([]);
      setSelectedMember(undefined);
      setCheckoutStatus("not-checked-in");

      try {
        const response = await fetch(
          "/api/user-data/checkout/insert-checkout",
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${token}`,
            },
            body: JSON.stringify({
              orderId: orderNumber,
              member_id: selectedMember.id,
              total_points_used: totalPoints,
              items: cart,
              on_hold: true,
              role: user?.user_metadata?.role,
              store_location: !error ? `${city}, ${region}, ${country}` : error,
            }),
          }
        );

        const responseData = await response.json();
        if (response.ok) {
          await refreshSelectedClient();
          await refreshHeldOrderData();
          showAlert(responseData.message, "success");
        } else {
          showAlert(responseData.message, "error");
        }
      } catch (error) {
        showAlert("Error adding on hold order", "error");
        throw error;
      }
    }
  };

  const handleCancelOrder = async () => {
    if (orderNumber) {
      try {
        const response = await fetch(
          `/api/user-data/checkout/delete-held-order`,
          {
            method: "DELETE",
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${token}`,
            },
            body: JSON.stringify({
              id: orderNumber,
              role: user?.user_metadata?.role,
            }),
          }
        );

        const data = await response.json();

        if (!response.ok) {
          throw new Error(data.message || "Failed to cancel order");
        }

        showAlert(data.message || "Order cancelled successfully", "success");

        await refreshSelectedClient();
      } catch (error) {
        console.error("Cancel order error:", error);
        showAlert(
          error instanceof Error ? error.message : "Failed to cancel order",
          "error"
        );
      }
    }

    setCart([]);
    setSelectedMember(undefined);
    setCheckoutStatus("not-checked-in");
    setOrderNumber(null);
    setShowCancelDialog(false);
  };

  const handleResumeOrder = async (order: HeldOrder) => {
    //filter out the order item based on the order id. update on hold status to false
    const { data, error } = await supabaseClient
      .from("orders")
      .update({ on_hold: false })
      .eq("id", order.id)
      .select();

    if (error) {
      console.error("Error updating on hold status:", error);
      return;
    }

    const mappedItems = order.items.map((item) => ({
      id: item.product_id,
      product_name: item.product_name,
      quantity: item.quantity,
      points: item.points_per_unit,
      order_max_quantity: item.order_max_quantity,
      monthly_max_quantity: item.monthly_max_quantity,
    }));

    // Set the cart with mapped items
    setCart(mappedItems);

    // Set the selected member with all necessary data
    const memberData = {
      id: order.member_id,
      first_name: order.member.first_name,
      last_name: order.member.last_name,
      memberships: order.member.memberships,
      checkoutStatus: order.member.checkoutStatus,
      check_in: order.member.check_in || [],
      role: order.member.role,
      orders: order.items,
    };

    await refreshSelectedClient();
    handleMemberSelect(memberData);
    setActiveTab("new-order");

    setOrderNumber(order.id);
  };

  const handleDialogVisibility = async (open: boolean) => {
    setShowDialog(open);

    // When dialog closes, refresh the selected member data to get updated checkout status
    if (!open && selectedMember) {
      await refreshSelectedClient();
      // Get the updated member data from the context
      if (selectedClient && selectedClient.id === selectedMember.id) {
        setSelectedMember(selectedClient);
        setCheckoutStatus(selectedClient.checkoutStatus);
      }
    }
  };

  useEffect(() => {
    if (!selectedClient) return;
    setSelectedMember(selectedClient);
    setCheckoutStatus(selectedClient.checkoutStatus);
  }, [selectedClient]);

  return (
    <>
      <Card className="relative w-full h-full pt-6">
        <CardContent>
          <NewOrder
            cart={cart}
            orderNumber={orderNumber}
            selectedMember={selectedMember}
            checkoutStatus={checkoutStatus}
            completingOrder={completingOrder}
            totalPoints={totalPoints}
            setShowModal={setShowModal}
            setModalConfig={setModalConfig}
            setShowCancelDialog={setShowCancelDialog}
            setShowHoldDialog={setShowHoldDialog}
            setShowReceiptModal={setShowReceiptModal}
            setCheckoutStatus={setCheckoutStatus}
            setOrderNumber={setOrderNumber}
            setSelectedMember={setSelectedMember}
            setCart={setCart}
          />
        </CardContent>
      </Card>

      <Dialog open={showDialog} onOpenChange={handleDialogVisibility}>
        <DialogContent
          className="h-full w-full max-w-6xl max-h-[84vh] flex flex-col"
          aria-describedby="checkout-dialog"
        >
          <Tabs
            value={activeTab}
            onValueChange={setActiveTab}
            className="w-full h-full max-h-[70vh]"
          >
            <DialogHeader className="w-full h-fit items-start">
              <DialogTitle>
                <TabsList>
                  <TabsTrigger value="new-order">New Order</TabsTrigger>
                  <TabsTrigger value="on-hold">On Hold Orders</TabsTrigger>
                  <TabsTrigger value="return-items">Return Items</TabsTrigger>
                </TabsList>
              </DialogTitle>
              <DialogDescription className="text-sm">
                Manage orders and returns for your members.
              </DialogDescription>
              <DialogClose className="absolute right-3 top-2">
                <X className="text-black" />
              </DialogClose>
            </DialogHeader>
            <div className="w-full h-full relative border-t border-t-muted">
              <TabsContent value="new-order" className="relative w-full h-full">
                <NewOrderCheckIn
                  handleDialogVisibility={handleDialogVisibility}
                />
              </TabsContent>
              <TabsContent value="on-hold" className="relative w-full h-full">
                <OnholdOrderContent
                  handleResumeOrder={handleResumeOrder}
                  heldOrders={heldOrderData}
                />
              </TabsContent>
              <TabsContent
                value="return-items"
                className="relative w-full h-full"
              >
                <ScrollArea className="max-h-[70vh] h-[70vh]">
                  <ReturnItemsContent />
                </ScrollArea>
              </TabsContent>
            </div>
          </Tabs>
        </DialogContent>
      </Dialog>

      <div className="relative h-full w-full">
        <div className="relative border-t border-t-muted z-50 p-6">
          <TabsContent value="new-order" className="relative w-full h-full">
            <ClientSearch isStaff={true} isModal={false} />
          </TabsContent>
          <TabsContent value="on-hold" className="relative w-full h-full">
            <OnholdOrderContent
              handleResumeOrder={handleResumeOrder}
              heldOrders={heldOrderData}
            />
          </TabsContent>
          <TabsContent value="return-items" className="relative w-full h-full">
            <ScrollArea className="max-h-[70vh] h-[70vh]">
              <ReturnItemsContent />
            </ScrollArea>
          </TabsContent>
        </div>

        <ReceiptModal
          isOpen={showReceiptModal}
          onClose={() => setShowReceiptModal(false)}
          orderItems={cart}
          orderNumber={orderNumber || ""}
          memberName={selectedMember?.first_name || "N/A"}
          totalPoints={totalPoints}
          onPrint={handlePrintReceipt}
          onComplete={handleCompleteTransaction}
          onCancel={() => setShowReceiptModal(false)}
          isCompletingOrder={completingOrder}
        />

        <HoldOrderDialog
          isOpen={showHoldDialog}
          onClose={() => setShowHoldDialog(false)}
          onConfirm={handleConfirmHold}
          memberName={selectedMember?.first_name || ""}
        />

        <CancelOrderDialog
          isOpen={showCancelDialog}
          onClose={() => setShowCancelDialog(false)}
          onConfirm={handleCancelOrder}
          memberName={selectedMember?.first_name || ""}
        />

        <ModalAlert
          isOpen={showModal}
          onClose={() => setShowModal(false)}
          title={modalConfig.title}
          message={modalConfig.message}
          type={modalConfig.type}
          confirmText={modalConfig.confirmText}
        />
      </div>
    </>
  );
}

function NewOrderCheckIn({
  handleDialogVisibility,
}: {
  handleDialogVisibility: (open: boolean) => void;
}) {
  const { user, token } = useAuthCheck();
  const {
    searchQuery,
    setSearchQuery,
    isSearching,
    isStaff,
    searchResults,
    fetchSearchResults,
  } = useClientSearch();
  const handleSearch = (query: string) => {
    setSearchQuery({ query, role: user?.user_metadata?.role });
  };
  const { setSelectedClient, selectedClient, refreshSelectedClient } =
    useSelectClient();

  const { showAlert } = useAlert();
  const [isLoading, setLoading] = useState(false);
  const { refreshData: refreshCheckedInToday } = useCheckedInToday();
  const { city, region, country, error } = useLocation();

  const clearSearch = () => {
    setSearchQuery({ query: "", role: user?.user_metadata?.role });
    setSelectedClient(null);
  };

  const handleSelectedClient = (client) => {
    setSelectedClient(client);
  };

  const handleCheckIn = async ({ forceCheckIn = false }) => {
    setLoading(true);
    if (selectedClient !== null) {
      const response = await fetch("/api/user-data/user-checkin", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({
          member_id: selectedClient?.id,
          checked_in_by: user?.id,
          is_forced_checked_in: forceCheckIn,
          added_store_location: !error
            ? `${city}, ${region}, ${country}`
            : error,
        }),
      });

      const result = await response.json();

      showAlert(result.message, response.ok ? "success" : "error");

      if (response.ok) {
        // Refresh search results to get updated checkout status
        const latestResults = await fetchSearchResults();
        await refreshSelectedClient();
        refreshCheckedInToday();

        // Update the selected client with the latest data that includes updated checkout status
        const updatedClient = latestResults.find(
          (client: { id: any }) => client.id === selectedClient.id
        );

        if (updatedClient) {
          setSelectedClient(updatedClient);
        }
      }

      setLoading(false);
    }
  };

  const checkIn = () => {
    handleCheckIn({ forceCheckIn: false })
      .then(async () => {
        // After successful check-in, refresh the main component's selected client data
        await refreshSelectedClient();
        handleDialogVisibility(false);
      })
      .catch((error) => {
        console.error("Error checking in:", error);
      });
  };

  return (
    <div className="relative w-full h-full flex flex-col justify-between">
      <div className="relative w-full h-full flex flex-col gap-4 items-start">
        <div className="w-full h-fit space-y-2">
          <h3 className="text-lg font-medium">Client Search</h3>
          <div className="relative">
            <Input
              type="text"
              placeholder="Search clients..."
              className="w-full pr-10 h-12 text-base shadow-sm focus-visible:ring-2 focus-visible:ring-blue-500"
              value={searchQuery.query}
              onChange={(e) => handleSearch(e.target.value)}
            />
            {searchQuery && (
              <button
                className="absolute right-3 top-1/2 -translate-y-1/2 hover:bg-gray-100 rounded-full p-1"
                onClick={clearSearch}
              >
                <X className="h-5 w-5 text-gray-600" />
              </button>
            )}
          </div>
          <p className="text-sm text-muted-foreground">
            Enter at least 3 characters of a client's address
          </p>
        </div>
        <div className="w-full h-full flex flex-col gap-2 items-start max-h-[50vh]">
          {isSearching ? (
            <div className="text-center py-8">
              <p>Searching...</p>
            </div>
          ) : null}

          <div className="relative w-full h-fit flex flex-col gap-2">
            <ScrollArea className="max-h-[50vh] h-[50vh]">
              {searchResults.map((result) => {
                return (
                  <>
                    {renderClientCard(
                      result,
                      selectedClient,
                      true,
                      handleSelectedClient
                    )}
                  </>
                );
              })}
            </ScrollArea>
          </div>
        </div>
      </div>
      <CustomButton
        disabled={!selectedClient || isLoading}
        onClick={checkIn}
        className="w-sm self-center"
      >
        {isLoading ? "Checking in..." : "Start"}
      </CustomButton>
    </div>
  );
}

function renderClientCard(
  client,
  selectedSearchClient,
  isSelectable = false,
  selectSearchClient: ((client: any) => void) | null = null
) {
  const clientActiveMembership = getCurrentActiveMembership(
    client?.memberships
  );

  const startDate =
    client?.memberships?.length > 0
      ? formatDate(
          client.memberships?.length > 1
            ? clientActiveMembership?.start_date
            : client.memberships[0]?.start_date
        )
      : "N/A";

  const endDate =
    client?.memberships?.length > 0
      ? formatDate(
          client.memberships?.length > 1
            ? clientActiveMembership?.end_date
            : client.memberships[0]?.end_date
        )
      : "N/A";

  const usedPoints =
    clientActiveMembership?.used_points ??
    client?.memberships?.[0]?.used_points ??
    0;

  const totalPoints =
    clientActiveMembership?.total_points ??
    client?.memberships?.[0]?.total_points ??
    0;

  const availablePoints =
    clientActiveMembership?.available_points ??
    client?.memberships?.[0]?.available_points ??
    0;

  const isSelected = selectedSearchClient?.id === client.id;

  return (
    <div
      className={`border-b mb-5 last:border-b-0 transition-colors ${
        isSelectable
          ? `cursor-pointer ${
              isSelected ? "bg-blue-50 border-blue-200" : "hover:bg-gray-100"
            }`
          : ""
      }`}
      onClick={() =>
        isSelectable && selectSearchClient && selectSearchClient(client)
      }
    >
      <div
        className={`text-white px-4 py-3 flex justify-between items-center ${
          isSelected ? "bg-blue-600" : "bg-gray-500"
        }`}
      >
        <p className="text-lg font-semibold">
          {client.first_name} {client.last_name}
        </p>
        {client?.additional_info?.preferred_language && (
          <span className="bg-blue-50 text-gray-800 text-xs font-medium px-2 py-1 rounded">
            {client.additional_info.preferred_language.charAt(0).toUpperCase() +
              client.additional_info.preferred_language.slice(1)}
          </span>
        )}
      </div>

      <div className="p-4 grid grid-cols-3 gap-4 text-sm">
        <div>
          <p className="font-semibold">
            Client #: <span className="font-normal">{client.id}</span>
          </p>
          <p>Phone 1: {client.contact_info.primary_number}</p>
          <p>{client.address}</p>
        </div>

        <div>
          <p className="font-semibold">Membership</p>
          <p>
            Start: {startDate}
            <br />
            End: {endDate}
            <br />
            Used {usedPoints} of {totalPoints} points
          </p>
          <div className="w-full mt-2 bg-gray-200 rounded-full h-1">
            <div
              className="bg-blue-500 h-1 rounded-full"
              style={{
                width: `${(usedPoints / totalPoints) * 100}%`,
              }}
            />
          </div>
        </div>

        <div>
          <p className="font-semibold">Authorized Shoppers</p>
          {Array.isArray(JSON.parse(client.authorized_shopper)) &&
            JSON.parse(client.authorized_shopper).filter(
              (shopper) => shopper.trim() !== ""
            ).length > 0 && (
              <p className="text-sm text-gray-600">
                {JSON.parse(client.authorized_shopper)
                  .filter((shopper) => shopper.trim() !== "")
                  .join(", ")}
              </p>
            )}
        </div>
      </div>

      {client.notes?.[0]?.description && (
        <div
          className={`rounded-lg p-2.5 ${getNoteStyles(
            client?.checkoutStatus
          )}`}
        >
          <div className="flex items-start gap-2">
            <div className="flex-1">
              <h4 className="text-xs font-medium text-amber-800">
                Important Note
              </h4>
              <p className="text-amber-700 text-sm">
                <div
                  dangerouslySetInnerHTML={{
                    __html: client.notes[0].description,
                  }}
                  className="prose max-w-none"
                />
              </p>
            </div>
            <div className="flex flex-col items-end text-xs">
              <span className="font-medium bg-amber-200 text-amber-800 px-1.5 py-0.5 rounded">
                {getInitials(client.notes[0].modified_by)}
              </span>
              <span className="text-amber-600 mt-0.5">
                {formatDate(client.notes[0].created_at)}
              </span>
            </div>
          </div>
        </div>
      )}

      <StatusBadge
        checkoutStatus={client?.checkoutStatus}
        hasAvailablePoints={availablePoints > 0}
      />
    </div>
  );
}

function StatusBadge({
  checkoutStatus,
  hasAvailablePoints,
}: {
  checkoutStatus?: CheckoutStatusType;
  hasAvailablePoints?: boolean;
}) {
  return getMembershipStatusMessage(checkoutStatus, hasAvailablePoints);
}

function NoClientFound({ searchQuery, filteredClients }) {
  return (
    <Fragment>
      {searchQuery?.length >= 3 && filteredClients.length === 0 && (
        <div className="text-center py-8">
          <Search className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-semibold text-gray-900">
            No clients found
          </h3>
          <p className="mt-1 text-sm text-gray-500">
            Try adjusting your search terms.
          </p>
        </div>
      )}
    </Fragment>
  );
}

function getMembershipStatusMessage(
  checkoutStatus?: string,
  hasAvailablePoints?: boolean
) {
  const statusConfig: Record<string, { bgColor: string; message: string }> = {
    "no-membership": { bgColor: "bg-gray-500", message: "No membership found" },
    expired: {
      bgColor: "bg-red-500",
      message: "Membership subscription expired",
    },
    inactive: {
      bgColor: "bg-orange-500",
      message: hasAvailablePoints
        ? "Membership inactive but has available points"
        : "Membership inactive - No available points",
    },
    "not-checked-in": {
      bgColor: "bg-blue-500",
      message: "Member has not checked in yet. Select to check in",
    },
    "has-checked-in": {
      bgColor: "bg-green-500",
      message: "Member has checked in and is ready to check out",
    },
    "already-checked-out": {
      bgColor: "bg-yellow-500",
      message:
        "Member has already checked out today. Members can only check out once per day",
    },
  };

  const { bgColor, message } = statusConfig[checkoutStatus || ""] || {
    bgColor: "bg-gray-500",
    message: "Membership status unknown",
  };

  return (
    <div
      className={`${bgColor} text-white text-center p-2 text-md font-semibold`}
    >
      {message}
    </div>
  );
}
