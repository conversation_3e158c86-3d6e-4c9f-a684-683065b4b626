import { Button } from "@stackshift-ui/button";
import { Container } from "@stackshift-ui/container";
import { Flex } from "@stackshift-ui/flex";
import { Heading } from "@stackshift-ui/heading";
import { Image } from "@stackshift-ui/image";
import { Section } from "@stackshift-ui/section";
import { Text } from "@stackshift-ui/text";
import React from "react";
import { ButtonProps, HeaderProps } from ".";
import { urlFor } from "lib/sanity";

export default function Header_J({
  mainImage,
  title,
  description,
  primaryButton,
  secondaryButton,
  bgImage,
}: HeaderProps) {
  return (
    <Section
      className="md:pt-10 pt-12 !px-0"
      style={{
        backgroundImage: `url(${urlFor(bgImage)})`,
        backgroundRepeat: "no-repeat",
        backgroundSize: "113%",
        backgroundPosition: "top",
      }}
    >
      <Container maxWidth={1280}>
        <Flex className="w-full flex items-center max-w-6xl mx-auto justify-end flex-col-reverse md:flex-row">
          <div className="w-full basis-3/5 p-10">
            <TitleAndDescription title={title} description={description} />
          </div>

          <div className="relative w-full basis-2/5">
            <div className="relative w-full flex !self-end !justify-self-end">
              <Image
                className="object-cover relative z-10 mx-auto md:w-[85%] lg:w-[80%] md:mx-0"
                src={`${mainImage?.image}`}
                width={350}
                height={300}
                alt={mainImage?.alt ?? "header-main-image"}
              />
            </div>
          </div>
        </Flex>
      </Container>
    </Section>
  );
}

function TitleAndDescription({
  title,
  description,
}: {
  title?: string;
  description?: string;
}) {
  return (
    <div className="pl-8">
      {title && (
        <Heading
          type="h2"
          weight="thin"
          className="mb-5 text-left !text-cfc-secondary text-6xl"
        >
          {title}
        </Heading>
      )}

      {description && (
        <Text muted className="my-6 text-2xl md:text-3xl font-thin">
          {description}
        </Text>
      )}
    </div>
  );
}

function Buttons({
  primaryButton,
  secondaryButton,
}: {
  primaryButton?: ButtonProps;
  secondaryButton?: ButtonProps;
}) {
  return (
    <Flex
      align="center"
      justify="center"
      gap={2}
      direction="col"
      className="lg:justify-start md:flex-row"
    >
      {primaryButton?.label && (
        <Button as="link" link={primaryButton} ariaLabel={primaryButton?.label}>
          {primaryButton?.label}
        </Button>
      )}

      {secondaryButton?.label && (
        <Button
          as="link"
          link={secondaryButton}
          className="bg-secondary hover:bg-secondary/50 inline-block rounded-global font-bold transition duration-200 px-3 py-4"
          ariaLabel={secondaryButton?.label}
        >
          {secondaryButton?.label}
        </Button>
      )}
    </Flex>
  );
}

interface MainImageProps {
  mainImage?: {
    image?: string | any;
    alt?: string;
  };
}

function MainImage({ mainImage }: MainImageProps) {
  if (!mainImage?.image) return null;

  return (
    <div className="relative w-full max-w-md mx-auto">
      <Image
        className=" object-cover w-full md:w-[90%] lg:w-[300px] relative z-10 md:scale-110"
        src={`${mainImage.image}`}
        sizes="(min-width: 520px) 520px, 90vw"
        width={800}
        height={500}
        alt={mainImage.alt ?? "header-main-image"}
      />
      {/* <DecorativeImages /> */}
    </div>
  );
}

function DecorativeImages() {
  return (
    <>
      <ImageElement
        src="/assets/elements/webriq-blue-dark-up.png"
        alt="webriq-blue-dark-up-mainImage-element"
        className="absolute hidden md:block"
        style={{ top: "-2rem", right: "3rem", zIndex: 2 }}
        width={112}
        height={112}
      />
      <ImageElement
        src="/assets/elements/wing-webriq-blue-down.png"
        alt="wing-webriq-blue-down-mainImage-element"
        className="absolute hidden md:block"
        style={{ bottom: "-2rem", right: "-2rem", zIndex: 2 }}
        width={144}
        height={144}
      />
      <ImageElement
        src="/assets/elements/bullets-gray-right.svg"
        alt="bullets-gray-right-mainImage-element"
        className="absolute hidden md:block"
        style={{ top: "3rem", right: "-3rem", zIndex: 2 }}
        width={115}
        height={157}
      />
      <ImageElement
        src="/assets/elements/bullets-gray-left.svg"
        alt="bullets-gray-left-mainImage-element"
        className="absolute hidden md:block"
        style={{ bottom: "2.5rem", left: "-4.5rem", zIndex: 2 }}
        width={157}
        height={115}
      />
    </>
  );
}

function ImageElement({
  src,
  alt,
  className,
  style,
  width,
  height,
}: {
  src: string;
  alt: string;
  className?: string;
  style?: React.CSSProperties;
  width: number;
  height: number;
}) {
  return (
    <Image
      src={src}
      alt={alt}
      className={className}
      style={style}
      width={width}
      height={height}
    />
  );
}

export { Header_J };
