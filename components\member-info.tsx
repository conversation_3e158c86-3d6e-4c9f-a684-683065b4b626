import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs";
import { DistributionDataProvider } from "context/useDistributionData";
import { HouseholdDataProvider } from "context/useHousehold";
import { useRouter } from "next/router";
import ClientSearch from "./client-search";
import ActivityManagementContent from "./dashboard/admin/activity-management-content";
import DistributionManagementContent from "./dashboard/admin/distribution-management-content";
import MemberManagement from "./dashboard/admin/member-management";
import ReceiptsManagementContent from "./dashboard/admin/receipts-management-content";
import { CheckoutContent } from "./dashboard/checkout/checkout-dialog";
import AccountHistory from "./dashboard/staff/account-history";
import AccountNotes from "./dashboard/staff/account-notes";
import MembershipContent from "./dashboard/staff/membership-content";
import OrderHistoryContent from "./dashboard/staff/order-history-content";
import { useAuthCheck } from "hooks/useA<PERSON><PERSON><PERSON><PERSON>";
import Link from "next/link";

export function MemberInfo() {
  const router = useRouter();
  const { user, loading } = useAuthCheck();
  const { tab, subtab } = router.query;

  if (loading) return null;

  const handleTabChange = (value: string) => {
    const newQuery: any = { ...router.query, tab: value };

    const noSubtab = ["receipts", "distribution", "activity"];
    if (noSubtab.includes(value)) {
      delete newQuery.subtab;
    }

    router.push(
      {
        pathname: router.pathname,
        query: newQuery,
      },
      undefined,
      { shallow: true }
    );
  };

  const handleSubTabChange = (value: string) => {
    const newQuery: any = { ...router.query };

    if (value) {
      newQuery.subtab = value;
    } else {
      delete newQuery.subtab;
    }

    router.push(
      {
        pathname: router.pathname,
        query: newQuery,
      },
      undefined,
      { shallow: true }
    );
  };

  const isVolunteer = user?.user_metadata?.role === "volunteer";

  return (
    <Tabs
      defaultValue={isVolunteer ? "check-in" : "membership-desk"}
      value={(tab as string) || (isVolunteer ? "check-in" : "membership-desk")}
      className="w-full"
      onValueChange={handleTabChange}
    >
      <TabsList
        className={`grid w-full ${
          isVolunteer ? "grid-cols-2" : "grid-cols-6"
        } h-12 bg-white border-b border-gray-200 rounded-none p-0 sticky top-0 z-10`}
      >
        {isVolunteer ? (
          <>
            <TabsTrigger value="check-in">Check-in</TabsTrigger>
            <TabsTrigger value="checkout" asChild>
              <Link
                href="/checkout"
                rel="noopener noreferrer"
                target="_self"
                className="w-full"
              >
                Checkout
              </Link>
            </TabsTrigger>
          </>
        ) : (
          <>
            <TabsTrigger value="membership-desk">Membership Desk</TabsTrigger>
            <TabsTrigger value="check-in">Check-in</TabsTrigger>
            <TabsTrigger value="checkout">
              {/* <Link
                href="/checkout"
                rel="noopener noreferrer"
                target="_self"
                className="w-full"
              >
                Checkout
              </Link> */}
              {/* <CheckoutContent /> */}
              Checkout
            </TabsTrigger>
            <TabsTrigger value="activity">Activity</TabsTrigger>
            <TabsTrigger value="distribution">Distribution</TabsTrigger>
            <TabsTrigger value="receipts">Receipts</TabsTrigger>
          </>
        )}
      </TabsList>

      {isVolunteer ? (
        <>
          <TabsContent value="check-in" className="mt-0">
            <div className="p-6">
              <ClientSearch
                isStaff={true}
                isModal={false}
                isCheckInPage={true}
              />
            </div>
          </TabsContent>
        </>
      ) : (
        <>
          <TabsContent value="membership-desk" className="mt-0">
            <Tabs
              defaultValue="account"
              value={(subtab as string) || "account"}
              className="w-full"
              onValueChange={handleSubTabChange}
            >
              <div className="bg-gray-50 border-b border-gray-200 py-3">
                <TabsList className="h-9 bg-white border border-gray-200 p-1 w-auto inline-flex rounded-lg shadow-sm">
                  <TabsTrigger
                    value="account"
                    className="h-7 px-3 text-sm font-medium rounded-md
                             data-[state=active]:bg-blue-600 data-[state=active]:text-white data-[state=active]:shadow-sm
                             text-gray-700 hover:text-gray-900 transition-colors"
                  >
                    Account
                  </TabsTrigger>
                  <TabsTrigger
                    value="memberships"
                    className="h-7 px-3 text-sm font-medium rounded-md
                             data-[state=active]:bg-blue-600 data-[state=active]:text-white data-[state=active]:shadow-sm
                             text-gray-700 hover:text-gray-900 transition-colors"
                  >
                    Memberships
                  </TabsTrigger>
                  <TabsTrigger
                    value="orders"
                    className="h-7 px-3 text-sm font-medium rounded-md
                             data-[state=active]:bg-blue-600 data-[state=active]:text-white data-[state=active]:shadow-sm
                             text-gray-700 hover:text-gray-900 transition-colors"
                  >
                    Orders
                  </TabsTrigger>
                  <TabsTrigger
                    value="notes"
                    className="h-7 px-3 text-sm font-medium rounded-md
                             data-[state=active]:bg-blue-600 data-[state=active]:text-white data-[state=active]:shadow-sm
                             text-gray-700 hover:text-gray-900 transition-colors"
                  >
                    Notes
                  </TabsTrigger>
                  <TabsTrigger
                    value="surveys"
                    className="h-7 px-3 text-sm font-medium rounded-md
                             data-[state=active]:bg-blue-600 data-[state=active]:text-white data-[state=active]:shadow-sm
                             text-gray-700 hover:text-gray-900 transition-colors"
                  >
                    Surveys
                  </TabsTrigger>
                  <TabsTrigger
                    value="history"
                    className="h-7 px-3 text-sm font-medium rounded-md
                             data-[state=active]:bg-blue-600 data-[state=active]:text-white data-[state=active]:shadow-sm
                             text-gray-700 hover:text-gray-900 transition-colors"
                  >
                    History
                  </TabsTrigger>
                </TabsList>
              </div>

              <TabsContent value="account" className="mt-0">
                <HouseholdDataProvider>
                  {/* <AccountContent /> */}
                  <MemberManagement />
                </HouseholdDataProvider>
              </TabsContent>
              <TabsContent value="memberships" className="mt-0">
                <MembershipContent />
              </TabsContent>
              <TabsContent value="orders" className="mt-0">
                <OrderHistoryContent />
              </TabsContent>
              <TabsContent value="notes" className="mt-0">
                <AccountNotes />
              </TabsContent>
              <TabsContent value="surveys" className="mt-0"></TabsContent>
              <TabsContent value="history" className="mt-0">
                <AccountHistory />
              </TabsContent>
            </Tabs>
          </TabsContent>

          <TabsContent value="check-in" className="mt-0">
            <div className="p-6">
              <ClientSearch
                isStaff={true}
                isModal={false}
                isCheckInPage={true}
              />
            </div>
          </TabsContent>

          <TabsContent value="checkout">
            <CheckoutContent />
          </TabsContent>

          <TabsContent value="activity" className="mt-0">
            <div className="p-6">
              <ActivityManagementContent />
            </div>
          </TabsContent>

          <TabsContent value="distribution" className="mt-0">
            <div className="p-6">
              <DistributionDataProvider>
                <DistributionManagementContent />
              </DistributionDataProvider>
            </div>
          </TabsContent>

          <TabsContent value="receipts" className="mt-0">
            <div className="p-6">
              <ReceiptsManagementContent />
            </div>
          </TabsContent>
        </>
      )}
    </Tabs>
  );
}
