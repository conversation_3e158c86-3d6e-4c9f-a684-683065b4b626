import { Button } from "@stackshift-ui/button";
import { Container } from "@stackshift-ui/container";
import { Flex } from "@stackshift-ui/flex";
import { Heading } from "@stackshift-ui/heading";
import { Image } from "@stackshift-ui/image";
import { Section } from "@stackshift-ui/section";
import { Text } from "@stackshift-ui/text";
import React from "react";
import { ButtonProps, HeaderProps } from ".";

import { PortableText } from "@portabletext/react";
import { PortableTextBlock } from "sanity";
import { MyPortableTextComponents } from "types";

export const defaultBlockStyle: MyPortableTextComponents = {
  block: {
    h1: ({ children }) => (
      <Heading
        fontSize="3xl"
        weight="bold"
        className="mb-8 leading-normal text-black dark:text-white"
      >
        {children}
      </Heading>
    ),
    h2: ({ children }) => (
      <Heading
        type="h2"
        weight="bold"
        fontSize="2xl"
        className="mb-8 text-black dark:text-white"
      >
        {children}
      </Heading>
    ),
    h3: ({ children }) => (
      <Heading
        type="h3"
        fontSize="xl"
        weight="bold"
        className="mb-8 leading-normal text-black dark:text-white"
      >
        {children}
      </Heading>
    ),
    h4: ({ children }) => (
      <Heading
        type="h4"
        weight="bold"
        fontSize="lg"
        className="mb-8 leading-normal text-black dark:text-white"
      >
        {children}
      </Heading>
    ),
    normal: ({ children }) => (
      <Text className="md:text-2xl text-xl leading-snug tracking-normal text-cfc-gray">
        {children}
      </Text>
    ),
    blockquote: ({ children }) => (
      <blockquote className="mb-6 italic leading-loose text-gray-500 px-14">
        - {children}
      </blockquote>
    ),
  },
  code: ({ value }) => (
    <pre data-language={value.language}>
      <code>{value.code}</code>
    </pre>
  ),
  list: {
    bullet: ({ children }) => (
      <ul className="flex flex-col pl-10 mb-8 space-y-4 leading-relaxed list-disc">
        {children}
      </ul>
    ),
    number: ({ children }) => (
      <ol className="flex flex-col pl-10 mb-8 space-y-4 leading-relaxed list-decimal">
        {children}
      </ol>
    ),
  },
  listItem: {
    bullet: ({ children }) => <li className="leading-relaxed">{children}</li>,
    number: ({ children }) => <li className="leading-relaxed">{children}</li>,
  },
  marks: {
    strong: ({ children }) => <strong>{children}</strong>,
    em: ({ children }) => <em>{children}</em>,
    code: ({ children }) => <code>{children}</code>,
    link: ({ children, value }) => (
      <a
        className="hover:text-primary-foreground text-primary"
        href={value.href}
        target="_blank"
        rel="noopener noreferrer"
      >
        {children}
      </a>
    ),
  },
  types: {
    addImage: ({ value }) => (
      <Image
        className="w-full h-full mb-5"
        width={500}
        height={500}
        sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
        src={urlFor(value?.image)}
        alt={value?.alt ?? value?.image?.asset?._ref}
      />
    ),
  },
};

export default function Header_K({
  mainImage,
  title,
  description,
  primaryButton,
  secondaryButton,
  bgImage,
  firstColumn,
}: HeaderProps) {
  return (
    <Section className="relative w-full !px-0 ">
      <Container maxWidth={1560} className="!px-0">
        <Flex className="w-full md:h-full relative items-center justify-center md:flex-none flex-col !px-0">
          <MainImage mainImage={mainImage} />
          <div className="md:absolute relative md:h-full w-full z-10 px-2">
            <TitleAndDescription
              title={title}
              description={description}
              firstColumn={firstColumn}
            />

            {/* <Flex wrap justify="center" className="mx-auto">
              {firstColumn && (
                <div className="mb-2 text-xs w-full text-center">
                  <PortableText
                    value={firstColumn}
                    components={defaultBlockStyle}
                    onMissingComponent={false} // Disabling warnings / handling unknown types
                  />
                </div>
              )}
            </Flex> */}
          </div>
        </Flex>
      </Container>
    </Section>
  );
}

function TitleAndDescription({
  title,
  description,
  firstColumn,
}: {
  title?: string;
  description?: string;
  firstColumn?: PortableTextBlock;
}) {
  return (
    <div className="w-full h-full flex items-center flex-col justify-end max-w-2xl mx-auto">
      {title && (
        <Heading
          type="h1"
          className="mb-8 font-bold font-heading md:font-extrabold leading-none text-center !text-cfc-gray md:text-4xl text-3xl"
        >
          {title}
        </Heading>
      )}

      <Flex wrap justify="center" className="mx-auto">
        {firstColumn && (
          <div className="mb-2 text-xs w-full text-center">
            <PortableText
              value={firstColumn}
              components={defaultBlockStyle}
              onMissingComponent={false} // Disabling warnings / handling unknown types
            />
          </div>
        )}
      </Flex>

      {/* {description && (
        <Text
          muted
          className="my-6 !md:text-3xl !text-2xl leading-tight font-extralight"
        >
          {description}
        </Text>
      )} */}
    </div>
  );
}

function Buttons({
  primaryButton,
  secondaryButton,
}: {
  primaryButton?: ButtonProps;
  secondaryButton?: ButtonProps;
}) {
  return (
    <Flex
      align="center"
      justify="center"
      gap={2}
      direction="col"
      className="lg:justify-start md:flex-row"
    >
      {primaryButton?.label && (
        <Button as="link" link={primaryButton} ariaLabel={primaryButton?.label}>
          {primaryButton?.label}
        </Button>
      )}

      {secondaryButton?.label && (
        <Button
          as="link"
          link={secondaryButton}
          className="bg-secondary hover:bg-secondary/50 inline-block rounded-global font-bold transition duration-200 px-3 py-4"
          ariaLabel={secondaryButton?.label}
        >
          {secondaryButton?.label}
        </Button>
      )}
    </Flex>
  );
}

interface MainImageProps {
  mainImage?: {
    image?: string | any;
    alt?: string;
  };
}

function MainImage({ mainImage }: MainImageProps) {
  if (!mainImage?.image) return null;

  return (
    <div className="relative w-full h-full">
      <Image
        className=" object-cover w-full relative"
        src={`${mainImage.image}`}
        width={1200}
        height={1000}
        alt={mainImage.alt ?? "header-main-image"}
      />
      {/* <DecorativeImages /> */}
    </div>
  );
}

function DecorativeImages() {
  return (
    <>
      <ImageElement
        src="/assets/elements/webriq-blue-dark-up.png"
        alt="webriq-blue-dark-up-mainImage-element"
        className="absolute hidden md:block"
        style={{ top: "-2rem", right: "3rem", zIndex: 2 }}
        width={112}
        height={112}
      />
      <ImageElement
        src="/assets/elements/wing-webriq-blue-down.png"
        alt="wing-webriq-blue-down-mainImage-element"
        className="absolute hidden md:block"
        style={{ bottom: "-2rem", right: "-2rem", zIndex: 2 }}
        width={144}
        height={144}
      />
      <ImageElement
        src="/assets/elements/bullets-gray-right.svg"
        alt="bullets-gray-right-mainImage-element"
        className="absolute hidden md:block"
        style={{ top: "3rem", right: "-3rem", zIndex: 2 }}
        width={115}
        height={157}
      />
      <ImageElement
        src="/assets/elements/bullets-gray-left.svg"
        alt="bullets-gray-left-mainImage-element"
        className="absolute hidden md:block"
        style={{ bottom: "2.5rem", left: "-4.5rem", zIndex: 2 }}
        width={157}
        height={115}
      />
    </>
  );
}

function ImageElement({
  src,
  alt,
  className,
  style,
  width,
  height,
}: {
  src: string;
  alt: string;
  className?: string;
  style?: React.CSSProperties;
  width: number;
  height: number;
}) {
  return (
    <Image
      src={src}
      alt={alt}
      className={className}
      style={style}
      width={width}
      height={height}
    />
  );
}

export { Header_K };
