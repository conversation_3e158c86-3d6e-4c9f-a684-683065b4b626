import React, { useState, useEffect } from "react";
import { Card } from "../../ui/card";
import { Label } from "../../ui/label";
import { Input } from "../../ui/input";
import { useSelectClient } from "context/useSelectClientContext";
import { formatDate } from "utils";
import { useAlert } from "context/useAlert";
import { useAuthCheck } from "hooks/useAuthCheck";
import ConfirmationModal from "../confirmation-modal";
import { DataTable } from "../../data-table";
import { Pencil, Plus, Trash2 } from "lucide-react";
import { CustomButton } from "@/components/ui/custom-button";
import { useClientSearch } from "context/useClientSearchContext";
import { useEditor, EditorContent } from "@tiptap/react";
import StarterKit from "@tiptap/starter-kit";
import Highlight from "@tiptap/extension-highlight";

export default function AccountNotes() {
  const [isPending, setIsPending] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [noteToDelete, setNoteToDelete] = useState<string | null>(null);
  const [noteToEdit, setNoteToEdit] = useState<any | null>(null);
  const [showForm, setShowForm] = useState(false);

  const { selectedClient, refreshSelectedClient } = useSelectClient();
  const { isSearching, fetchSearchResults } = useClientSearch();
  const { showAlert } = useAlert();
  const { user, token } = useAuthCheck();

  const editor = useEditor({
    extensions: [StarterKit, Highlight],
    content: noteToEdit?.description || "",
    editorProps: {
      attributes: {
        class:
          "prose prose-sm w-full min-h-[150px] p-3 border rounded-md focus:outline-none",
      },
    },
  });

  useEffect(() => {
    if (editor && noteToEdit?.description) {
      editor.commands.setContent(noteToEdit.description);
    }
  }, [noteToEdit, editor]);

  const handleCreateNotes = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    setIsPending(true);

    const formData = new FormData(event.currentTarget);
    const data = {
      ...Object.fromEntries(formData.entries()),
      description: editor?.getHTML() || "",
    };

    try {
      const endpoint = noteToEdit
        ? `/api/members-data/update-notes`
        : `/api/members-data/create-notes`;

      const response = await fetch(endpoint, {
        method: noteToEdit ? "PUT" : "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({
          ...data,
          id: noteToEdit?.id,
          member_id: selectedClient?.id,
          modified_by: user?.id,
        }),
      });

      const responseData = await response.json();

      if (!response.ok) {
        showAlert(responseData?.message, "error");
        throw new Error(responseData.message || "An error occurred");
      }

      await fetchSearchResults();
      await refreshSelectedClient();
      showAlert(
        `Note ${noteToEdit ? "updated" : "created"} successfully!`,
        "success"
      );
      setNoteToEdit(null);
      editor?.commands.setContent("");
    } catch (error) {
      showAlert(
        `Failed to ${noteToEdit ? "update" : "create"} note. Please try again.`,
        "error"
      );
    } finally {
      setIsPending(false);
      setShowForm(false);
      editor?.commands.setContent("");
    }
  };

  const handleDeleteNote = async (noteId: string) => {
    try {
      const response = await fetch(`/api/members-data/delete-notes`, {
        method: "DELETE",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({ id: noteId }),
      });

      if (response.ok) {
        showAlert("Note deleted successfully", "success");
        await fetchSearchResults();
        await refreshSelectedClient();
      } else {
        const errorData = await response.json();
        showAlert("Failed to delete note", "error");
        throw new Error(errorData.message || "Failed to delete note");
      }
    } catch (error) {
      console.error("Error deleting note:", error);
      showAlert("Error deleting note", "error");
      throw error;
    }
  };

  const confirmDeleteNote = (noteId: string) => {
    setNoteToDelete(noteId);
    setIsModalOpen(true);
  };

  const cancelDeleteNote = () => {
    setIsModalOpen(false);
    setNoteToDelete(null);
  };

  const confirmAndDeleteNote = () => {
    if (noteToDelete) {
      handleDeleteNote(noteToDelete);
    }
    cancelDeleteNote();
  };

  const handleEditClick = (note: any) => {
    setNoteToEdit(note);
    setShowForm(true);
  };

  const headers = ["Date", "Subject", "Note", "Modified By", "Actions"];
  const data = selectedClient?.notes || [];
  const renderRow = (note: any) => (
    <tr key={note.id} className="odd:bg-white even:bg-gray-50">
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
        {formatDate(note.created_at)}
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
        {note.subject}
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
        <div
          dangerouslySetInnerHTML={{ __html: note.description }}
          className="prose max-w-none"
        />
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
        {note.modified_by}
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
        <div className="flex gap-2 items-center justify-center">
          <CustomButton
            size="icon"
            variant="ghost"
            onClick={() => handleEditClick(note)}
          >
            <Pencil className="h-4 w-4" />
          </CustomButton>
          <CustomButton
            size="icon"
            variant="ghost"
            className="text-destructive"
            onClick={() => {
              confirmDeleteNote(note.id);
            }}
          >
            <Trash2 className="h-4 w-4" />
          </CustomButton>
        </div>
      </td>
    </tr>
  );

  const isDisabled = isSearching || isPending || !selectedClient;

  return (
    <Card className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-semibold">Account Notes</h2>
        <CustomButton
          variant="outline"
          disabled={isDisabled}
          onClick={() => {
            setShowForm(!showForm);
            setNoteToEdit(null);
          }}
        >
          <Plus className="h-4 w-4 mr-2" />
          Add note
        </CustomButton>
      </div>

      {showForm && (
        <div className="mb-6 p-6 border rounded-lg bg-gray-50">
          <h3 className="text-lg font-medium mb-4">
            {noteToEdit ? "Edit Note" : "Add New Note"}
          </h3>
          <form className="space-y-6" onSubmit={handleCreateNotes}>
            <div className="space-y-2">
              <Label className="text-sm font-medium">Subject</Label>
              <Input
                type="text"
                id="subject"
                name="subject"
                className="w-full"
                disabled={isDisabled}
                placeholder="Enter subject"
                defaultValue={noteToEdit?.subject || ""}
                required
              />
            </div>
            <div className="space-y-2">
              <Label className="text-sm font-medium">Note Type</Label>
              <select
                id="noteType"
                name="noteType"
                className="w-full p-2 border rounded-md"
                disabled={isDisabled}
                defaultValue={
                  noteToEdit?.noteType ||
                  (user?.user_metadata?.role === "volunteer"
                    ? "volunteer"
                    : "staff")
                }
                required
              >
                {user?.user_metadata?.role === "staff" ||
                user?.user_metadata?.role === "admin" ? (
                  <>
                    <option value="staff">Staff</option>
                    <option value="volunteer">Volunteer</option>
                  </>
                ) : (
                  <option value="volunteer">Volunteer</option>
                )}
              </select>
            </div>
            <div className="space-y-2">
              <Label className="text-sm font-medium">Note</Label>
              <div className="border rounded-md">
                <div className="border-b p-2 bg-gray-50 flex gap-2">
                  <CustomButton
                    type="button"
                    size="sm"
                    variant="ghost"
                    onClick={() => editor?.chain().focus().toggleBold().run()}
                    className={editor?.isActive("bold") ? "bg-gray-200" : ""}
                  >
                    B
                  </CustomButton>
                  <CustomButton
                    type="button"
                    size="sm"
                    variant="ghost"
                    onClick={() => editor?.chain().focus().toggleItalic().run()}
                    className={editor?.isActive("italic") ? "bg-gray-200" : ""}
                  >
                    I
                  </CustomButton>
                  <CustomButton
                    type="button"
                    size="sm"
                    variant="ghost"
                    onClick={() =>
                      editor?.chain().focus().toggleHighlight().run()
                    }
                    className={
                      editor?.isActive("highlight") ? "bg-gray-200" : ""
                    }
                  >
                    H
                  </CustomButton>
                </div>
                <EditorContent editor={editor} disabled={isDisabled} />
              </div>
            </div>
            <div className="flex justify-end space-x-2">
              <CustomButton
                type="button"
                variant="outline"
                onClick={() => {
                  setShowForm(false);
                  setNoteToEdit(null);
                }}
              >
                Cancel
              </CustomButton>
              <CustomButton type="submit" disabled={isDisabled}>
                {isPending
                  ? `${noteToEdit ? "Updating" : "Saving"} Note...`
                  : `${noteToEdit ? "Update" : "Save"} Note`}
              </CustomButton>
            </div>
          </form>
        </div>
      )}

      <div className="space-y-4">
        <div className="overflow-x-auto">
          <DataTable
            headers={headers}
            data={data}
            renderRow={renderRow}
            isLoading={isSearching}
          />
        </div>
      </div>

      <ConfirmationModal
        isOpen={isModalOpen}
        onConfirm={confirmAndDeleteNote}
        onCancel={cancelDeleteNote}
        message="Are you sure you want to delete this note?"
      />
    </Card>
  );
}
