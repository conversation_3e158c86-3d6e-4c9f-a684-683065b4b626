import React from "react";
import dynamic from "next/dynamic";
import {
  SectionsProps,
  ArrayOfImageTitleAndText,
  FeaturedItem,
  Images,
  LabeledRoute,
} from "../../../types";
import * as FeaturesVariants from "@stackshift-ui/features";

const Variants = {
  variant_a: FeaturesVariants.Features_A,
  variant_b: FeaturesVariants.Features_B,
  variant_c: FeaturesVariants.Features_C,
  variant_d: FeaturesVariants.Features_D,
  variant_e: FeaturesVariants.Features_E,
  variant_f: FeaturesVariants.Features_F,
  variant_g: FeaturesVariants.Features_G,
  variant_h: FeaturesVariants.Features_H,
  variant_i: dynamic(() => import("./variant_i")),
  variant_j: dynamic(() => import("./variant_j")),
  variant_k: dynamic(() => import("./variant_k")),
  variant_l: dynamic(() => import("./variant_l")),
  variant_m: dynamic(() => import("./variant_m")),
};

export interface FeaturesProps {
  caption?: string;
  title?: string;
  description?: string;
  features?: ArrayOfImageTitleAndText[];
  tags?: string[];
  featuredItems?: FeaturedItem[];
  images?: Images[];
  primaryButton?: LabeledRoute;
}

export interface FeaturesProps {
  caption?: string;
  title?: string;
  description?: string;
  features?: ArrayOfImageTitleAndText[];
  tags?: string[];
  featuredItems?: FeaturedItem[];
  images?: Images[];
  primaryButton?: LabeledRoute;
}

const displayName = "Features";

export const Features: React.FC<SectionsProps> = ({ data }) => {
  const variant = data?.variant;
  const Variant = variant && Variants[variant as keyof typeof Variants];

  const props = {
    caption: data?.variants?.caption ?? undefined,
    title: data?.variants?.title ?? undefined,
    description: data?.variants?.description ?? undefined,
    features: data?.variants?.arrayOfImageTitleAndText ?? undefined,
    tags: data?.variants?.tags ?? undefined,
    featuredItems: data?.variants?.featuredItems ?? undefined,
    images: data?.variants?.images ?? undefined,
    primaryButton: data?.variants?.primaryButton ?? undefined,
  };

  return Variant ? <Variant {...props} /> : null;
};

Features.displayName = displayName;
