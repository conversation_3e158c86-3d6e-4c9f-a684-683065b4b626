import dynamic from "next/dynamic";
import { SectionsProps, Logo, LabeledRoute, LabeledRouteWithKey, Form } from "types";

import * as CTAVariants from "@stackshift-ui/call-to-action";
const Variants = {
    variant_a: CTAVariants.CallToAction_A,
    variant_b: CTAVariants.CallToAction_B,
    variant_c: CTAVariants.CallToAction_C,
    variant_d: CTAVariants.CallToAction_D,
    variant_e: CTAVariants.CallToAction_E,
    variant_f: dynamic(() => import("./variant_f")),
    variant_g: dynamic(() => import("./variant_g")),
};

export interface CTAProps {
  logo?: Logo;
  title?: string;
  plainText?: string;
  button?: LabeledRoute;
  features?: string[];
  formLinks?: LabeledRouteWithKey[];
  form?: Form;
  signInLink?: LabeledRoute;
}

const displayName = "CallToAction";

export const CallToAction: React.FC<SectionsProps> = ({ data }) => {
  const variant = data?.variant;
  const Variant = variant && Variants[variant as keyof typeof Variants];

  const props = {
    logo: data?.variants?.logo ?? undefined,
    title: data?.variants?.title ?? undefined,
    plainText: data?.variants?.plainText ?? undefined,
    button: data?.variants?.primaryButton ?? undefined,
    features: data?.variants?.tags ?? undefined,
    formLinks: data?.variants?.formLinks ?? undefined,
    form: data?.variants?.form ?? undefined,
    signInLink: data?.variants?.signInLink ?? undefined,
  };

  return Variant ? <Variant {...props} /> : null;
};

CallToAction.displayName = displayName;
