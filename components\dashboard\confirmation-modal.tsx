import React from "react";
import { CustomButton } from "../ui/custom-button";

interface ConfirmationModalProps {
  isOpen: boolean;
  message: string;
  onConfirm: () => void;
  onCancel: () => void;
  isLoading?: boolean;
  isLoadingText?: string;
}

const ConfirmationModal: React.FC<ConfirmationModalProps> = ({
  isOpen,
  message,
  onConfirm,
  onCancel,
  isLoading = false,
  isLoadingText = "Deleting...",
}) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 flex items-center justify-center bg-gray-500 bg-opacity-50">
      <div className="bg-white p-6 rounded shadow-md w-96">
        <h3 className="text-lg font-semibold">{message}</h3>
        <div className="mt-4 flex justify-end gap-4">
          <CustomButton variant="outline" onClick={onCancel}>
            Cancel
          </CustomButton>
          <CustomButton
            disabled={isLoading}
            onClick={onConfirm}
            className="bg-red-500 text-white"
          >
            {isLoading ? isLoadingText : "Confirm"}
          </CustomButton>
        </div>
      </div>
    </div>
  );
};

export default ConfirmationModal;
