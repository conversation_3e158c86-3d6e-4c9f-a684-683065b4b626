import { Button } from "@stackshift-ui/button";
import { Container } from "@stackshift-ui/container";
import { Flex } from "@stackshift-ui/flex";
import { Heading } from "@stackshift-ui/heading";
import { Image } from "@stackshift-ui/image";
import { Section } from "@stackshift-ui/section";
import { Text } from "@stackshift-ui/text";
import React from "react";
import { ButtonProps, HeaderProps } from ".";
import { PortableText } from "@portabletext/react";
import { MyPortableTextComponents } from "types";
import { usePathname } from "next/navigation";

export const defaultBlockStyle: MyPortableTextComponents = {
  block: {
    h1: ({ children }) => (
      <Heading
        fontSize="3xl"
        weight="bold"
        className="mb-8 leading-normal text-black dark:text-white"
      >
        {children}
      </Heading>
    ),
    h2: ({ children }) => (
      <Heading
        type="h2"
        weight="bold"
        fontSize="2xl"
        className="mb-8 text-black dark:text-white"
      >
        {children}
      </Heading>
    ),
    h3: ({ children }) => (
      <Heading
        type="h3"
        weight="extrabold"
        className="mb-4 md:text-3xl text-2xl text-left leading-normal !text-cfc-gray dark:text-white"
      >
        {children}
      </Heading>
    ),
    h4: ({ children }) => (
      <Heading
        type="h4"
        weight="bold"
        fontSize="lg"
        className="mb-8 leading-normal text-black dark:text-white"
      >
        {children}
      </Heading>
    ),
    normal: ({ children }) => (
      <Text className="text-base text-center leading-normal tracking-wide text-gray-500">
        {children}
      </Text>
    ),

    blockquote: ({ children }) => (
      <blockquote className="mb-6 italic leading-loose text-gray-500 px-14">
        - {children}
      </blockquote>
    ),
  },
  code: ({ value }) => (
    <pre data-language={value.language}>
      <code>{value.code}</code>
    </pre>
  ),
  list: {
    bullet: ({ children }) => (
      <ul className="flex flex-col pl-10 mb-8 space-y-4 leading-relaxed list-disc">
        {children}
      </ul>
    ),
    number: ({ children }) => (
      <ol className="flex flex-col pl-10 mb-8 space-y-4 leading-relaxed list-decimal">
        {children}
      </ol>
    ),
  },
  listItem: {
    bullet: ({ children }) => <li className="leading-relaxed">{children}</li>,
    number: ({ children }) => <li className="leading-relaxed">{children}</li>,
  },
  marks: {
    strong: ({ children }) => <strong>{children}</strong>,
    em: ({ children }) => <em>{children}</em>,
    code: ({ children }) => <code>{children}</code>,
    link: ({ children, value }) => (
      <a
        className="!text-[#004e56] underline md:text-base text-sm"
        href={value.href}
        target="_blank"
        rel="noopener noreferrer"
      >
        {children}
      </a>
    ),
  },
  types: {
    addImage: ({ value }) => (
      <Image
        className="w-full h-full mb-5"
        width={500}
        height={500}
        sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
        src={urlFor(value?.image)}
        alt={value?.alt ?? value?.image?.asset?._ref}
      />
    ),
  },
};

export default function Header_Q({
  mainImage,
  title,
  description,
  primaryButton,
  secondaryButton,
  firstColumn,
}: HeaderProps) {
  const pathname = usePathname();
  return (
    <Section className="pb-20 relative">
      <Container maxWidth={1280}>
        <Flex align="center" direction="col" className="w-full">
          <MainImage mainImage={mainImage} />
          <Flex
            justify="center"
            className={`w-full flex flex-col max-w-5xl mx-auto mb-2`}
          >
            {primaryButton?.label && (
              <Button
                as="link"
                link={primaryButton}
                ariaLabel={primaryButton?.label}
                className="mx-auto py-4 text-cfc-secondary text-xl"
              >
                {primaryButton?.label}
              </Button>
            )}
            {firstColumn && (
              <div className="p-2 text-xs w-full">
                <PortableText
                  value={firstColumn}
                  components={defaultBlockStyle}
                  onMissingComponent={false} // Disabling warnings / handling unknown types
                />
              </div>
            )}
          </Flex>
        </Flex>
      </Container>
    </Section>
  );
}

interface MainImageProps {
  mainImage?: {
    image?: string | any;
    alt?: string;
  };
}

function MainImage({ mainImage }: MainImageProps) {
  if (!mainImage?.image) return null;

  return (
    <div className="relative w-full p-2 mx-auto">
      <Image
        className="object-cover relative z-10 md:mx-none mx-auto"
        src={`${mainImage.image}`}
        width={300}
        height={200}
        alt={mainImage.alt ?? "header-main-image"}
      />
    </div>
  );
}

export { Header_Q };
