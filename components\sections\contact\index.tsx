import React from "react";
import dynamic from "next/dynamic";
import { SectionsProps, SocialLink, Form } from "types";
import * as ContactVariants from "@stackshift-ui/contact";
import { PortableTextBlock } from "@portabletext/types";

const Variants = {
  variant_a: ContactVariants.Contact_A,
  variant_b: ContactVariants.Contact_B,
  variant_c: dynamic(() => import("./variant_c")),
};

export interface ContactProps {
  title?: string;
  contactDescription?: string;
  officeInformation?: string;
  firstColumn?: PortableTextBlock[],
  contactEmail?: string;
  contactNumber?: string;
  socialLinks?: SocialLink[];
  form?: Form;
  block?: any;
  blockText?: PortableTextBlock[];
}

const displayName = "Contact";

export const Contact: React.FC<SectionsProps> = ({ data }) => {
  const variant = data?.variant;
  const Variant = variant && Variants[variant as keyof typeof Variants];

  const props = {
    title: data?.variants?.title ?? undefined,
    contactDescription: data?.variants?.contactDescription ?? undefined,
    officeInformation: data?.variants?.officeInformation ?? undefined,
    contactEmail: data?.variants?.contactEmail ?? undefined,
    contactNumber: data?.variants?.contactNumber ?? undefined,
    socialLinks: data?.variants?.socialLinks ?? undefined,
    form: data?.variants?.form ?? undefined,
    block: data?.variants?.block ?? undefined,
    blockText: data?.variants?.blockText ?? undefined,
    firstColumn: data?.variants?.firstColumn ?? undefined,
  };

  return Variant ? <Variant {...props} /> : null;
};

Contact.displayName = displayName;
