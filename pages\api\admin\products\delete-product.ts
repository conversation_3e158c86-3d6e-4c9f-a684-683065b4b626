import { NextApiRequest, NextApiResponse } from "next";
import { supabaseClient } from "utils/supabase/client";

export default async function DELETE(
  req: NextApiRequest,
  res: NextApiResponse
) {
  try {
    const { productId, role } = req.body;

    if (role !== "admin") {
      return res.status(403).json({
        message: "Unauthorized. Only admin can delete product data.",
      });
    }

    const { error: productError } = await supabaseClient
      .from("products")
      .delete()
      .eq("id", productId);

    if (productError) {
      return res.status(404).json({
        message: "Product not found",
      });
    }

    return res.status(200).json({ message: "Product deleted successfully" });
  } catch (error) {
    return res
      .status(500)
      .json({ message: "Internal Server Error", error: error.message });
  }
}
