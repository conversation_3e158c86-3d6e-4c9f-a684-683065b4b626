import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>rigger,
  <PERSON>I<PERSON>,
  Select,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import React, { useEffect, useState } from "react";
import { useSelectClient } from "context/useSelectClientContext";
import <PERSON>Only<PERSON><PERSON> from "@/components/ui/read-only-field";
import { useClientSearch } from "context/useClientSearchContext";
import { Skeleton } from "@/components/ui/skeleton";
import { useRouter } from "next/router";

interface ContactInformationProps {
  isLoading: boolean;
  user?: any;
}

export default function ContactInformation({
  isLoading,
  user = null,
}: ContactInformationProps) {
  const { selectedClient } = useSelectClient();
  const { isSearching } = useClientSearch();
  const router = useRouter();

  const initialContactInfo =
    router.pathname === "/account-settings"
      ? JSON.parse(user?.contact_info || "{}")
      : selectedClient?.contact_info || {};

  const [contactInfo, setContactInfo] = useState(initialContactInfo);

  useEffect(() => {
    if (router.pathname === "/account-settings") {
      setContactInfo(JSON.parse(user?.contact_info || "{}"));
    } else if (selectedClient?.contact_info) {
      setContactInfo(selectedClient.contact_info);
    }
  }, [selectedClient, user, router.pathname]);

  const {
    primary_number,
    primary_phone_type,
    secondary_number,
    secondary_phone_type,
    alternate_contact_name,
  } = contactInfo;

  const isDisabled =
    isLoading || (!selectedClient && router.pathname !== "/account-settings");

  return (
    <React.Fragment>
      <div className="grid grid-cols-2 gap-4">
        <div>
          <Label htmlFor="primary_number">Primary Number</Label>
          {isSearching ? (
            <Skeleton className="w-full h-9" />
          ) : (
            <Input
              name="primary_number"
              value={primary_number || ""}
              onChange={(e) =>
                setContactInfo({
                  ...contactInfo,
                  primary_number: e.target.value,
                })
              }
              disabled={isDisabled}
            />
          )}
        </div>
        <div>
          <Label htmlFor="primary_phone_type">Primary Phone Type</Label>
          {isSearching ? (
            <Skeleton className="w-full h-9" />
          ) : (
            <Select
              name="primary_phone_type"
              value={primary_phone_type || ""}
              onValueChange={(value) =>
                setContactInfo({ ...contactInfo, primary_phone_type: value })
              }
              disabled={isDisabled}
            >
              <SelectTrigger disabled={isDisabled}>
                <SelectValue placeholder="Select type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="home">Home</SelectItem>
                <SelectItem value="mobile">Mobile</SelectItem>
                <SelectItem value="work">Work</SelectItem>
                <SelectItem value="other">Other</SelectItem>
              </SelectContent>
            </Select>
          )}
        </div>

        <div>
          <Label htmlFor="secondary_number">Secondary Number</Label>
          {isSearching ? (
            <Skeleton className="w-full h-9" />
          ) : (
            <Input
              name="secondary_number"
              value={secondary_number || ""}
              onChange={(e) =>
                setContactInfo({
                  ...contactInfo,
                  secondary_number: e.target.value,
                })
              }
              disabled={isDisabled}
            />
          )}
        </div>
        <div>
          <Label htmlFor="secondary_phone_type">Secondary Phone Type</Label>
          {isSearching ? (
            <Skeleton className="w-full h-9" />
          ) : (
            <Select
              name="secondary_phone_type"
              value={secondary_phone_type || ""}
              onValueChange={(value) =>
                setContactInfo({ ...contactInfo, secondary_phone_type: value })
              }
              disabled={isDisabled}
            >
              <SelectTrigger disabled={isDisabled}>
                <SelectValue placeholder="Select type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="home">Home</SelectItem>
                <SelectItem value="mobile">Mobile</SelectItem>
                <SelectItem value="work">Work</SelectItem>
                <SelectItem value="other">Other</SelectItem>
              </SelectContent>
            </Select>
          )}
        </div>
      </div>

      <div className="mt-4">
        <Label htmlFor="alternate_contact_name">
          Alternate Phone Contact Name
        </Label>
        {isSearching ? (
          <Skeleton className="w-full h-9" />
        ) : (
          <Input
            name="alternate_contact_name"
            value={alternate_contact_name || ""}
            onChange={(e) =>
              setContactInfo({
                ...contactInfo,
                alternate_contact_name: e.target.value,
              })
            }
            disabled={isDisabled}
          />
        )}
      </div>

      {selectedClient &&
        selectedClient.email &&
        router.pathname !== "/account-settings" && (
          <div className="mt-4">
            <ReadOnlyField
              label="Email Address"
              value={selectedClient.email}
              disabled={isDisabled}
              isLoading={isSearching}
            />
          </div>
        )}

      {router.pathname === "/account-settings" && user?.email && (
        <div className="mt-4">
          <ReadOnlyField
            label="Email Address"
            value={selectedClient?.email}
            disabled={isDisabled}
            isLoading={isSearching}
          />
        </div>
      )}
    </React.Fragment>
  );
}
