import React from "react";
import { Label } from "@/components/ui/label";
import ReadOnly<PERSON>ield from "@/components/ui/read-only-field";
import { useClientSearch } from "context/useClientSearchContext";
import { useHouseholdData } from "context/useHousehold";

export default function MemberAges() {
  const { getAgeGroups } = useHouseholdData();
  const { isSearching } = useClientSearch();

  const ageGroups = getAgeGroups();

  return (
    <div className="space-y-4">
      <div className="flex flex-wrap justify-evenly gap-4">
        {Object.entries(ageGroups).map(([group, count]) => (
          <ReadOnlyField
            key={group}
            label={group}
            value={count}
            isSmall={true}
            isLoading={isSearching}
          />
        ))}
      </div>
    </div>
  );
}
