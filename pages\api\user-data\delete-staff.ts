import { NextApiRequest, NextApiResponse } from "next";
import { supabaseAdmin } from "utils/supabase/admin";
import { supabaseClient } from "utils/supabase/client";

export default async function DELETE(
  req: NextApiRequest,
  res: NextApiResponse
) {
  try {
    const { id, userId, role, userAuthId } = req.body;

    if (!userId) {
      return res.status(404).json({
        message: "User not found",
      });
    }

    if (!userId || role !== "admin") {
      return res.status(403).json({
        message: "Unauthorized. Only admin can delete category data.",
      });
    }

    const { error: memberError } = await supabaseClient
      .from("members")
      .delete()
      .eq("id", id);

    if (memberError) {
      return res.status(404).json({
        message: "Member not found",
      });
    }

    const { error: userError } = await supabaseClient
      .from("users")
      .delete()
      .eq("id", userId);

    if (userError) {
      return res.status(404).json({
        message: "User not found",
      });
    }

    //Delete from supabase authentication
    const { data, error } = await supabaseAdmin.deleteUser(userAuthId);

    if (error) {
      return res.status(404).json({
        message: "User not found",
      });
    }

    return res.status(200).json({ message: "Staff data deleted successfully" });
  } catch (error) {
    return res
      .status(500)
      .json({ message: "Internal Server Error", error: error.message });
  }
}
