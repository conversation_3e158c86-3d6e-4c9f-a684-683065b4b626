import { MemberInfo } from "@/components/member-info";
import { <PERSON><PERSON> } from "@stackshift-ui/button";
import { RefreshCw, Search, UserPlus } from "lucide-react";
import ClientSearch from "../../client-search";
import { useState } from "react";
import Link from "next/link";
import { useClientSearch } from "context/useClientSearchContext";
import { useSelectClient } from "context/useSelectClientContext";

export default function StaffDashboardPage() {
  const [isClientSearchOpen, setIsClientSearchOpen] = useState(true);
  const { fetchSearchResults } = useClientSearch();
  const { refreshSelectedClient } = useSelectClient();

  const openClientSearch = () => {
    setIsClientSearchOpen(true);
  };

  const closeClientSearch = () => {
    setIsClientSearchOpen(false);
  };

  const handleRefreshData = async () => {
    await fetchSearchResults();
    await refreshSelectedClient();
  };

  return (
    <div className="min-h-screen bg-background">
      <div className="flex items-center gap-2 p-4 border-b">
        <Button
          variant="outline"
          className="gap-2 flex items-center"
          onClick={openClientSearch}
        >
          <Search className="h-4 w-4" />
          Client Search
        </Button>
        <Link href="/administrator?tab=members">
          <Button variant="outline" className="gap-2 flex items-center">
            <UserPlus className="h-4 w-4" />
            New Client
          </Button>
        </Link>
        <Button
          variant="outline"
          className="gap-2 flex items-center"
          onClick={handleRefreshData}
        >
          <RefreshCw className="h-4 w-4" />
          Refresh
        </Button>
      </div>
      <main className="p-4">
        <MemberInfo />
        {isClientSearchOpen && (
          <ClientSearch
            isStaff={true}
            isModal={true}
            onClose={closeClientSearch}
          />
        )}
      </main>
    </div>
  );
}
