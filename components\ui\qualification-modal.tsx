import {
  <PERSON><PERSON>,
  DialogContent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON>Title,
  <PERSON><PERSON>Footer,
} from "@/components/ui/dialog";
import { CustomButton } from "@/components/ui/custom-button";

interface QualificationModalProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  message?: string;
}

export function QualificationModal({
  isOpen,
  onClose,
  title = "Membership Not Available",
  message = "This client does not qualify for membership based on their household size and income.",
}: QualificationModalProps) {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
        </DialogHeader>
        <p className="text-gray-600">{message}</p>
        <DialogFooter>
          <CustomButton type="button" variant="outline" onClick={onClose}>
            Close
          </CustomButton>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
