import React, { useState } from "react";
import { Card } from "../../ui/card";
import { useSelectClient } from "context/useSelectClientContext";
import { formatDate } from "utils";
import { DataTable } from "../../data-table";
import MembershipFormModal from "./membership-form-modal";
import ConfirmationModal from "../confirmation-modal";
import { useAlert } from "context/useAlert";
import { useAuthCheck } from "hooks/useAuthCheck";
import { CustomButton } from "@/components/ui/custom-button";
import { Pencil, Plus, Trash2 } from "lucide-react";
import { useClientSearch } from "context/useClientSearchContext";
import { Tooltip } from "react-tooltip";

export default function MembershipContent() {
  const { selectedClient, refreshSelectedClient } = useSelectClient();
  const { fetchSearchResults, isSearching } = useClientSearch();
  const { showAlert } = useAlert();
  const { user, token } = useAuthCheck();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [membershipToDelete, setMembershipToDelete] = useState<{
    membershipId: string;
    paymentId: string;
  } | null>(null);
  const [editData, setEditData] = useState<any>(null);
  const [modalMode, setModalMode] = useState<"add" | "edit">("add");

  const data =
    selectedClient?.memberships.flatMap((membership) =>
      membership.payments.map((payment) => ({ membership, payment }))
    ) || [];

  const handleDeleteMembership = async ({ membershipId, paymentId }) => {
    try {
      const response = await fetch(`/api/members-data/delete-membership`, {
        method: "DELETE",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({
          membershipId,
          paymentId,
          userId: user?.id,
        }),
      });

      if (response.ok) {
        showAlert("Membership deleted successfully", "success");
      } else {
        const errorData = await response.json();
        showAlert("Failed to delete membership", "error");
        throw new Error(errorData.message || "Failed to delete membership");
      }
    } catch (error) {
      console.error("Error deleting membership:", error);
      showAlert("Error deleting membership", "error");
      throw error;
    } finally {
      await fetchSearchResults();
      await refreshSelectedClient();
    }
  };

  const handleEdit = (membership, payment) => {
    setEditData({
      membership_id: membership.id,
      start_date: membership.start_date,
      end_date: membership.end_date,
      payment_method: payment.method,
      amount: payment.amount.toString(),
      total_points: membership.total_points,
      used_points: membership.used_points,
      fee_waived: payment.fee_waived,
      credit_card_fee: payment.credit_card_fee,
      split_details: payment.split_details
        ? JSON.parse(payment.split_details)
        : null,
    });
    setModalMode("edit");
    setIsModalOpen(true);
  };

  const handleAdd = () => {
    setEditData(null);
    setModalMode("add");
    setIsModalOpen(true);
  };

  const headers = [
    "Start Date",
    "End Date",
    "Membership Points",
    "Used Points",
    "Available Points",
    "Amount Paid",
    "Payment Date",
    "Payment Method",
    "Added By",
    "Updated By",
    "Location",
    "Status",
    "Actions",
  ];

  const renderRow = ({ membership, payment }) => (
    <tr key={membership.id} className="odd:bg-white even:bg-gray-50">
      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
        {formatDate(membership.start_date)}
      </td>
      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
        {formatDate(membership.end_date)}
      </td>
      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
        {membership.total_points}
      </td>
      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
        {membership.used_points ?? 0}
      </td>
      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
        {membership.available_points}
      </td>
      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
        ${payment.amount}
      </td>
      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
        {formatDate(payment.date)}
      </td>
      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
        {payment.method.charAt(0).toUpperCase() + payment.method.slice(1)}
      </td>
      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
        {membership.added_by}
      </td>
      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
        {membership.updated_by}
      </td>
      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
        {membership.updated_store_location ?? membership.added_store_location}
      </td>
      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
        <span
          className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
            membership.status === "active"
              ? "bg-green-100 text-green-800"
              : membership.status === "inactive"
              ? "bg-orange-100 text-orange-800"
              : membership.status === "expired"
              ? "bg-red-100 text-red-800"
              : "bg-gray-100 text-gray-800"
          }`}
        >
          {membership?.status?.charAt(0)?.toUpperCase() +
            membership?.status?.slice(1)}
        </span>
      </td>
      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
        <div className="flex gap-2 items-center justify-center">
          <CustomButton
            size="icon"
            variant="ghost"
            disabled={
              membership.status === "expired" &&
              user?.user_metadata?.role !== "admin"
            }
            onClick={() => handleEdit(membership, payment)}
          >
            <Pencil className="h-4 w-4" />
          </CustomButton>
          <span
            data-tooltip-id="delete-membership-tooltip"
            data-tooltip-content={
              !user
                ? "You must be logged in to delete memberships"
                : membership.used_points && membership.used_points > 0
                ? "Cannot delete membership with used points"
                : "Delete membership"
            }
          >
            <CustomButton
              size="icon"
              variant="ghost"
              className="text-destructive"
              disabled={
                !user || (membership.used_points && membership.used_points > 0)
              }
              onClick={() =>
                confirmDeleteMembership({
                  membershipId: membership.id,
                  paymentId: payment.id,
                })
              }
            >
              <Trash2 className="h-4 w-4" />
            </CustomButton>
          </span>
          <Tooltip id="delete-membership-tooltip" />
        </div>
      </td>
    </tr>
  );

  const cancelDeleteMembership = () => {
    setIsDeleteModalOpen(false);
    setMembershipToDelete(null);
  };

  const confirmDeleteMembership = (params: {
    membershipId: string;
    paymentId: string;
  }) => {
    setMembershipToDelete(params);
    setIsDeleteModalOpen(true);
  };

  const confirmAndDeleteMembership = () => {
    if (membershipToDelete) {
      handleDeleteMembership(membershipToDelete);
    }
    cancelDeleteMembership();
  };

  return (
    <Card className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-semibold">Membership History</h2>
        <CustomButton
          disabled={!selectedClient}
          variant="outline"
          onClick={handleAdd}
        >
          <Plus className="h-4 w-4 mr-2" />
          Add Membership
        </CustomButton>
      </div>

      <div className="overflow-x-auto">
        <DataTable
          headers={headers}
          data={data}
          renderRow={renderRow}
          isLoading={isSearching}
        />
      </div>

      <MembershipFormModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        mode={modalMode}
        initialData={editData}
      />

      <ConfirmationModal
        isOpen={isDeleteModalOpen}
        onConfirm={confirmAndDeleteMembership}
        onCancel={cancelDeleteMembership}
        message="Are you sure you want to delete this membership?"
      />
    </Card>
  );
}
