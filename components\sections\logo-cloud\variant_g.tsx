import { Container } from "@stackshift-ui/container";
import { Flex } from "@stackshift-ui/flex";
import { Heading } from "@stackshift-ui/heading";
import { Image } from "@stackshift-ui/image";
import { Section } from "@stackshift-ui/section";
import React from "react";
import { LogoCloudProps } from ".";
import { Images, MyPortableTextComponents } from "types";
import { PortableText } from "@portabletext/react";
import { urlFor } from "lib/sanity";

// block styling as props to `components` of the PortableText component
export const textComponentBlockStyling: MyPortableTextComponents = {
  block: {
    h1: ({ children }) => (
      <Heading
        weight="extrabold"
        className="mb-2 md:text-5xl leading-normal !text-cfc-secondary dark:text-white"
      >
        {children}
      </Heading>
    ),
    h2: ({ children }) => (
      <Heading
        type="h2"
        weight="bold"
        fontSize="2xl"
        className="mb-8 text-black dark:text-white"
      >
        {children}
      </Heading>
    ),
    h3: ({ children }) => (
      <Heading
        type="h3"
        fontSize="xl"
        className="mb-8 leading-normal font-extrabold !text-cfc-gray dark:text-white"
      >
        {children}
      </Heading>
    ),
    h4: ({ children }) => (
      <Heading
        type="h4"
        weight="bold"
        fontSize="lg"
        className="mb-8 leading-normal text-black dark:text-white"
      >
        {children}
      </Heading>
    ),
    normal: ({ children }) => (
      <p className="mb-8 leading-relaxed md:text-base text-cfc-gray text-base">
        {children}
      </p>
    ),

    blockquote: ({ children }) => (
      <blockquote className="mb-6 italic leading-loose text-gray-500 px-14">
        - {children}
      </blockquote>
    ),
  },
  code: ({ value }) => (
    <pre data-language={value.language}>
      <code>{value.code}</code>
    </pre>
  ),
  list: {
    bullet: ({ children }) => (
      <ul className="flex flex-col pl-10 mb-8 space-y-4 leading-relaxed list-disc">
        {children}
      </ul>
    ),
    number: ({ children }) => (
      <ol className="flex flex-col pl-10 mb-8 space-y-4 leading-relaxed list-decimal">
        {children}
      </ol>
    ),
  },
  listItem: {
    bullet: ({ children }) => <li className="leading-relaxed">{children}</li>,
    number: ({ children }) => <li className="leading-relaxed">{children}</li>,
  },
  marks: {
    strong: ({ children }) => <strong>{children}</strong>,
    em: ({ children }) => <em>{children}</em>,
    code: ({ children }) => <code>{children}</code>,
    link: ({ children, value }) => (
      <a
        className="hover:text-primary-foreground text-primary"
        href={value.href}
        target="_blank"
        rel="noopener noreferrer"
      >
        {children}
      </a>
    ),
  },

  types: {
    addImage: ({ value }) => (
      <Image
        className="w-full h-full mb-5"
        width={500}
        height={500}
        sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
        src={urlFor(value?.image)}
        alt={value?.alt ?? value?.image?.asset?._ref}
      />
    ),

    addIframe: ({ value }) => {
      return (
        <div className="relative flex">
          <iframe
            loading="lazy"
            frameBorder="0"
            src={value?.iframe}
            width="100%"
            data-rocket-lazyload="fitvidscompatible"
            className="entered w-full h-[85rem]"
          />
        </div>
      );
    },
  },
};

export default function LogoCloud_G({
  title,
  images,
  firstColumn,
}: LogoCloudProps) {
  return (
    <Section className="pt-20 bg-white">
      <Container maxWidth={1280}>
        <Flex wrap justify="center" className="mx-auto mb-8">
          {firstColumn && (
            <div className="mb-2 text-xs w-full md:max-w-3xl text-center overflow-visible">
              <PortableText
                value={firstColumn}
                components={textComponentBlockStyling}
                onMissingComponent={false} // Disabling warnings / handling unknown types
              />
            </div>
          )}
        </Flex>

        <Title title={title} />
        <Flex wrap justify="center" align="center" className="md:w-10/12 w-full mx-auto">
          <LogoCloudImages images={images} />
        </Flex>
      </Container>
    </Section>
  );
}

function Title({ title }: { title?: string }) {
  if (!title) return null;

  return (
    <Heading
      type="h2"
      weight="extrabold"
      className="mb-5 md:text-5xl text-4xl text-center !text-cfc-secondary"
    >
      {title}
    </Heading>
  );
}

function LogoCloudImages({ images }: { images?: Images[] }) {
  if (!images) return null;

  console.log(images);

  return (
    <React.Fragment>
      {images?.slice(0, 2).map((image, index) => (
        <div className="w-full px-2 mb-4" key={index}>
          {image?.image ? (
            <Flex
              align="center"
              justify="center"
              className="w-full rounded-global bg-white"
            >
              <Image
                className="object-scale-down"
                src={`${image?.image}`}
                width={800}
                height={200}
                alt={image?.alt ?? `logoCloud-image${index}`}
              />
            </Flex>
          ) : null}
        </div>
      ))}

      <div className="w-full flex md:flex-row flex-col">
        {images?.slice(2, 4).map((image, index) => (
          <div className="w-full px-2 mb-4" key={index}>
            {image?.image ? (
              <Flex
                align="center"
                justify="center"
                className="w-full rounded-global bg-white"
              >
                <Image
                  className="object-scale-down"
                  src={`${image?.image}`}
                  width={800}
                  height={200}
                  alt={image?.alt ?? `logoCloud-image${index}`}
                />
              </Flex>
            ) : null}
          </div>
        ))}
      </div>
    </React.Fragment>
  );
}

export { LogoCloud_G };
