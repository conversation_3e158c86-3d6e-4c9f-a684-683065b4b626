import React from "react";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "../../ui/dialog";
import { CustomButton } from "../../ui/custom-button";

interface CancelHeldOrderDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  memberName: string;
}

export function CancelHeldOrderDialog({
  isOpen,
  onClose,
  onConfirm,
  memberName,
}: CancelHeldOrderDialogProps) {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Cancel Held Order</DialogTitle>
          <DialogDescription>
            Are you sure you want to cancel {memberName}'s held order? This
            action cannot be undone.
          </DialogDescription>
        </DialogHeader>
        <DialogFooter className="flex gap-2">
          <CustomButton variant="outline" onClick={onClose}>
            No, Keep Order
          </CustomButton>
          <CustomButton
            variant="destructive"
            onClick={() => {
              onConfirm();
              onClose();
            }}
          >
            Yes, Cancel Order
          </CustomButton>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
