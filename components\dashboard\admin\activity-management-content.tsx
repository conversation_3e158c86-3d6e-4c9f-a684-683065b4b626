import React, { useEffect, useState } from "react";
import { CustomButton } from "@/components/ui/custom-button";
import { Skeleton } from "@/components/ui/skeleton";
import { DateRangeFilter } from "@/components/ui/date-range-filter";
import axios from "axios";
import { startOfYear } from "date-fns";
import { useAuthCheck } from "hooks/useAuthCheck";

export default function ActivityManagementContent() {
  const [dateRange, setDateRange] = useState<{
    fromDate: Date;
    toDate: Date;
  }>({
    fromDate: new Date(),
    toDate: new Date(),
  });
  const { token } = useAuthCheck();

  const [activitySummaryData, setActivitySummaryData] = useState<any>([]);
  const [fetchingActivitySummaryData, setFetchingActivitySummaryData] =
    useState<boolean>(false);

  useEffect(() => {
    if (dateRange?.fromDate && dateRange?.toDate) {
      fetchData();
    }
  }, [dateRange]);

  const fetchData = async () => {
    try {
      setFetchingActivitySummaryData(true);
      const response = await axios.get("/api/admin/activity-summary", {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        params: {
          dateRange: JSON.stringify(dateRange),
        },
      });
      setActivitySummaryData(response.data);
    } catch (error) {
      console.error("Error fetching activity summary data:", error);
    } finally {
      setFetchingActivitySummaryData(false);
    }
  };

  const formatHour = (hour) => {
    const date = new Date();
    date.setUTCHours(hour, 0, 0, 0);

    return date.toLocaleTimeString("en-US", {
      hour: "numeric",
      hour12: true,
    });
  };

  const formatTrafficDataForDisplay = (trafficPerHour) => {
    if (
      !trafficPerHour ||
      !Array.isArray(trafficPerHour) ||
      trafficPerHour.length === 0
    ) {
      return [];
    }

    return trafficPerHour.map((data) => ({
      timeRange: `${formatHour(data.hour)} - ${formatHour(
        (data.hour + 1) % 24
      )}`,
      checkin_count: data.checkin_count,
    }));
  };

  const trafficDataForDisplay = formatTrafficDataForDisplay(
    activitySummaryData?.checkins?.trafficPerHour
  );

  const handleExportReport = () => {
    const startDate = dateRange.fromDate.toLocaleDateString();
    const endDate = dateRange.toDate.toLocaleDateString();

    // Extract payment data with cleaner structure
    const cashAmount =
      activitySummaryData?.payments?.byMethod?.cash?.amount || 0;
    const creditAmount =
      activitySummaryData?.payments?.byMethod?.credit?.amount || 0;
    const otherAmount =
      activitySummaryData?.payments?.byMethod?.other?.amount || 0;
    const cashCount = activitySummaryData?.payments?.byMethod?.cash?.count || 0;
    const creditCount =
      activitySummaryData?.payments?.byMethod?.credit?.count || 0;
    const splitCount =
      activitySummaryData?.payments?.byMethod?.split?.count || 0;
    const splitcashPayment =
      activitySummaryData?.payments?.byMethod?.split?.cashPayment || 0;
    const splitCreditComponent =
      activitySummaryData?.payments?.byMethod?.split?.creditComponent || 0;

    const csvContent = `Activity Summary

Start Date: ${startDate}
End Date: ${endDate}

Month,Unduplicated Consumer Count,Order Count,Item Count,Total Points,Total Amount,Cash Amount,Cash Transactions,Credit Amount,Credit Transactions,Split Payments,$11.45 Credits,$13.45 Credits,$15.45 Credits,Other Credits
${dateRange.fromDate.toLocaleDateString("en-US", {
  month: "short",
  year: "numeric",
})},${
      activitySummaryData?.members?.unduplicatedConsumerCount ||
      activitySummaryData?.members?.count ||
      0
    },${activitySummaryData?.orders?.completed || 0},${
      activitySummaryData?.orders?.distributed || 0
    },${
      activitySummaryData?.orders?.totalPointsUsed ||
      activitySummaryData?.memberships?.totalPoints ||
      0
    },$${(activitySummaryData?.payments?.totalAmount || 0).toFixed(
      2
    )},$${cashAmount.toFixed(2)},${cashCount},$${creditAmount.toFixed(
      2
    )},${creditCount},${splitCount},${
      activitySummaryData?.payments?.byMethod?.credit?.breakdown?.["11.45"] || 0
    },${
      activitySummaryData?.payments?.byMethod?.credit?.breakdown?.["13.45"] || 0
    },${
      activitySummaryData?.payments?.byMethod?.credit?.breakdown?.["15.45"] || 0
    },${activitySummaryData?.payments?.byMethod?.credit?.breakdown?.other || 0}
Total: NA,${
      activitySummaryData?.members?.unduplicatedConsumerCount ||
      activitySummaryData?.members?.count ||
      0
    },${activitySummaryData?.orders?.completed || 0},${
      activitySummaryData?.orders?.distributed || 0
    },${
      activitySummaryData?.orders?.totalPointsUsed ||
      activitySummaryData?.memberships?.totalPoints ||
      0
    },$${(activitySummaryData?.payments?.totalAmount || 0).toFixed(
      2
    )},$${cashAmount.toFixed(2)},${cashCount},$${creditAmount.toFixed(
      2
    )},${creditCount},${splitCount},${
      activitySummaryData?.payments?.byMethod?.credit?.breakdown?.["11.45"] || 0
    },${
      activitySummaryData?.payments?.byMethod?.credit?.breakdown?.["13.45"] || 0
    },${
      activitySummaryData?.payments?.byMethod?.credit?.breakdown?.["15.45"] || 0
    },${activitySummaryData?.payments?.byMethod?.credit?.breakdown?.other || 0}

Daily Deposit Summary
Payment Type,Amount,Transaction Count
Cash Total,${cashAmount.toFixed(2)},${cashCount}
Credit Card Total,${creditAmount.toFixed(2)},${creditCount}
Split Payment Cash Component,${splitcashPayment.toFixed(2)},
Split Payment Credit Component,${splitCreditComponent.toFixed(2)},

Credit Card Breakdown for Deposits
$11.45 Transactions,${
      activitySummaryData?.payments?.byMethod?.credit?.breakdown?.["11.45"] || 0
    }
$13.45 Transactions,${
      activitySummaryData?.payments?.byMethod?.credit?.breakdown?.["13.45"] || 0
    }
$15.45 Transactions,${
      activitySummaryData?.payments?.byMethod?.credit?.breakdown?.["15.45"] || 0
    }
Other Amount Transactions,${
      activitySummaryData?.payments?.byMethod?.credit?.breakdown?.other || 0
    }`;

    // Create blob and download
    const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
    const link = document.createElement("a");
    const url = URL.createObjectURL(blob);
    link.setAttribute("href", url);
    link.setAttribute(
      "download",
      `activity_summary_${dateRange.fromDate.toISOString().split("T")[0]}_to_${
        dateRange.toDate.toISOString().split("T")[0]
      }.csv`
    );
    link.style.visibility = "hidden";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const activityData = activitySummaryData;

  console.log("🚀 ~ activityData", activityData);

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-semibold">Activity Summary</h2>
        <div className="flex gap-4">
          <DateRangeFilter
            onDateRangeChange={(dateRange) =>
              setDateRange({
                fromDate: dateRange.from ?? new Date(2020, 0, 1),
                toDate: dateRange.to ?? new Date(),
              })
            }
          />
          <CustomButton variant="outline" onClick={handleExportReport}>
            Export Report
          </CustomButton>
        </div>
      </div>

      <div className="grid grid-cols-5 gap-4 mb-8">
        {fetchingActivitySummaryData ? (
          Array(5)
            .fill(0)
            .map((_, index) => (
              <Skeleton key={index} className="h-32 w-full rounded-lg" />
            ))
        ) : (
          <>
            <div className="p-4 border rounded-lg bg-white">
              <div className="flex justify-between items-start mb-2">
                <div className="text-sm text-gray-500">Total Members</div>
              </div>
              <div className="text-2xl font-semibold">
                {activityData?.members?.count}
              </div>
              <div className="text-sm text-gray-500 mt-1">
                Total members created in the system
              </div>
            </div>

            <div className="p-4 border rounded-lg bg-white">
              <div className="flex justify-between items-start mb-2">
                <div className="text-sm text-gray-500">Completed Orders</div>
              </div>
              <div className="text-2xl font-semibold">
                {activityData?.orders?.completed ?? 0}
              </div>
              <div className="text-sm text-gray-500 mt-1">
                {activityData?.orders?.onHold} pending order
              </div>
              <div className="text-sm text-gray-500 mt-1">
                {activityData?.checkins?.today} check-ins today
              </div>
            </div>

            <div className="p-4 border rounded-lg bg-white">
              <div className="flex justify-between items-start mb-2">
                <div className="text-sm text-gray-500">
                  Average Shopping Time
                </div>
              </div>
              <div className="text-2xl font-semibold">
                {activityData?.checkins?.averageShoppingDuration} min
              </div>
              <div className="text-sm text-gray-500 mt-1">
                Duration per visit in minutes
              </div>
            </div>

            <div className="p-4 border rounded-lg bg-white">
              <div className="flex justify-between items-start mb-2">
                <div className="text-sm text-gray-500">Total Revenue</div>
              </div>
              <div className="text-2xl font-semibold">
                ${(activityData?.payments?.totalAmount || 0).toFixed(2)}
              </div>
              <div className="text-sm text-gray-500 mt-1">
                Total revenue from payments
              </div>
            </div>

            <div className="p-4 border rounded-lg bg-white">
              <div className="flex justify-between items-start mb-2">
                <div className="text-sm text-gray-500">Points Redeemed</div>
              </div>
              <div className="text-2xl font-semibold">
                {activityData?.orders?.totalPointsUsed ||
                  activityData?.memberships?.totalPoints ||
                  0}
              </div>
              <div className="text-sm text-gray-500 mt-1">
                Total points used in orders
              </div>
            </div>
          </>
        )}
      </div>

      <div className="grid grid-cols-3 gap-6">
        <ConsumerActivity
          activityData={activityData}
          fetchingActivitySummaryData={fetchingActivitySummaryData}
        />

        <PaymentSummary
          activityData={activityData}
          fetchingActivitySummaryData={fetchingActivitySummaryData}
        />

        <TrafficSummary
          trafficDataForDisplay={trafficDataForDisplay}
          fetchingActivitySummaryData={fetchingActivitySummaryData}
        />
      </div>
    </div>
  );
}

function ConsumerActivity({ activityData, fetchingActivitySummaryData }) {
  return (
    <div className="border rounded-lg p-6 bg-white">
      <h3 className="font-medium mb-4">Consumer Activity</h3>
      <div className="space-y-4">
        <div className="flex justify-between items-center pb-2 border-b">
          <div>
            <div className="font-medium">Unduplicated Consumer Count</div>
            <div className="text-sm text-gray-500">
              How many accounts paid for a membership, checked in, or checked
              out in this date range
            </div>
          </div>
          {fetchingActivitySummaryData ? (
            <Skeleton className="w-24 h-6" />
          ) : (
            <div className="text-xl font-semibold">
              {activityData?.members?.unduplicatedConsumerCount}
            </div>
          )}
        </div>
        <div className="flex justify-between items-center pb-2 border-b">
          <div>
            <div className="font-medium">Order Count</div>
            <div className="text-sm text-gray-500">
              The number of orders completed
            </div>
          </div>
          {fetchingActivitySummaryData ? (
            <Skeleton className="w-24 h-6" />
          ) : (
            <div className="text-xl font-semibold">
              {activityData?.orders?.completed ?? 0}
            </div>
          )}
        </div>
        <div className="flex justify-between items-center pb-2 border-b">
          <div>
            <div className="font-medium">Item Count</div>
            <div className="text-sm text-gray-500">
              The number of items distributed
            </div>
          </div>
          {fetchingActivitySummaryData ? (
            <Skeleton className="w-24 h-6" />
          ) : (
            <div className="text-xl font-semibold">
              {activityData?.orders?.distributed ?? 0}
            </div>
          )}
        </div>

        <div className="flex justify-between items-center pb-2 border-b">
          <div>
            <div className="font-medium">Total Points Used</div>
            <div className="text-sm text-gray-500">
              Total number of points used in orders
            </div>
          </div>
          {fetchingActivitySummaryData ? (
            <Skeleton className="w-24 h-6" />
          ) : (
            <div className="text-xl font-semibold">
              {activityData?.orders?.totalPointsUsed ?? 0}
            </div>
          )}
        </div>

        <div className="flex justify-between items-center">
          <div>
            <div className="font-medium">Available Points Balance</div>
            <div className="text-sm text-gray-500">
              Total available points in system
            </div>
          </div>
          {fetchingActivitySummaryData ? (
            <Skeleton className="w-24 h-6" />
          ) : (
            <div className="text-xl font-semibold">
              {activityData?.memberships?.availablePoints}
            </div>
          )}
        </div>
        <div className="flex justify-between items-center">
          <div>
            <div className="font-medium">Inactive Points</div>
            <div className="text-sm text-gray-500">
              {activityData?.memberships?.byStatus?.inactive ?? 0} inactive
              memberships in the system
            </div>
          </div>
          {fetchingActivitySummaryData ? (
            <Skeleton className="w-24 h-6" />
          ) : (
            <div className="text-xl font-semibold">
              {activityData?.memberships?.inactivePoints ?? 0}
            </div>
          )}
        </div>
        <div className="flex justify-between items-center">
          <div>
            <div className="font-medium">Expired Points</div>
            <div className="text-sm text-gray-500">
              {activityData?.memberships?.byStatus?.expired ?? 0} expired
              memberships in the system
            </div>
          </div>
          {fetchingActivitySummaryData ? (
            <Skeleton className="w-24 h-6" />
          ) : (
            <div className="text-xl font-semibold">
              {activityData?.memberships?.expiredPoints ?? 0}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

function PaymentSummary({ activityData, fetchingActivitySummaryData }) {
  // Extract payment data with new structure
  const cashAmount = activityData?.payments?.byMethod?.cash?.amount || 0;
  const creditAmount = activityData?.payments?.byMethod?.credit?.amount || 0;
  const splitCount = activityData?.payments?.byMethod?.split?.count || 0;
  const cashCount = activityData?.payments?.byMethod?.cash?.count || 0;
  const creditCount = activityData?.payments?.byMethod?.credit?.count || 0;
  const creditBreakdown = activityData?.payments?.byMethod?.credit?.breakdown;

  return (
    <div className="border rounded-lg p-6 bg-white">
      <h3 className="font-medium mb-4">Payment Summary</h3>
      <div className="space-y-4">
        <div className="flex justify-between items-center pb-2 border-b">
          <div>
            <div className="font-medium">Total Revenue</div>
            <div className="text-sm text-gray-500">
              Total sum of all payments collected
            </div>
          </div>
          {fetchingActivitySummaryData ? (
            <Skeleton className="w-24 h-6" />
          ) : (
            <div className="text-xl font-semibold text-yellow-600">
              ${(activityData?.payments?.totalAmount || 0).toFixed(2)}
            </div>
          )}
        </div>
        <div className="flex justify-between items-center pb-2 border-b">
          <div>
            <div className="font-medium">Cash Payments</div>
            <div className="text-sm text-gray-500">
              ${cashAmount.toFixed(2)} from {cashCount} transactions
            </div>
          </div>
          {fetchingActivitySummaryData ? (
            <Skeleton className="w-24 h-6" />
          ) : (
            <div className="text-xl font-semibold">
              ${cashAmount.toFixed(2)}
            </div>
          )}
        </div>
        <div className="flex justify-between items-center pb-2 border-b">
          <div>
            <div className="font-medium">Credit Card</div>
            {creditBreakdown && (
              <div className="text-sm text-gray-500">
                {Object.entries(creditBreakdown).map(
                  ([key, value]: any[]) =>
                    value > 0 && (
                      <div key={key}>
                        {key === "other" ? "Other" : `$${key}`}: {value}{" "}
                        transaction(s)
                      </div>
                    )
                )}
              </div>
            )}
          </div>
          {fetchingActivitySummaryData ? (
            <Skeleton className="w-24 h-6" />
          ) : (
            <div className="text-xl font-semibold">
              ${creditAmount.toFixed(2)}
            </div>
          )}
        </div>
        <div className="flex justify-between items-center pb-2 border-b">
          <div>
            <div className="font-medium">Split Payments</div>
            <div className="text-sm text-gray-500">
              {splitCount} split payments made
            </div>
            <div className="text-sm text-gray-500">
              ${activityData?.payments?.byMethod?.split?.cashPayment || 0} split
              cash payments made
            </div>
            <div className="text-sm text-gray-500">
              ${activityData?.payments?.byMethod?.split?.creditPayment || 0}{" "}
              split credit payments made
            </div>
          </div>
          {fetchingActivitySummaryData ? (
            <Skeleton className="w-24 h-6" />
          ) : (
            <div className="text-xl font-semibold">{splitCount}</div>
          )}
        </div>
        <div className="flex justify-between items-center">
          <div>
            <div className="font-medium">Coupons Redeemed</div>
            <div className="text-sm text-gray-500">
              {activityData?.payments?.byMethod?.other?.count} coupons redeemed
            </div>
            <div className="text-sm text-gray-500">
              {activityData?.payments?.byMethod?.other?.totalPoints} total
              coupon points
            </div>
          </div>
          {fetchingActivitySummaryData ? (
            <Skeleton className="w-24 h-6" />
          ) : (
            <div className="text-xl font-semibold">
              ${activityData?.payments?.byMethod?.other?.amount ?? 0}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

function TrafficSummary({
  trafficDataForDisplay,
  fetchingActivitySummaryData,
}) {
  // Find max check-in count for calculating bar widths
  const maxCheckins =
    trafficDataForDisplay?.reduce(
      (max, item) => Math.max(max, item.checkin_count),
      0
    ) || 1; // Default to 1 to avoid division by zero

  return (
    <div className="border rounded-lg p-6 bg-white">
      <h3 className="font-medium mb-4">Traffic Summary</h3>

      {fetchingActivitySummaryData ? (
        <div className="space-y-4">
          {[1, 2, 3, 4].map((i) => (
            <Skeleton key={i} className="h-10 w-full" />
          ))}
        </div>
      ) : trafficDataForDisplay?.length > 0 ? (
        <div className="space-y-3">
          <div className="flex justify-between items-center pb-2 text-sm text-gray-500 font-medium border-b mb-2">
            <span>Time Range</span>
            <span>Check-ins</span>
          </div>

          {trafficDataForDisplay.map((data, index) => (
            <div key={index} className="space-y-1">
              <div className="flex justify-between items-center text-sm">
                <span className="font-medium text-gray-700">
                  {data.timeRange}
                </span>
                <span className="font-semibold text-gray-900">
                  {data.checkin_count}
                </span>
              </div>
              <div className="w-full bg-gray-100 rounded-full h-2">
                <div
                  className="bg-blue-600 h-2 rounded-full"
                  style={{
                    width: `${(data.checkin_count / maxCheckins) * 100}%`,
                    transition: "width 0.5s ease",
                  }}
                />
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="py-12 text-center">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-12 w-12 mx-auto text-gray-300 mb-4"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={1.5}
              d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
            />
          </svg>
          <p className="text-gray-500 font-medium">No traffic data available</p>
          <p className="text-sm text-gray-400 mt-1">
            Try selecting a different date range with more activity
          </p>
        </div>
      )}
    </div>
  );
}
