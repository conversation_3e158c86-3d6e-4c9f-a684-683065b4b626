import { PortableText } from "@portabletext/react";
import { Container } from "@stackshift-ui/container";
import { Flex } from "@stackshift-ui/flex";
import { Heading } from "@stackshift-ui/heading";
import { Section } from "@stackshift-ui/section";
import { TextComponentProps } from ".";
import { MyPortableTextComponents } from "types";
import { usePathname } from "next/navigation";

export default function TextComponent_E({
  heading,
  firstColumn,
}: TextComponentProps) {
  const pathname = usePathname();

  const textComponentBlockStyling: MyPortableTextComponents = {
    block: {
      h1: ({ children }) => (
        <Heading
          fontSize="3xl"
          weight="bold"
          className="mb-8 leading-normal text-black dark:text-white"
        >
          {children}
        </Heading>
      ),
      h2: ({ children }) => (
        <Heading
          type="h2"
          weight="bold"
          fontSize="2xl"
          className="mb-8 text-black dark:text-white"
        >
          {children}
        </Heading>
      ),
      h3: ({ children }) => (
        <Heading
          type="h3"
          fontSize="xl"
          weight="bold"
          className="mb-8 leading-normal text-black dark:text-white"
        >
          {children}
        </Heading>
      ),
      h4: ({ children }) => (
        <Heading
          type="h4"
          weight="bold"
          fontSize="lg"
          className="mb-8 leading-normal text-black dark:text-white"
        >
          {children}
        </Heading>
      ),
      normal: ({ children }) => (
        <p
          className={`mb-8 leading-relaxed text-cfc-gray ${
            pathname.includes("volunteer") ? "!text-xl font-light" : "text-base"
          }`}
        >
          {children}
        </p>
      ),
      blockquote: ({ children }) => (
        <blockquote className="mb-6 italic leading-loose text-gray-500 px-14">
          - {children}
        </blockquote>
      ),
    },
    code: ({ value }) => (
      <pre data-language={value.language}>
        <code>{value.code}</code>
      </pre>
    ),
    list: {
      bullet: ({ children }) => (
        <ul className="flex flex-col pl-10 mb-8 space-y-4 leading-relaxed list-disc">
          {children}
        </ul>
      ),
      number: ({ children }) => (
        <ol className="flex flex-col pl-10 mb-8 space-y-4 leading-relaxed list-decimal">
          {children}
        </ol>
      ),
    },
    listItem: {
      bullet: ({ children }) => <li className="leading-relaxed">{children}</li>,
      number: ({ children }) => <li className="leading-relaxed">{children}</li>,
    },
    marks: {
      strong: ({ children }) => <strong>{children}</strong>,
      em: ({ children }) => <em>{children}</em>,
      code: ({ children }) => <code>{children}</code>,
      link: ({ children, value }) => (
        <a
          className="hover:text-primary-foreground text-primary"
          href={value.href}
          target="_blank"
          rel="noopener noreferrer"
        >
          {children}
        </a>
      ),
    },
  };
  return (
    <Section
      className={`${
        pathname.includes("volunteer") ? "py-10" : "pt-10 pb-0"
      } bg-white relative overflow-hidden`}
    >
      <Container maxWidth={1280}>
        {heading && (
          <Heading
            type="h2"
            fontSize="4xl"
            weight="thin"
            className="mb-5 text-center !text-cfc-secondary leading-none md:px-0 px-4"
          >
            {heading}
          </Heading>
        )}


        <Flex wrap justify="center" className="mx-auto">
          {firstColumn && (
            <div
              className={`${
                pathname.includes("volunteer") ? "max-w-3xl" : "w-full md:px-0 px-4"
              } mb-2 text-xs text-center  `}
            >
              <PortableText
                value={firstColumn}
                components={textComponentBlockStyling}
                onMissingComponent={false} // Disabling warnings / handling unknown types
              />
            </div>
          )}
        </Flex>
      </Container>
    </Section>
  );
}

export { TextComponent_E };
