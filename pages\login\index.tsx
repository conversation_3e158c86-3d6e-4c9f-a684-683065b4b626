import LoginForm from "@/components/login-form";
import { createClientComponentClient } from "@supabase/auth-helpers-nextjs";
import { useEffect, useState } from "react";
import { useRouter } from "next/router";

export default function Login() {
  const [loading, setLoading] = useState(true);
  const supabase = createClientComponentClient();
  const router = useRouter();

  useEffect(() => {
    const handleAuthChange = async () => {
      try {
        const {
          data: { user },
        } = await supabase.auth.getUser();

        if (user) {
          const role = user.user_metadata?.role?.toLowerCase();

          const redirectPath =
            role === "admin"
              ? "/administrator"
              : role === "staff"
              ? "/membership"
              : "/points-checker";

          router.push(redirectPath);
        }
      } catch (error) {
        console.error("Auth error:", error);
      } finally {
        setLoading(false);
      }
    };

    handleAuthChange();

    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange(async (event, session) => {
      if (event === "SIGNED_IN" && session?.user) {
        const role = session.user.user_metadata?.role?.toLowerCase();
        const redirectPath =
          role === "admin" || role === "staff" || role === "volunteer"
            ? "/membership"
            : "/points-checker";

        window.location.href = redirectPath;
      }
    });

    return () => {
      subscription.unsubscribe();
    };
  }, []);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        Loading...
      </div>
    );
  }

  return <LoginForm />;
}
