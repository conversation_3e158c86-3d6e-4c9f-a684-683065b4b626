import React from "react";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "../../ui/dialog";
import { CustomButton } from "../../ui/custom-button";

interface CancelOrderDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  memberName: string;
}

export function CancelOrderDialog({
  isOpen,
  onClose,
  onConfirm,
  memberName,
}: CancelOrderDialogProps) {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Cancel Order</DialogTitle>
          <DialogDescription>
            Are you sure you want to cancel {memberName}'s order? This will
            clear the cart and all order information.
          </DialogDescription>
        </DialogHeader>
        <DialogFooter className="flex gap-2">
          <CustomButton variant="outline" onClick={onClose}>
            No, Keep Order
          </CustomButton>
          <CustomButton
            variant="destructive"
            onClick={() => {
              onConfirm();
              onClose();
            }}
          >
            Yes, Cancel Order
          </CustomButton>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
