import { PortableText } from "@portabletext/react";
import { Container } from "@stackshift-ui/container";
import { Flex } from "@stackshift-ui/flex";
import { Heading } from "@stackshift-ui/heading";
import { Section } from "@stackshift-ui/section";
import { TextComponentProps } from ".";
import { MyPortableTextComponents } from "types";

import Image from "next/image";
import { urlFor } from "lib/sanity";

// block styling as props to `components` of the PortableText component
export const textComponentBlockStyling: MyPortableTextComponents = {
  block: {
    h1: ({ children }) => (
      <Heading
        fontSize="3xl"
        weight="bold"
        className="mb-8 leading-normal text-black dark:text-white"
      >
        {children}
      </Heading>
    ),
    h2: ({ children }) => (
      <Heading
        type="h2"
        weight="bold"
        fontSize="2xl"
        className="mb-8 text-black dark:text-white"
      >
        {children}
      </Heading>
    ),
    h3: ({ children }) => (
      <Heading
        type="h3"
        fontSize="xl"
        weight="bold"
        className="mb-8 leading-normal text-black dark:text-white"
      >
        {children}
      </Heading>
    ),
    h4: ({ children }) => (
      <Heading
        type="h4"
        weight="bold"
        fontSize="lg"
        className="mb-8 leading-normal text-black dark:text-white"
      >
        {children}
      </Heading>
    ),
    normal: ({ children }) => (
      <p className="md:mb-8 mb-6 md:text-3xl text-2xl font-light leading-relaxed text-white">
        {children}
      </p>
    ),

    custom: ({ children }) => (
      <p className="md:mb-8 mb-6 text-black text-xs leading-relaxed ">
        {children}
      </p>
    ),

    blockquote: ({ children }) => (
      <blockquote className="mb-6 italic leading-loose text-gray-500 px-14">
        - {children}
      </blockquote>
    ),
  },
  code: ({ value }) => (
    <pre data-language={value.language}>
      <code>{value.code}</code>
    </pre>
  ),
  list: {
    bullet: ({ children }) => (
      <ul className="flex flex-col text-white md:text-3xl text-2xl font-light md:pl-20 pl-14 mb-8 space-y-4 leading-relaxed list-disc">
        {children}
      </ul>
    ),
    number: ({ children }) => (
      <ol className="flex flex-col pl-10 mb-8 space-y-4 leading-relaxed list-decimal">
        {children}
      </ol>
    ),
  },
  listItem: {
    bullet: ({ children }) => (
      <li className="leading-none text-white md:text-3xl text-lg font-light">
        {children}
      </li>
    ),
    number: ({ children }) => <li className="leading-relaxed">{children}</li>,
  },
  marks: {
    strong: ({ children }) => <strong>{children}</strong>,
    em: ({ children }) => <em>{children}</em>,
    code: ({ children }) => <code>{children}</code>,
    link: ({ children, value }) => (
      <a
        className="hover:text-primary-foreground text-primary"
        href={value.href}
        target="_blank"
        rel="noopener noreferrer"
      >
        {children}
      </a>
    ),
  },
  types: {
    addImage: ({ value }) => (
      <Image
        className="w-full h-full mb-5"
        width={500}
        height={500}
        sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
        src={urlFor(value?.image)}
        alt={value?.alt ?? value?.image?.asset?._ref}
      />
    ),
    addIframe: ({ value }) => {
      return (
        <div className="relative flex">
          <iframe
            loading="lazy"
            frameBorder="0"
            src={value?.iframe}
            width="100%"
            data-rocket-lazyload="fitvidscompatible"
            className="entered w-full h-[65rem]"
          />
        </div>
      );
    },
  },
};

export default function TextComponent_G({
  heading,
  firstColumn,
  secondColumn,
}: TextComponentProps) {
  return (
    <Section className="bg-white relative overflow-hidden md:pt-0 pt-10 pb-32">
      <Container maxWidth={1280} className="!px-0">
        <Flex
          wrap
          justify="center"
          className="mx-auto w-full  md:w-10/12 md:px-4"
        >
          {firstColumn && (
            <div className="text-xs w-full text-center bg-cfc-gray py-10 md:mb-32 mb-20">
              <PortableText
                value={firstColumn}
                components={textComponentBlockStyling}
                onMissingComponent={false} // Disabling warnings / handling unknown types
              />
            </div>
          )}

          {secondColumn && (
            <div className="w-full mb-2 text-xs rounded-lg text-center">
              <PortableText
                value={secondColumn}
                components={textComponentBlockStyling}
                onMissingComponent={false} // Disabling warnings / handling unknown types
              />
            </div>
          )}
        </Flex>
      </Container>
    </Section>
  );
}

export { TextComponent_G };
