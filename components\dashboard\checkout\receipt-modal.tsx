import React from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>Content,
  Di<PERSON><PERSON>eader,
  Di<PERSON>Title,
  DialogFooter,
} from "../../ui/dialog";
import { CustomButton } from "../../ui/custom-button";
import { OrderItem } from "../../../pages/checkout";

interface ReceiptModalProps {
  isOpen: boolean;
  onClose: () => void;
  orderItems: OrderItem[];
  orderNumber: string;
  memberName: string;
  totalPoints: number;
  onPrint: () => void;
  onComplete: () => void;
  onCancel: () => void;
  isCompletingOrder: boolean;
}

export function ReceiptModal({
  isOpen,
  onClose,
  orderItems,
  orderNumber,
  memberName,
  totalPoints,
  onPrint,
  onComplete,
  onCancel,
  isCompletingOrder,
}: ReceiptModalProps) {
  const handlePrint = () => {
    onPrint();
    window.print();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>Order Receipt</DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          <div className="text-center border-b pb-4">
            <h3 className="font-bold">Community Food Club</h3>
            {orderNumber && <p>Order #{orderNumber}</p>}
            {memberName && <p>Member: {memberName}</p>}
            <p>
              Date:{" "}
              {new Date().toLocaleDateString("en-US", {
                year: "numeric",
                month: "long",
                day: "numeric",
              })}
            </p>
          </div>

          <div className="space-y-2">
            {orderItems.map((item) => (
              <div key={item.id} className="flex justify-between">
                <span>{item.product_name}</span>
                <div className="flex gap-4">
                  <span>x{item.quantity}</span>
                  <span>({item.points} pts/unit)</span>
                  <span>{item.points * item.quantity} pts</span>
                </div>
              </div>
            ))}

            <div className="border-t pt-2 font-bold flex justify-between">
              <span>Total Points:</span>
              <span>{totalPoints}</span>
            </div>
          </div>
        </div>

        <DialogFooter className="flex gap-2 print:hidden">
          <CustomButton
            variant="outline"
            onClick={onCancel}
            disabled={isCompletingOrder}
          >
            Cancel
          </CustomButton>
          <CustomButton
            variant="outline"
            onClick={handlePrint}
            disabled={isCompletingOrder}
          >
            Print
          </CustomButton>
          <CustomButton onClick={onComplete} disabled={isCompletingOrder}>
            {isCompletingOrder ? "Completing..." : "Complete"}
          </CustomButton>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
