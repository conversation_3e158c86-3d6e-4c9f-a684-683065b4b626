import React from "react";
import { Card } from "../../ui/card";
import { useSelectClient } from "context/useSelectClientContext";
import { formatDate } from "utils";
import { DataTable } from "../../data-table";
import { useClientSearch } from "context/useClientSearchContext";
import TableFilters from "../table-filters";
import { useFilters } from "hooks/useFilters";

export default function AccountHistory() {
  const { selectedClient } = useSelectClient();
  const { isSearching } = useClientSearch();

  const historyFilters = {
    eventType: {
      label: "Event Type",
      options: [
        { id: "1", label: "Order", value: "order" },
        { id: "2", label: "Payment", value: "payment" },
        { id: "3", label: "Check-in", value: "check-in" },
        { id: "4", label: "Checkout", value: "checkout" },
      ],
    },
  };

  const { selectedFilters, handleFilterChange } = useFilters({
    eventType: [],
  });

  const filterHistory = (history) => {
    return history?.filter((event) => {
      const matchesEventType =
        selectedFilters.eventType.length === 0 ||
        selectedFilters.eventType.includes(event.type);

      return matchesEventType;
    });
  };

  const events = [
    ...(selectedClient?.return_items || []).map((item) => ({
      ...item,
      type: "return",
      created_at: item.created_at,
      performed_by: "Member",
      location: item.store_location,
      details: `Returned ${item.quantity} ${item.products?.product_name}, Points: ${item.total_points}, Reason: ${item.reason}`,
    })),
    ...(selectedClient?.check_in || []).flatMap((event) => [
      ...(event.checkout_date
        ? [
            {
              ...event,
              id: `${event.id}-checkout`,
              type: "checkout",
              performed_by: event.checked_out_by || "N/A",
              created_at: event.checkout_date,
              location: event.updated_store_location,
              details: `Checked out by ${event.checked_out_by || "N/A"}`,
            },
          ]
        : []),
      {
        ...event,
        id: `${event.id}-checkin`,
        type: "check-in",
        performed_by: event.checked_in_by || "N/A",
        created_at: event.checkin_date,
        location: event.added_store_location || "N/A",
        details: event.is_forced_checked_in
          ? `Force Checked-In by ${event.checked_in_by || "N/A"}`
          : "Regular Check-In",
      },
    ]),
    ...(selectedClient?.orders || []).map((order) => ({
      ...order,
      type: "order",
      performed_by: "Member",
      location: order.store_location,
      details: `Points Used: ${order.total_points_used}, Items: ${order.items.length}`,
    })),
    ...(selectedClient?.memberships || []).flatMap((membership) =>
      (membership.payments || []).map((payment) => ({
        ...payment,
        type: "payment",
        created_at: payment.date || "N/A",
        performed_by: membership.added_by || "N/A",
        location: payment.added_store_location || "N/A",
        details: `Amount: $${payment.amount || "N/A"}`,
      }))
    ),
  ].sort(
    (a, b) =>
      new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
  );

  const headers = [
    "Event Type",
    "Date",
    "Time",
    "Performed By",
    "Location",
    "Details",
  ];
  const renderRow = (event: any) => (
    <tr key={event.id} className="odd:bg-white even:bg-gray-50">
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
        {event.type.charAt(0).toUpperCase() + event.type.slice(1)}
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
        {formatDate(
          event.type === "check-in"
            ? event.checkin_date || event.created_at
            : event.type === "checkout"
            ? event.checkout_date || event.created_at
            : event.created_at
        )}
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
        {new Date(
          event.type === "check-in"
            ? event.checkin_date || event.created_at
            : event.type === "checkout"
            ? event.checkout_date || event.created_at
            : event.created_at
        ).toLocaleTimeString([], {
          hour: "2-digit",
          minute: "2-digit",
          hour12: true,
        })}
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
        {event.performed_by}
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
        {event.location}
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
        {event.details}
      </td>
    </tr>
  );

  console.log("🚀 ~ selectedClient", selectedClient);

  return (
    <Card className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-semibold">Account History</h2>
        <TableFilters
          filters={historyFilters}
          selectedFilters={selectedFilters}
          onFilterChange={handleFilterChange}
        />
      </div>
      <div className="space-y-4">
        <DataTable
          headers={headers}
          data={filterHistory(events)}
          renderRow={renderRow}
          isLoading={isSearching}
        />
      </div>
    </Card>
  );
}
