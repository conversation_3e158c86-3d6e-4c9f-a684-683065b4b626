import { Container } from "@stackshift-ui/container";
import { Flex } from "@stackshift-ui/flex";
import { Form } from "@stackshift-ui/form";
import { Heading } from "@stackshift-ui/heading";
import { Section } from "@stackshift-ui/section";
import React from "react";
import { CTAProps } from ".";
import { thankYouPageLink } from "../../../helper";
import { Form as iForm, LabeledRoute } from "../../../types";
import { FormField } from "@stackshift-ui/form-field";
import Button from "components/ui/button";

export default function CallToAction_G({ title, plainText, form }: CTAProps) {
  return (
    <Section className="px-10 pb-20 relative overflow-hidden">
      <Container maxWidth={1280} className="!px-0">
        <Flex
          align="center"
          direction="col"
          className="lg:justify-between lg:flex-col md:gap-2 !px-0"
        >
          <div className="text-center lg:text-left basis-1/2">
            <TitleAndText title={title} text={plainText} />
          </div>

          <FormFields form={form} />
        </Flex>
      </Container>
    </Section>
  );
}

function TitleAndText({ title, text }: { title?: string; text?: string }) {
  return (
    <React.Fragment>
      {title ? (
        <Heading type="h2" className="mb-2 text-white font-light">
          {title}
        </Heading>
      ) : null}

      {/* {text ? (
        <Text className="mb-8 leading-loose" muted>
          {text}
        </Text>
      ) : null} */}
    </React.Fragment>
  );
}

function CTABtton({ button }: { button?: LabeledRoute }) {
  if (!button?.label) return null;

  return (
    <Button as="link" link={button} ariaLabel={button?.label}>
      {button?.label}
    </Button>
  );
}

function FormFields({ form }: { form?: iForm }) {
  if (!form) return null;

  return (
    <div className="w-full lg:px-5 px-0 md:max-w-2xl">
      <div className="mb-14">
        {form?.name ? (
          <div>
            <Heading type="h2" weight="thin" className="text-center !text-cfc-secondary md:text-4xl">
              {form?.name}
            </Heading>
          </div>
        ) : null}

        {form?.subtitle ? (
          <div>
            <p className="text-center text-cfc-gray">{form?.subtitle}</p>
          </div>
        ) : null}
      </div>

      {form?.fields && (
        <Form
          id={form?.id ?? undefined}
          name="Contact-VariantA-Form"
          className="w-full lg:max-w-2xl max-w-xl mx-auto text-xs space-y-6 font-semibold md:pb-0 md:px-5"
          thankyouPage={thankYouPageLink(form?.thankYouPage)}
        >
          <div className="w-full flex flex-col gap-2">
            {form.fields?.slice(0, 2).map((formFields, idx) => (
              <React.Fragment>
                <span className="text-base text-cfc-gray font-extrabold uppercase">
                  {formFields?.name}
                </span>
                <FormField
                  noLabel
                  variant="primary"
                  name={formFields?.name ?? ""}
                  className={`cursor-pointer w-full text-cfc-gray bg-gray-200 rounded-xl outline-none tracking-wide font-heading !px-4 !py-3 resize-none text-base font-normal`}
                  placeholder={formFields?.name}
                  required={formFields?.isRequired}
                  {...formFields}
                />
              </React.Fragment>
            ))}
          </div>

          <div className="w-full flex flex-col md:gap-2 gap-2">
            {form?.fields?.slice(2, 3).map((formFields, index) => (
              <>
                <span className="text-base text-cfc-gray font-extrabold uppercase">
                  {formFields?.name}
                </span>
                <FormField
                  key={index}
                  noLabel
                  variant="primary"
                  name={formFields?.name ?? ""}
                  className={`w-full text-cfc-gray bg-gray-200 cursor-pointer rounded-lg outline-none tracking-wide font-heading !py-3 !px-4 !resize-none !text-base font-normal min-h-[200px]`}
                  placeholder={formFields?.name}
                  required={formFields?.isRequired}
                  {...formFields}
                />
              </>
            ))}
          </div>

          <div className="items-center flex justify-start w-full pt-2">
            <div>
              <div className="webriq-recaptcha" />
            </div>
            {form?.buttonLabel && (
              <Button
                as="button"
                variant="solid"
                className="bg-cfc-primary text-white font-extrabold px-10 py-2 hover:!bg-[#5d9a36] hover:scale-110 ease-in-out transition-all duration-300"
                radius="none"
                size="md"
                type="submit"
                ariaLabel={form?.buttonLabel}
              >
                {form?.buttonLabel}
              </Button>
            )}
          </div>
        </Form>
      )}
    </div>
  );
}

export { CallToAction_G };
