import { NextApiRequest, NextApiResponse } from "next";
import { supabaseClient } from "utils/supabase/client";

export default async function GET(req: NextApiRequest, res: NextApiResponse) {
  try {
    const { role } = req.query;

    if (!["admin", "staff"].includes(role as string)) {
      return res.status(403).json({
        message:
          "Unauthorized. Only admin or staff can view distribution data.",
      });
    }

    const [
      { data: orders, error: ordersError },
      { data: products, error: productsError },
      { data: categories, error: categoryError },
    ] = await Promise.all([
      supabaseClient
        .from("orders")
        .select("id, created_at, total_points_used, items, store_location")
        .eq("is_completed", true)
        .order("created_at", { ascending: false }),

      supabaseClient
        .from("products")
        .select("id, category, product_name, unit_price, unit_weight")
        .order("created_at", { ascending: false }),

      supabaseClient.from("category").select("id, name"),
    ]);

    if (ordersError || productsError || categoryError) {
      return res.status(500).json({
        message: "Error fetching data from the database",
        errors: {
          orders: ordersError?.message,
          products: productsError?.message,
          categories: categoryError?.message,
        },
      });
    }

    const productMap = Object.fromEntries(products.map((p) => [p.id, p]));
    const categoryMap = Object.fromEntries(
      categories.map((c) => [c.id, c.name || "Unknown"])
    );

    const categoryStatsByLocation: Record<string, any> = {};

    for (const order of orders) {
      const { store_location } = order;

      if (!categoryStatsByLocation[store_location]) {
        categoryStatsByLocation[store_location] = {};
      }

      for (const item of order.items) {
        const product = productMap[item.product_id];
        if (!product) continue;

        const { category, unit_price, unit_weight } = product;
        const { quantity, total_points } = item;
        const categoryName = categoryMap[category] || "Unknown";

        const categoryKey = `${category}_${store_location}`;

        if (!categoryStatsByLocation[store_location][categoryKey]) {
          categoryStatsByLocation[store_location][categoryKey] = {
            name: categoryName,
            quantity: 0,
            points_used: 0,
            total_value: 0,
            store_location,
            unit_weight,
            date: order.created_at,
          };
        }

        const categoryData =
          categoryStatsByLocation[store_location][categoryKey];
        categoryData.quantity += quantity;
        categoryData.points_used += total_points;
        categoryData.total_value += unit_price * quantity;

        if (new Date(order.created_at) > new Date(categoryData.date)) {
          categoryData.date = order.created_at;
        }
      }
    }

    const result = Object.keys(categoryStatsByLocation).map((location) => ({
      store_location: location,
      category: Object.values(categoryStatsByLocation[location]),
      orders: orders
        .filter((order) => order.store_location === location)
        .map((order) => ({
          id: order.id,
          created_at: order.created_at,
          store_location: order.store_location,
        })),
    }));

    res.json({ distributions: result });
  } catch (error) {
    console.error("Error fetching distribution data:", error);
    res.status(500).json({ error: "Internal server error" });
  }
}
