import { Container } from "@stackshift-ui/container";
import { Flex } from "@stackshift-ui/flex";
import { Form } from "@stackshift-ui/form";
import { Heading } from "@stackshift-ui/heading";
import { Image } from "@stackshift-ui/image";
import { Input } from "@stackshift-ui/input";
import { Link } from "@stackshift-ui/link";
import { Section } from "@stackshift-ui/section";
import { Text } from "@stackshift-ui/text";
import React from "react";
import { CTAProps } from ".";
import { logoLink, thankYouPageLink } from "../../../helper";
import {
  Form as iForm,
  LabeledRoute,
  LabeledRouteWithKey,
} from "../../../types";
import { FormField } from "@stackshift-ui/form-field";
import Button from "components/ui/button";

export default function CallToAction_F({
  logo,
  title,
  plainText,
  button,
  form,
  formLinks,
  signInLink,
}: CTAProps) {
  return (
    <Section className="px-10 pt-40 bg-cfc-primary relative overflow-hidden">
      <svg
        xmlns="http://www.w3.org/2000/svg"
        viewBox="0 0 1000 100"
        preserveAspectRatio="none"
        className="absolute -top-1 w-full left-0 wave-svg h-16"
      >
        <path
          className="elementor-shape-fill"
          d="M1000,4.3V0H0v4.3C0.9,23.1,126.7,99.2,500,100S1000,22.7,1000,4.3z"
        ></path>
      </svg>

      <Container maxWidth={1280}>
        <Flex
          align="center"
          direction="col"
          className="lg:justify-between lg:flex-col md:gap-2"
        >
          <div className="text-center lg:text-left basis-1/2">
            <TitleAndText title={title} text={plainText} />

            {/* <CTABtton button={button} /> */}
          </div>

          <FormFields form={form} />
        </Flex>
      </Container>
    </Section>
  );
}

function TitleAndText({ title, text }: { title?: string; text?: string }) {
  return (
    <React.Fragment>
      {title ? (
        <Heading type="h2" className="mb-2 text-white font-light">
          {title}
        </Heading>
      ) : null}

      {/* {text ? (
        <Text className="mb-8 leading-loose" muted>
          {text}
        </Text>
      ) : null} */}
    </React.Fragment>
  );
}

function CTABtton({ button }: { button?: LabeledRoute }) {
  if (!button?.label) return null;

  return (
    <Button as="link" link={button} ariaLabel={button?.label}>
      {button?.label}
    </Button>
  );
}

function FormFields({ form }: { form?: iForm }) {
  if (!form) return null;

  return (
    <div className="w-full lg:px-10 px-0 md:max-w-3xl">
      {form?.fields && (
        <Form
          id={form?.id ?? undefined}
          name="Contact-VariantA-Form"
          className="w-full text-xs space-y-2 font-semibold md:pb-0 p-5"
          thankyouPage={thankYouPageLink(form?.thankYouPage)}
        >
          <div className="w-full flex md:flex-row flex-col gap-2">
            {form.fields
              ?.slice(0, 2)
              .map((formFields, idx) => (
                <FormField
                  noLabel
                  variant="primary"
                  name={formFields?.name ?? ""}
                  className={`md:w-1/2 cursor-pointer w-full text-white bg-cfc-primary-foreground rounded-lg outline-none tracking-wide font-heading !px-4 !py-2 resize-none text-sm font-normal`}
                  placeholder={formFields?.name}
                  required={formFields?.isRequired}
                  {...formFields}
                />
              ))}
          </div>

          <div className="w-full flex md:flex-row flex-col md:gap-4">
            {form?.fields
              ?.slice(2, 3)
              .map((formFields, index) => (
                <FormField
                  key={index}
                  noLabel
                  variant="primary"
                  name={formFields?.name ?? ""}
                  className={`w-full text-white bg-cfc-primary-foreground cursor-pointer rounded-lg outline-none tracking-wide font-heading !py-2 !px-4 resize-none text-sm font-normal`}
                  placeholder={formFields?.name}
                  required={formFields?.isRequired}
                  {...formFields}
                />
              ))}
          </div>

          <div className="items-center flex justify-center w-full pt-2">
            <div>
              <div className="webriq-recaptcha" />
            </div>
            {form?.buttonLabel && (
              <Button
                as="button"
                variant="solid"
                radius="none"
                size="md"
                type="submit"
                ariaLabel={form?.buttonLabel}
              >
                {form?.buttonLabel}
              </Button>
            )}
          </div>
        </Form>
      )}
    </div>
  );
}

export { CallToAction_F };
