import { PortableText } from "@portabletext/react";
import { Container } from "@stackshift-ui/container";
import { Flex } from "@stackshift-ui/flex";
import { Heading } from "@stackshift-ui/heading";
import { Section } from "@stackshift-ui/section";
import { TextComponentProps } from ".";
import { MyPortableTextComponents } from "types";

export const textComponentBlockStyling: MyPortableTextComponents = {
  block: {
    h1: ({ children }) => (
      <Heading
        fontSize="3xl"
        weight="bold"
        className="mb-8 leading-normal text-black dark:text-white"
      >
        {children}
      </Heading>
    ),
    h2: ({ children }) => (
      <Heading
        type="h2"
        weight="semibold"
        className="mb-2 text-center md:text-3xl text-2xl !text-cfc-gray/80 !leading-none dark:text-white px-4"
      >
        {children}
      </Heading>
    ),
    h3: ({ children }) => (
      <Heading
        type="h3"
        fontSize="xl"
        weight="thin"
        className="mb-4 leading-normal tracking-wide !text-cfc-secondary/65 dark:text-white"
      >
        {children}
      </Heading>
    ),
    h4: ({ children }) => (
      <Heading
        type="h4"
        weight="bold"
        fontSize="lg"
        className="mb-8 leading-normal text-black dark:text-white"
      >
        {children}
      </Heading>
    ),
    normal: ({ children }) => (
      <p className="mb-1 md:leading-normal text-center leading-normal font-normal tracking-wide text-cfc-gray lg:text-base md:text-sm text-base">
        {children}
      </p>
    ),

    blockquote: ({ children }) => (
      <blockquote className="mb-6 italic leading-loose text-gray-500 px-14">
        - {children}
      </blockquote>
    ),
  },

  code: ({ value }) => (
    <pre data-language={value.language}>
      <code>{value.code}</code>
    </pre>
  ),
  list: {
    bullet: ({ children }) => (
      <ul className="flex flex-col pl-10 mb-8 space-y-4 leading-relaxed list-disc">
        {children}
      </ul>
    ),
    number: ({ children }) => (
      <ol className="flex flex-col pl-10 mb-8 space-y-4 leading-relaxed list-decimal">
        {children}
      </ol>
    ),
  },
  listItem: {
    bullet: ({ children }) => <li className="leading-relaxed">{children}</li>,
    number: ({ children }) => <li className="leading-relaxed">{children}</li>,
  },
  marks: {
    strong: ({ children }) => <strong>{children}</strong>,
    em: ({ children }) => <em>{children}</em>,
    code: ({ children }) => <code>{children}</code>,
    link: ({ children, value }) => (
      <a
        className="hover:text-primary-foreground text-primary"
        href={value.href}
        target="_blank"
        rel="noopener noreferrer"
      >
        {children}
      </a>
    ),
  },
};

export default function TextComponent_M({
  heading,
  firstColumn,
  secondColumn,
  thirdColumn,
  fourthColumn
}: TextComponentProps) {
  return (
    <Section className="pt-12 lg:!px-4 !px-0">
      <Container className="container lg:!px-4 !px-0 mx-auto" maxWidth={1280}>
        {heading && (
          <Heading weight="light" type="h2" className="w-full md:!text-5xl !text-3xl text-cfc-secondary/65 mb-2 text-center">
            {heading}
          </Heading>
        )}

        <Flex className="flex md:items-start items-center justify-center md:flex-row flex-col mx-auto lg:max-w-6xl w-full">
          {firstColumn && (
            <div className="text-xs leading-relaxed text-justify text-gray-500 lg:w-1/4 lg:text-base px-2 md:mb-0 mb-10">
              <PortableText
                value={firstColumn}
                components={textComponentBlockStyling}
                onMissingComponent={false} // Disabling warnings / handling unknown types
              />
            </div>
          )}

          {secondColumn && (
            <div className="text-xs leading-relaxed text-justify text-gray-500 lg:w-1/4 lg:text-base px-2 md:mb-0 mb-10">
              <PortableText
                value={secondColumn}
                components={textComponentBlockStyling}
                onMissingComponent={false} // Disabling warnings / handling unknown types
              />
            </div>
          )}

          {thirdColumn && (
            <div className="text-xs leading-relaxed text-justify text-gray-500 lg:w-1/4 lg:text-base px-2 md:mb-0 mb-10">
              <PortableText
                value={thirdColumn}
                components={textComponentBlockStyling}
                onMissingComponent={false} // Disabling warnings / handling unknown types
              />
            </div>
          )}
          {fourthColumn && (
            <div className="text-xs leading-relaxed text-justify text-gray-500 lg:w-1/4 lg:text-base px-2 md:mb-0">
              <PortableText
                value={fourthColumn}
                components={textComponentBlockStyling}
                onMissingComponent={false} // Disabling warnings / handling unknown types
              />
            </div>
          )}
        </Flex>
      </Container>
    </Section>
  );
}

export { TextComponent_M };
