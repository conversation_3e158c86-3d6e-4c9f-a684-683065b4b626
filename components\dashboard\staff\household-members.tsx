import React, { useEffect, useState } from "react";
import { CustomButton } from "@/components/ui/custom-button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { RequiredLabel } from "@/components/ui/required-label";
import {
  Select,
  SelectContent,
  SelectValue,
  SelectTrigger,
  SelectItem,
} from "@/components/ui/select";
import { Skeleton } from "@/components/ui/skeleton";
import { useClientSearch } from "context/useClientSearchContext";
import { useSelectClient } from "context/useSelectClientContext";
import { Plus, X } from "lucide-react";
import { useHouseholdData } from "context/useHousehold";
import { CardTitle } from "@/components/ui/card";
import { CardHeader } from "@/components/ui/card";

export default function HouseholdMembers({
  isDisabled,
  headOfHousehold = false,
  selectedUser,
  isUserForm = false,
}: {
  isDisabled: boolean;
  headOfHousehold?: boolean;
  selectedUser?: any;
  isUserForm?: boolean;
}) {
  const { selectedClient } = useSelectClient();
  const { isSearching } = useClientSearch();
  const {
    setBirthdays,
    birthdays,
    getAge,
    handleBirthdayChange,
    setMemberIncomes,
    memberCount,
    setMemberCount,
  } = useHouseholdData();
  const [memberTypes, setMemberTypes] = useState<Record<number, string>>({});

  const data = selectedClient ? selectedClient : selectedUser;

  // Get first name and last name from the form when in add user context
  const getHeadOfHouseholdName = () => {
    if (headOfHousehold) {
      const firstName = document.getElementById("firstName")?.value || "";
      const lastName = document.getElementById("lastName")?.value || "";
      return `${firstName} ${lastName}`.trim();
    }
    return "";
  };

  React.useEffect(() => {
    if (data?.additional_info?.household_members) {
      const members = data.additional_info.household_members.filter(
        (member) => member
      );

      setMemberCount(members.length || 1);

      // Initialize state with existing data
      const initialBirthdays = {};
      const initialIncomes = {};
      const initialMemberTypes = {};

      members.forEach((member, index) => {
        initialBirthdays[index] = member.memberBirthday || "";
        initialIncomes[index] = member.memberIncome || 0;
        initialMemberTypes[index] = member.memberType || "adult";
      });

      setBirthdays(initialBirthdays);
      setMemberIncomes(initialIncomes);
      setMemberTypes(initialMemberTypes);
    }
  }, [data]);

  useEffect(() => {
    setMemberTypes((prevTypes) => {
      const updatedMemberTypes = { ...prevTypes };

      for (let i = 0; i < memberCount; i++) {
        const age = getAge(i);
        if (typeof age === "number") {
          updatedMemberTypes[i] = age < 18 ? "child" : "adult";
        }
      }

      return updatedMemberTypes;
    });
  }, [getAge, memberCount, birthdays]);

  // Effect to update head of household name when first/last name changes
  useEffect(() => {
    const updateHeadOfHouseholdName = () => {
      if (headOfHousehold) {
        const headName = getHeadOfHouseholdName();
        if (headName) {
          const headOfHouseholdInput = document.getElementById(
            "memberName-0"
          ) as HTMLInputElement;
          if (headOfHouseholdInput) {
            headOfHouseholdInput.value = headName;
          }
        }
      }
    };

    // Set up event listeners for first name and last name inputs
    const firstNameInput = document.getElementById("firstName");
    const lastNameInput = document.getElementById("lastName");

    if (firstNameInput && lastNameInput) {
      firstNameInput.addEventListener("input", updateHeadOfHouseholdName);
      lastNameInput.addEventListener("input", updateHeadOfHouseholdName);

      console.log("updateHeadOfHouseholdName()", updateHeadOfHouseholdName());

      // Initial update
      updateHeadOfHouseholdName();

      // Cleanup
      return () => {
        firstNameInput.removeEventListener("input", updateHeadOfHouseholdName);
        lastNameInput.removeEventListener("input", updateHeadOfHouseholdName);
      };
    }
  }, [headOfHousehold]);

  const addMember = () => {
    setMemberCount((prev) => prev + 1);
  };

  const removeMember = (indexToRemove: number) => {
    if (memberCount > 1) {
      setMemberCount((prev) => prev - 1);
      setBirthdays((prev) => {
        const newBirthdays = { ...prev };
        delete newBirthdays[indexToRemove];
        return newBirthdays;
      });
      setMemberIncomes((prev) => {
        const newIncomes = { ...prev };
        delete newIncomes[indexToRemove];
        return newIncomes;
      });
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        {isUserForm ? (
          <div></div>
        ) : (
          <CardHeader>
            <CardTitle>Household Members</CardTitle>
          </CardHeader>
        )}
        <CustomButton
          type="button"
          variant="outline"
          size="sm"
          onClick={addMember}
          disabled={isDisabled}
          className="flex items-center gap-1"
        >
          <Plus className="w-4 h-4" />
          Add New Member
        </CustomButton>
      </div>
      <div className="space-y-4">
        {[...Array(memberCount)].map((_, index) => {
          const isHeadOfHousehold = index === 0;
          return (
            <div
              key={index}
              className="relative grid grid-cols-12 gap-4 p-4 border rounded-lg items-center"
            >
              {!isHeadOfHousehold && (
                <button
                  type="button"
                  className="absolute -top-2 -right-2 h-12 w-8 rounded-full p-0 text-red-500"
                  onClick={() => removeMember(index)}
                  disabled={isDisabled}
                >
                  <X className="h-5 w-5" />
                </button>
              )}

              <div className="col-span-3 space-y-2">
                <RequiredLabel htmlFor={`memberName-${index}`}>
                  {isHeadOfHousehold ? "Head of Household Name" : "Name"}
                </RequiredLabel>
                {isSearching ? (
                  <Skeleton className="w-full h-9" />
                ) : (
                  <Input
                    id={`memberName-${index}`}
                    name={`memberName-${index}`}
                    defaultValue={
                      data?.additional_info?.household_members?.[index]
                        ?.memberName ||
                      (isHeadOfHousehold && headOfHousehold
                        ? getHeadOfHouseholdName()
                        : "")
                    }
                    disabled={
                      isHeadOfHousehold && headOfHousehold ? true : isDisabled
                    }
                    required
                  />
                )}
              </div>

              <div className="col-span-2 space-y-2">
                <RequiredLabel htmlFor={`memberBirthday-${index}`}>
                  Birthday
                </RequiredLabel>
                {isSearching ? (
                  <Skeleton className="w-full h-9" />
                ) : (
                  <Input
                    type="date"
                    id={`memberBirthday-${index}`}
                    name={`memberBirthday-${index}`}
                    defaultValue={
                      data?.additional_info?.household_members?.[index]
                        ?.memberBirthday || ""
                    }
                    onChange={(e) =>
                      handleBirthdayChange(index, e.target.value)
                    }
                    disabled={isDisabled}
                    required
                  />
                )}
              </div>

              <div className="space-y-2">
                <Label>Age</Label>
                <div className="h-9 flex items-center text-gray-600">
                  {isSearching ? (
                    <Skeleton className="w-full h-9" />
                  ) : (
                    <span>{getAge(index)}</span>
                  )}
                </div>
              </div>

              <div className="col-span-2 space-y-2">
                <RequiredLabel htmlFor={`memberIncome-${index}`}>
                  Annual Income
                </RequiredLabel>
                {isSearching ? (
                  <Skeleton className="w-full h-9" />
                ) : (
                  <div className="relative">
                    <span className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-500">
                      $
                    </span>
                    <Input
                      type="number"
                      id={`memberIncome-${index}`}
                      name={`memberIncome-${index}`}
                      className="pl-7"
                      min="0"
                      step="1"
                      defaultValue={
                        data?.additional_info?.household_members?.[index]
                          ?.memberIncome || ""
                      }
                      onChange={(e) =>
                        setMemberIncomes((prev) => {
                          const updated = { ...prev };
                          updated[index] = Number(e.target.value);
                          return updated;
                        })
                      }
                      disabled={isDisabled}
                      required
                    />
                  </div>
                )}
              </div>

              <div className="col-span-2 space-y-2">
                <Label htmlFor={`memberIncomeNotes-${index}`}>
                  Income Notes
                </Label>
                {isSearching ? (
                  <Skeleton className="w-full h-9" />
                ) : (
                  <Input
                    id={`memberIncomeNotes-${index}`}
                    name={`memberIncomeNotes-${index}`}
                    defaultValue={
                      data?.additional_info?.household_members?.[index]
                        ?.memberIncomeNotes || ""
                    }
                    disabled={isDisabled}
                  />
                )}
              </div>

              <div className="col-span-2 space-y-2">
                <RequiredLabel htmlFor={`memberType-${index}`}>
                  Member Type
                </RequiredLabel>
                {isSearching ? (
                  <Skeleton className="w-full h-9" />
                ) : (
                  <Select
                    defaultValue={memberTypes[index] || "adult"}
                    value={memberTypes[index] || "adult"}
                    disabled={isDisabled}
                    name={`memberType-${index}`}
                    onValueChange={(value) => {
                      setMemberTypes((prev) => ({ ...prev, [index]: value }));
                    }}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="adult">Adult</SelectItem>
                      <SelectItem value="child">Child</SelectItem>
                    </SelectContent>
                  </Select>
                )}
              </div>
            </div>
          );
        })}
      </div>

      <div className="space-y-2">
        <Label htmlFor="householdNotes">Household Notes & Verification</Label>
        {isSearching ? (
          <Skeleton className="w-full h-24" />
        ) : (
          <textarea
            id="householdNotes"
            name="householdNotes"
            className="w-full min-h-[6rem] rounded-md border border-input p-3"
            defaultValue={
              selectedClient?.additional_info?.household_note_verification || ""
            }
            disabled={isDisabled}
          />
        )}
      </div>
    </div>
  );
}
