import React, {
  createContext,
  useState,
  useContext,
  useEffect,
  ReactNode,
} from "react";
import axios from "axios";
import { useAuthCheck } from "hooks/useAuthCheck";

interface DistributionDataContextType {
  distributionData: any;
  fetchingDistributionData: boolean;
  setFetchingDistributionData: React.Dispatch<React.SetStateAction<boolean>>;
  refreshData: () => void;
}

const DistributionDataContext =
  createContext<DistributionDataContextType | null>(null);

export const useDistributionData = () => {
  const context = useContext(DistributionDataContext);
  if (!context) {
    throw new Error(
      "useDistributionData must be used within a DistributionDataProvider"
    );
  }
  return context;
};

interface DistributionDataProviderProps {
  children: ReactNode;
}

export const DistributionDataProvider: React.FC<
  DistributionDataProviderProps
> = ({ children }) => {
  const [distributionData, setDistributionData] = useState<any>([]);
  const [fetchingDistributionData, setFetchingDistributionData] =
    useState<boolean>(true);
  const { user, token } = useAuthCheck();

  const fetchData = async () => {
    try {
      const response = await axios.get("/api/admin/distribution", {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        params: { role: user?.user_metadata?.role },
      });
      setDistributionData(response.data);
    } catch (error) {
      console.error("Error fetching distribution data:", error);
    } finally {
      setFetchingDistributionData(false);
    }
  };

  const refreshData = () => {
    fetchData();
  };

  useEffect(() => {
    if (user) {
      fetchData();
    }
  }, [user]);

  return (
    <DistributionDataContext.Provider
      value={{
        distributionData,
        fetchingDistributionData,
        setFetchingDistributionData,
        refreshData,
      }}
    >
      {children}
    </DistributionDataContext.Provider>
  );
};
