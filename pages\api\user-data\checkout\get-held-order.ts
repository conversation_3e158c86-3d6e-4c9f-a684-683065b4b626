import { NextApiRequest, NextApiResponse } from "next";
import { calculateCheckoutStatus } from "utils";
import { supabaseClient } from "utils/supabase/client";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== "GET") {
    return res.status(405).json({ error: "Method not allowed" });
  }

  try {
    // Fetch members
    const { data: members, error: memberError } = await supabaseClient
      .from("members")
      .select("id, first_name, last_name");

    if (memberError) throw memberError;
    const memberIds = members.map((m) => m.id);

    const { data: orders, error: orderError } = await supabaseClient
      .from("orders")
      .select("*, member_id")
      .in("member_id", memberIds)
      .eq("on_hold", true)
      .or("is_completed.is.null, is_completed.eq.false");

    if (orderError) throw orderError;

    const { data: memberships, error: membershipError } = await supabaseClient
      .from("memberships")
      .select("member_id, id, available_points, start_date, end_date, status")
      .in("member_id", memberIds);

    if (membershipError) throw membershipError;

    const { data: checkins, error: checkinError } = await supabaseClient
      .from("checkins")
      .select("member_id, checkin_date, checkout_date")
      .in("member_id", memberIds)
      .order("created_at", { ascending: false });

    if (checkinError) throw checkinError;

    const formattedOrders = orders.map((order) => {
      const member = members.find((m) => m.id === order.member_id);
      const memberCheckins = checkins.filter(
        (c) => c.member_id === order.member_id
      );
      const memberMemberships = memberships.filter(
        (m) => m.member_id === order.member_id
      );

      return {
        ...order,
        member: {
          first_name: member?.first_name ?? "Unknown",
          last_name: member?.last_name ?? "Unknown",
          checkoutStatus: calculateCheckoutStatus(
            memberCheckins,
            memberMemberships
          ),
          memberships: memberMemberships,
        },
      };
    });

    return res.status(200).json(formattedOrders);
  } catch (error) {
    console.error("Error fetching on-hold orders:", error);
    return res.status(500).json({ error: "Failed to fetch on-hold orders" });
  }
}
