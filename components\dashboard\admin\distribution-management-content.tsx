import { Input } from "@/components/ui/input";
import { Search } from "lucide-react";
import React, { useState, useMemo } from "react";
import { useDistributionData } from "context/useDistributionData";
import { formatDate } from "utils";
import { DataTable } from "@/components/data-table";
import { Skeleton } from "@/components/ui/skeleton";
import { DateRangeFilter } from "@/components/ui/date-range-filter";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

export default function DistributionManagementContent() {
  const { distributionData, fetchingDistributionData } = useDistributionData();
  const [distributionSearchTerm, setDistributionSearchTerm] = useState("");
  const [selectedLocation, setSelectedLocation] =
    useState<string>("All Locations");
  const [dateRange, setDateRange] = useState<{
    from: Date | undefined;
    to: Date | undefined;
  }>({
    from: undefined,
    to: undefined,
  });

  // Get unique store locations dynamically and include "All Locations"
  const uniqueLocations = useMemo(() => {
    const locations =
      distributionData?.distributions?.map(
        (distribution) => distribution.store_location
      ) || [];
    return ["All Locations", ...new Set(locations)];
  }, [distributionData]);

  // Get all categories if "All Locations" is selected
  const selectedDistributionCategories = useMemo(() => {
    if (selectedLocation === "All Locations" || !selectedLocation) {
      // Merge and aggregate categories by name if "All Locations" is selected
      const combinedCategories = distributionData?.distributions?.flatMap(
        (distribution) => distribution.category || []
      );

      // Combine values for categories with the same name
      const aggregatedCategories = combinedCategories?.reduce((acc, item) => {
        if (!acc[item.name]) {
          acc[item.name] = {
            ...item,
            quantity: 0,
            points_used: 0,
            total_value: 0,
            totalPoundsDistributed: 0,
          };
        }

        acc[item.name].quantity += item.quantity || 0;
        acc[item.name].points_used += item.points_used || 0;
        acc[item.name].total_value += item.total_value || 0;
        acc[item.name].totalPoundsDistributed +=
          (item.quantity || 0) * (item.unit_weight || 0);

        return acc;
      }, {});

      return aggregatedCategories ? Object.values(aggregatedCategories) : [];
    }

    // Return categories from the selected location
    return (
      distributionData?.distributions?.find(
        (distribution) => distribution.store_location === selectedLocation
      )?.category || []
    );
  }, [distributionData, selectedLocation]);

  // Search and filter categories in the selected distribution
  const filterDistributions = (distributions: any[]) => {
    return distributions?.filter((distribution) => {
      const matchesSearch =
        distributionSearchTerm === "" ||
        distribution.name
          .toLowerCase()
          .includes(distributionSearchTerm.toLowerCase()) ||
        formatDate(distribution.date)
          .toLowerCase()
          .includes(distributionSearchTerm.toLowerCase());

      let matchesDateRange = true;
      if (dateRange.from && dateRange.to) {
        const distributionDate = new Date(distribution.date);
        matchesDateRange =
          distributionDate >= dateRange.from &&
          distributionDate <= dateRange.to;
      }

      return matchesSearch && matchesDateRange;
    });
  };

  // Filtered category data
  const filteredData = filterDistributions(selectedDistributionCategories);

  // Calculate checkouts for filtered orders
  const calculateCheckouts = () => {
    const allOrders =
      selectedLocation === "All Locations"
        ? distributionData?.distributions?.flatMap(
            (distribution) => distribution.orders || []
          )
        : distributionData?.distributions?.find(
            (distribution) => distribution.store_location === selectedLocation
          )?.orders || [];

    if (!allOrders?.length) {
      return 0;
    }

    const filteredOrders = allOrders.filter((order) => {
      if (dateRange.from && dateRange.to) {
        const orderDate = new Date(order.created_at);
        return orderDate >= dateRange.from && orderDate <= dateRange.to;
      }
      return true;
    });

    return filteredOrders.length;
  };

  const calculateTotals = (filteredData: any[]) => {
    if (!filteredData || filteredData.length === 0) {
      return {
        totalItemsDistributed: 0,
        totalPointsUsed: 0,
        totalValue: 0,
        totalPoundsDistributed: 0,
      };
    }

    return filteredData.reduce(
      (acc, item) => {
        return {
          totalItemsDistributed:
            acc.totalItemsDistributed + (item.quantity || 0),
          totalPointsUsed: acc.totalPointsUsed + (item.points_used || 0),
          totalValue: acc.totalValue + (item.total_value || 0),
          totalPoundsDistributed:
            acc.totalPoundsDistributed +
            (item.quantity * item.unit_weight || 0),
        };
      },
      {
        totalItemsDistributed: 0,
        totalPointsUsed: 0,
        totalValue: 0,
        totalPoundsDistributed: 0,
      }
    );
  };

  const checkoutsCount = calculateCheckouts();
  const {
    totalItemsDistributed,
    totalPointsUsed,
    totalValue,
    totalPoundsDistributed,
  } = calculateTotals(filteredData);

  const headers = [
    "Category",
    "Quantity Distributed",
    "Points Used",
    "Estimated Retail Value Distributed",
    "Date",
  ];

  const renderRow = (item: any) => (
    <tr key={item.name} className="border-b">
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
          {item.name}
        </span>
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
        {item.quantity}
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
        {item.points_used}
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
        ${item.total_value.toFixed(2)}
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
        {formatDate(item.date)}
      </td>
    </tr>
  );

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-semibold">Distribution Details</h2>
        <div className="flex gap-4 items-center">
          <div className="relative">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
            <Input
              placeholder="Search distributions..."
              className="pl-10 w-full md:w-[300px]"
              value={distributionSearchTerm}
              onChange={(e) => setDistributionSearchTerm(e.target.value)}
            />
          </div>
          <div className="flex flex-col md:flex-row gap-4 items-start md:items-center">
            <DateRangeFilter
              isCustom={false}
              onDateRangeChange={(dateRange) =>
                setDateRange({
                  from: dateRange.from ?? new Date(2020, 0, 1),
                  to: dateRange.to ?? new Date(),
                })
              }
            />
          </div>
          <Select
            value={selectedLocation}
            onValueChange={(value) => setSelectedLocation(value)}
          >
            <SelectTrigger className="w-[200px]">
              <SelectValue placeholder="Select Location" />
            </SelectTrigger>
            <SelectContent>
              {uniqueLocations?.map((location, index) => (
                <SelectItem key={index} value={location as string}>
                  {location as string}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="grid grid-cols-5 gap-4 mb-6">
        <div className="p-4 border rounded-lg bg-white">
          <div className="text-sm text-gray-500">Total Items Distributed</div>
          {fetchingDistributionData ? (
            <Skeleton className="h-10 mt-1" />
          ) : (
            <div className="text-2xl font-semibold mt-1">
              {totalItemsDistributed}
            </div>
          )}
        </div>
        <div className="p-4 border rounded-lg bg-white">
          <div className="text-sm text-gray-500">Total Pounds Distributed</div>
          {fetchingDistributionData ? (
            <Skeleton className="h-10 mt-1" />
          ) : (
            <div className="text-2xl font-semibold mt-1">
              {totalPoundsDistributed.toFixed(2)}
            </div>
          )}
        </div>
        <div className="p-4 border rounded-lg bg-white">
          <div className="text-sm text-gray-500">Total Points Used</div>
          {fetchingDistributionData ? (
            <Skeleton className="h-10 mt-1" />
          ) : (
            <div className="text-2xl font-semibold mt-1">{totalPointsUsed}</div>
          )}
        </div>
        <div className="p-4 border rounded-lg bg-white">
          <div className="text-sm text-gray-500">
            Estimated Retail Value Distributed
          </div>
          {fetchingDistributionData ? (
            <Skeleton className="h-10 mt-1" />
          ) : (
            <div className="text-2xl font-semibold mt-1">
              ${totalValue.toFixed(2)}
            </div>
          )}
        </div>
        <div className="p-4 border rounded-lg bg-white">
          <div className="text-sm text-gray-500">Checkouts</div>
          {fetchingDistributionData ? (
            <Skeleton className="h-10 mt-1" />
          ) : (
            <div className="text-2xl font-semibold mt-1">{checkoutsCount}</div>
          )}
        </div>
      </div>
      <DataTable
        headers={headers}
        data={filteredData}
        renderRow={renderRow}
        isLoading={fetchingDistributionData}
      />
    </div>
  );
}
