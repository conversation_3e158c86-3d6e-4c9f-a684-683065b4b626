import { blockContentNormalStyle, title,primaryButton, secondaryButton } from "../../../common/fields";
import { hideIfVariantIn } from "@webriq-pagebuilder/sanity-plugin-schema-default";

export const textComponentSchema = [
  title(),
  blockContentNormalStyle(
    "firstColumn",
    "Content",
    "Add text content in a single column."
  ),

  blockContentNormalStyle(
    "secondColumn",
    "Second Column",
    "Add content in the second column.", 
    hideIfVariantIn([
      "variant_a",
      "variant_d",
      "variant_e",
      "variant_f",
      "variant_h",
      "variant_j",
      "variant_k",
      "variant_n",
      "variant_o",
    ])
  ),

  blockContentNormalStyle(
    "thirdColumn",
    "Third Column",
    "Add content in the third column.",
    hideIfVariantIn([
      "variant_a",
      "variant_b",
      "variant_d",
      "variant_e",
      "variant_f",
      "variant_g",
      "variant_h",
      "variant_i",
      "variant_j",
      "variant_k",
      "variant_l",
      "variant_n",
      "variant_o",
    ])
  ),
  
  blockContentNormalStyle(
    "fourthColumn",
    "Fourth Column",
    "Add content in the third column.",
    hideIfVariantIn([
      "variant_a",
      "variant_b",
      "variant_c",
      "variant_d",
      "variant_e",
      "variant_f",
      "variant_g",
      "variant_h",
      "variant_i",
      "variant_j",
      "variant_k",
      "variant_l",
      "variant_n",
      "variant_o",
    ])
  ),

  primaryButton(hideIfVariantIn([
    "variant_a",
    "variant_b",
    "variant_d",
    "variant_e",
    "variant_f",
    "variant_g",
    "variant_h",
    "variant_i",
    "variant_j",
    "variant_k",
    "variant_l",
    "variant_m",
    "variant_n",
    "variant_o",
  ])),

  secondaryButton(hideIfVariantIn([
    "variant_a",
    "variant_b",
    "variant_d",
    "variant_e",
    "variant_f",
    "variant_g",
    "variant_h",
    "variant_i",
    "variant_j",
    "variant_k",
    "variant_l",
    "variant_m",
    "variant_n",
    "variant_o",
  ]))
];
