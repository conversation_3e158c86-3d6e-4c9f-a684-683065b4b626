import {
  checkEmailExist,
  checkUsernameExist,
  insertData,
} from "helper/supabase";
import { supabaseAdmin } from "utils/supabase/admin";
import { supabaseClient } from "utils/supabase/client";

export default async function POST(req, res) {
  try {
    const {
      firstName,
      lastName,
      middleInitial,
      email,
      address,
      secondaryAddress,
      city,
      state,
      zipCode,
      contactInfo,
      isHeadOfHousehold,
      role,
      password,
      amountPaid,
      paymentOption,
      username,
      annualIncome,
      householdSize,
      totalPoints,
      authorizedShoppers,
      addedBy,
      added_store_location,
      householdNotes,
      householdMembers,
      ageGroups,
      personalInfo,
      preferredLanguage,
      housingType,
    } = req.body;

    const { data: userData, error: userError } = await supabaseClient
      .from("users")
      .select("id, role")
      .eq("user_auth_id", addedBy)
      .single();

    if (userError) {
      return res.status(404).json({
        message: "User not found",
      });
    }

    if (!userData || !["staff", "admin"].includes(userData.role)) {
      return res.status(403).json({
        message: "Unauthorized. Only staff and admin can create user data.",
      });
    }

    const { data: memberData, error: memberError } = await supabaseClient
      .from("members")
      .select("id")
      .eq("user_id", userData.id)
      .single();

    if (memberError) {
      return res.status(404).json({
        message: "Member not found",
      });
    }

    const isHead = isHeadOfHousehold === "no" ? false : true;
    const startDate = new Date();
    const endDate = new Date(startDate);
    endDate.setMonth(startDate.getMonth() + 1);
    const noPaymentRequired = [
      "staff",
      "admin",
      "volunteer",
      "member",
    ].includes(role);
    const emailExist = await checkEmailExist(email);
    const usernameExist = await checkUsernameExist(username);

    if (emailExist?.error) {
      console.error("Email already exist");
      return res.status(500).json({
        message: emailExist?.error,
      });
    }

    if (usernameExist?.error) {
      console.error("Username already exist");
      return res.status(500).json({
        message: usernameExist?.error,
      });
    }

    const { data: signUpData, error: signUpError } =
      await supabaseAdmin.createUser({
        email: email,
        password,
        user_metadata: { role: role, username: username },
        email_confirm: true,
      });

    if (signUpError) {
      console.error("Sign up error:", signUpError.message);
      return res.status(500).json({
        message: "Failed to create user.",
      });
    }

    const insertUser = await insertData("users", {
      first_name: firstName,
      last_name: lastName,
      email: email,
      role: role,
      username: username,
      user_auth_id: signUpData?.user?.id,
      status: "active",
    });

    const insertMember = await insertData("members", {
      first_name: firstName,
      last_name: lastName,
      middle_initial: middleInitial,
      address: address,
      secondary_address: secondaryAddress,
      city: city,
      state: state,
      zip_code: zipCode,
      contact_info: contactInfo,
      head_of_household: isHead,
      user_id: insertUser?.[0]?.id,
      household_size: householdSize,
      annual_income: annualIncome,
      authorized_shopper: authorizedShoppers.split(","),
    });

    // Only insert memberships and payments for regular members
    if (!noPaymentRequired) {
      const insertMemberships = await insertData("memberships", {
        member_id: insertMember?.[0]?.id,
        start_date: startDate,
        end_date: endDate,
        status: "active",
        total_paid: amountPaid,
        total_points: totalPoints,
        available_points: totalPoints,
        added_by: memberData?.id,
        added_store_location,
      });

      const insertPayments = await insertData("payments", {
        membership_id: insertMemberships?.[0]?.id,
        date: startDate,
        method: paymentOption,
        amount: amountPaid,
        added_store_location,
      });
    }

    if (role === "member") {
      const insertAdditionalInfo = await insertData("additional_info", {
        member_id: insertMember?.[0]?.id,
        household_members: householdMembers,
        age_groups: ageGroups,
        household_note_verification: householdNotes,
        personal_info: personalInfo,
        preferred_language: preferredLanguage,
        housing_type: housingType,
      });
    }

    return res.status(200).json({
      message: "Signup successful!",
    });
  } catch (error) {
    console.error(error);

    return res.status(500).json({
      message: "Failed to process the request.",
    });
  }
}
