import React from "react";
import dynamic from "next/dynamic";
import {
  SectionsProps,
  Images,
  MainImage,
  LabeledRouteWithKey,
  Form,
  Template,
  ArrayOfVideos,
} from "../../../types";
import * as HeaderVariants from "@stackshift-ui/header";
import { PortableTextBlock } from "@portabletext/types";

const Variants = {
  variant_a: HeaderVariants.Header_A,
  variant_b: HeaderVariants.Header_B,
  variant_c: HeaderVariants.Header_C,
  variant_d: HeaderVariants.Header_D,
  variant_e: HeaderVariants.Header_E,
  variant_g: dynamic(() => import("./variant_g")),
  variant_h: dynamic(() => import("./variant_h")),
  variant_i: dynamic(() => import("./variant_i")),
  variant_j: dynamic(() => import("./variant_j")),
  variant_k: dynamic(() => import("./variant_k")),
  variant_l: dynamic(() => import("./variant_l")),
  variant_m: dynamic(() => import("./variant_m")),
  variant_n: dynamic(() => import("./variant_n")),
  variant_o: dynamic(() => import("./variant_o")),
  variant_p: dynamic(() => import("./variant_p")),
  variant_q: dynamic(() => import("./variant_q")),
  variant_r: dynamic(() => import("./variant_r")),
};

export interface ButtonProps {
  as?: string;
  label?: string;
  link?: {
    target?: string;
    route?: string;
  };
  ariaLabel?: string;
  variant?: string;
}

export interface HeaderProps {
  template?: Template;
  mainImage?: MainImage;
  bgImage?: bgImage;
  firstColumn?: PortableTextBlock[];
  images?: Images[];
  title?: string;
  videos?: ArrayOfVideos[];
  description?: string;
  primaryButton?: ButtonProps;
  secondaryButton?: ButtonProps;
  videoLink?: string;
  formLinks?: LabeledRouteWithKey[];
  form?: Form;
}

const displayName = "Header";

export const Header: React.FC<SectionsProps> = ({ data }) => {
  const variant = data?.variant;
  const Variant = variant && Variants[variant as keyof typeof Variants];

  const props = {
    mainImage: data?.variants?.mainImage ?? undefined,
    videos: data?.variants?.videos ?? undefined,
    bgImage: data?.variants?.bgImage ?? undefined,
    firstColumn: data?.variants?.firstColumn ?? undefined,
    secondColumn: data?.variants?.secondColumn ?? undefined,
    images: data?.variants?.images ?? undefined,
    title: data?.variants?.title ?? undefined,
    description: data?.variants?.description ?? undefined,
    primaryButton: data?.variants?.primaryButton ?? undefined,
    secondaryButton: data?.variants?.secondaryButton ?? undefined,
    videoLink: data?.variants?.youtubeLink ?? undefined,
    formLinks: data?.variants?.formLinks ?? undefined,
    form: data?.variants?.form ?? undefined,
  };

  return Variant ? <Variant {...props} /> : null;
};

Header.displayName = displayName;
