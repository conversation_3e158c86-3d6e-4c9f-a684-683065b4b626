"use client";

import React from "react";
import ReactCalendar from "react-calendar";
import { cn } from "utils/cn";
import "react-calendar/dist/Calendar.css";

export type CalendarProps = {
  value?: Date | Date[] | null;
  onChange?: (value: Date | Date[]) => void;
  onClickDay?: (value: Date) => void;
  className?: string;
  selectRange?: boolean;
  showNeighboringMonth?: boolean;
  locale?: string;
};

function Calendar({
  value,
  onChange,
  onClickDay,
  className,
  selectRange = false,
  showNeighboringMonth = true,
  locale = "en-US",
  ...props
}: CalendarProps) {
  return (
    <div className={cn("react-calendar-wrapper", className)}>
      <ReactCalendar
        value={value}
        onChange={onChange}
        onClickDay={onClickDay}
        selectRange={selectRange}
        showNeighboringMonth={showNeighboringMonth}
        locale={locale}
        prev2Label={null}
        next2Label={null}
        {...props}
      />
    </div>
  );
}

Calendar.displayName = "Calendar";

export { Calendar };
