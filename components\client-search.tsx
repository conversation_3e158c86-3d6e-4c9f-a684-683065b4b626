import React, { useState } from "react";
import { X, Search } from "lucide-react";
import { Input } from "../components/ui/input";
import { CustomButton } from "components/ui/custom-button";
import {
  formatDate,
  getCurrentActiveMembership,
  getInitials,
  getNoteStyles,
  isMembershipActive,
} from "utils";
import { useAlert } from "context/useAlert";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { useAuthCheck } from "hooks/useAuthCheck";
import { useSelectClient } from "context/useSelectClientContext";
import { CheckoutStatusType } from "types";
import ConfirmationModal from "./dashboard/confirmation-modal";
import { useClientSearch } from "context/useClientSearchContext";
import { useCheckedInToday } from "context/useCheckedInToday";
import { useLocation } from "hooks/useLocation";

export default function ClientSearch({
  isStaff,
  isModal = false,
  onClose,
  isCheckInPage = false,
}: {
  isStaff: boolean;
  isModal: boolean;
  onClose?: () => void;
  isCheckInPage?: boolean;
}) {
  const { showAlert } = useAlert();
  const { user, token } = isStaff
    ? useAuthCheck()
    : { user: null, token: null };
  const { selectedClient, setSelectedClient, refreshSelectedClient } =
    useSelectClient();
  const {
    searchResults,
    isSearching,
    searchQuery,
    setSearchQuery,
    fetchSearchResults,
  } = useClientSearch();
  const { refreshData: refreshCheckedInToday } = useCheckedInToday();

  const [open, setOpen] = useState(true);
  const [showForceCheckInModal, setShowForceCheckInModal] = useState(false);
  const [loading, setLoading] = useState(false);
  const [selectedSearchClient, setSelectedSearchClient] = useState(null);
  const { city, region, country, error } = useLocation();

  const handleSearch = (query: string) => {
    setSearchQuery({ query, role: user?.user_metadata?.role });
    setSelectedClient(null);
    setSelectedSearchClient(null);
  };

  const clearSearch = () => {
    setSearchQuery({ query: "", role: user?.user_metadata?.role });
    setSelectedClient(null);
    setSelectedSearchClient(null);
  };

  const selectSearchClient = (client) => {
    setSelectedSearchClient(client);
  };

  const handleOpenClient = () => {
    isModal ? (onClose ? onClose() : null) : null;
    if (selectedSearchClient) {
      setSelectedClient(selectedSearchClient);
      setSelectedSearchClient(null);
    }
  };

  const handleCheckIn = async ({ forceCheckIn = false }) => {
    setLoading(true);
    if (selectedClient !== null) {
      const response = await fetch("/api/user-data/user-checkin", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({
          member_id: selectedClient?.id,
          checked_in_by: user?.id,
          is_forced_checked_in: forceCheckIn,
          added_store_location: !error
            ? `${city}, ${region}, ${country}`
            : error,
        }),
      });

      const result = await response.json();

      if (response.ok) {
        showAlert(result.message, "success");
        if (isModal && onClose) {
          onClose();
        }
      } else {
        showAlert(result.message, "error");
        if (isModal && onClose) {
          onClose();
        }
      }

      await fetchSearchResults();
      await refreshSelectedClient();
      await refreshCheckedInToday();
      setLoading(false);
    }
  };

  if (!isModal) {
    return (
      <RegularClientSearch
        loading={loading}
        searchQuery={searchQuery}
        selectedClient={selectedClient}
        selectedSearchClient={selectedSearchClient}
        searchResults={searchResults}
        isSearching={isSearching}
        handleSearch={handleSearch}
        clearSearch={clearSearch}
        selectSearchClient={selectSearchClient}
        handleOpenClient={handleOpenClient}
        handleCheckIn={handleCheckIn}
        isStaff={isStaff}
        setShowForceCheckInModal={setShowForceCheckInModal}
        user={user}
        setSelectedClient={setSelectedClient}
        setSelectedSearchClient={setSelectedSearchClient}
        isCheckInPage={isCheckInPage}
      />
    );
  }

  return (
    <Dialog
      open={open}
      onOpenChange={(isOpen) => {
        setOpen(isOpen);
        if (!isOpen && onClose) onClose();
      }}
    >
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader className="bg-blue-600 text-white p-4 -mx-[25px] -mt-7 flex flex-row justify-between items-center rounded-t-lg">
          <DialogTitle className="text-xl">Search</DialogTitle>
        </DialogHeader>
        <ClientSearchInput
          searchQuery={searchQuery.query}
          clearSearch={clearSearch}
          handleSearch={handleSearch}
        />
        <ClientSearchResults
          filteredClients={searchResults}
          selectedClient={selectedClient}
          selectedSearchClient={selectedSearchClient}
          selectSearchClient={selectSearchClient}
          handleOpenClient={handleOpenClient}
          isSearching={isSearching}
          user={user}
          handleCheckIn={handleCheckIn}
          setShowForceCheckInModal={setShowForceCheckInModal}
          loading={loading}
          isStaff={isStaff}
          setSelectedClient={setSelectedClient}
          setSelectedSearchClient={setSelectedSearchClient}
          isCheckInPage={isCheckInPage}
        />

        {!isSearching && (
          <NoClientFound
            searchQuery={searchQuery.query}
            filteredClients={searchResults}
          />
        )}
        <ConfirmationModal
          isOpen={showForceCheckInModal}
          message={`Are you sure you want to force check-in "${selectedClient?.first_name} ${selectedClient?.last_name}" ?`}
          onConfirm={() => handleCheckIn({ forceCheckIn: true })}
          onCancel={() => setShowForceCheckInModal(false)}
          isLoading={loading}
          isLoadingText="Force checking in..."
        />
      </DialogContent>
    </Dialog>
  );
}

function RegularClientSearch({
  searchQuery,
  selectedClient,
  selectedSearchClient,
  searchResults,
  isSearching,
  handleSearch,
  clearSearch,
  selectSearchClient,
  handleOpenClient,
  handleCheckIn,
  isStaff,
  setShowForceCheckInModal,
  loading,
  user,
  setSelectedClient,
  setSelectedSearchClient,
  isCheckInPage,
}) {
  return (
    <div className="bg-white w-full mx-auto rounded-lg shadow-sm border">
      <div className="border-b p-6">
        <ClientSearchInput
          searchQuery={searchQuery.query}
          clearSearch={clearSearch}
          handleSearch={handleSearch}
        />
      </div>

      <ClientSearchResults
        filteredClients={searchResults}
        selectedClient={selectedClient}
        selectedSearchClient={selectedSearchClient}
        selectSearchClient={selectSearchClient}
        handleOpenClient={handleOpenClient}
        isSearching={isSearching}
        user={user}
        handleCheckIn={handleCheckIn}
        setShowForceCheckInModal={setShowForceCheckInModal}
        loading={loading}
        isStaff={isStaff}
        setSelectedClient={setSelectedClient}
        setSelectedSearchClient={setSelectedSearchClient}
        isCheckInPage={isCheckInPage}
      />

      {!isSearching && searchQuery.query && searchResults.length === 0 && (
        <div className="flex items-center justify-center h-[400px]">
          <NoClientFound
            searchQuery={searchQuery.query}
            filteredClients={searchResults}
          />
        </div>
      )}
    </div>
  );
}

function ClientSearchInput({ searchQuery, clearSearch, handleSearch }) {
  return (
    <div className="space-y-2">
      <h3 className="text-lg font-medium">Client Search</h3>
      <div className="relative">
        <Input
          type="text"
          placeholder="Search clients..."
          className="w-full pr-10 h-12 text-base shadow-sm focus-visible:ring-2 focus-visible:ring-blue-500"
          value={searchQuery}
          onChange={(e) => handleSearch(e.target.value)}
        />
        {searchQuery && (
          <button
            className="absolute right-3 top-1/2 -translate-y-1/2 hover:bg-gray-100 rounded-full p-1"
            onClick={clearSearch}
          >
            <X className="h-5 w-5 text-gray-600" />
          </button>
        )}
      </div>
      <p className="text-sm text-muted-foreground">
        Enter at least 3 characters of a client's address
      </p>
    </div>
  );
}

function ClientSearchResults({
  filteredClients,
  selectedClient,
  selectedSearchClient,
  selectSearchClient,
  handleOpenClient,
  isSearching,
  user,
  handleCheckIn,
  setShowForceCheckInModal,
  loading,
  isStaff,
  setSelectedClient,
  setSelectedSearchClient,
  isCheckInPage,
}) {
  if (isSearching && isStaff) {
    return (
      <div className="text-center py-8">
        <p>Searching...</p>
      </div>
    );
  }

  // If a client is selected (opened), show only that client
  const clients = selectedClient ? [selectedClient] : filteredClients;

  // For the opened client, show the detailed view with check-in functionality
  if (selectedClient) {
    const currentActiveMembership = getCurrentActiveMembership(
      selectedClient?.memberships
    );

    const isCheckInEnabled =
      isMembershipActive(
        currentActiveMembership?.start_date,
        currentActiveMembership?.end_date
      ) && currentActiveMembership?.status === "active";

    const isCheckInDisabled =
      selectedClient?.checkoutStatus === "has-checked-in" ||
      selectedClient?.checkoutStatus === "already-checked-out" ||
      (currentActiveMembership
        ? currentActiveMembership?.status !== "active"
        : true) ||
      !isCheckInEnabled ||
      loading;

    const displayForceCheckin =
      ["has-checked-in", "already-checked-out"].includes(
        selectedClient?.checkoutStatus
      ) &&
      selectedClient !== null &&
      (currentActiveMembership
        ? currentActiveMembership?.available_points > 0
        : false);

    return (
      <div className="overflow-hidden bg-white">
        <div className="max-h-[400px] overflow-y-auto">
          {renderClientCard(selectedClient, null, false)}
        </div>

        {isStaff && (
          <div className="bg-gray-50 px-3 py-2 border-t flex justify-end gap-2">
            <CustomButton
              variant="outline"
              onClick={() => setSelectedClient(null)}
              className="hover:bg-gray-100 h-8 text-sm flex items-center"
            >
              Cancel
            </CustomButton>
            {!displayForceCheckin && isCheckInPage && (
              <CustomButton
                variant={isCheckInEnabled ? "default" : "outline"}
                onClick={() => handleCheckIn({ forceCheckIn: false })}
                disabled={isCheckInDisabled}
              >
                {loading
                  ? "Checking in..."
                  : isCheckInEnabled
                  ? "Check In"
                  : "Check In Not Available"}
              </CustomButton>
            )}

            {displayForceCheckin && isCheckInPage && (
              <CustomButton
                variant="default"
                onClick={() => setShowForceCheckInModal(true)}
                disabled={loading}
              >
                Force Check In
              </CustomButton>
            )}
          </div>
        )}
      </div>
    );
  }

  return (
    <div className="overflow-hidden bg-white">
      <div className="max-h-[400px] overflow-y-auto">
        {clients.map((client) => (
          <div key={client.id}>
            {renderClientCard(
              client,
              selectedSearchClient,
              true,
              selectSearchClient
            )}
          </div>
        ))}
      </div>

      {isStaff && selectedSearchClient && (
        <div className="bg-gray-50 px-3 py-2 border-t flex justify-end gap-2">
          <CustomButton
            variant="outline"
            onClick={() => setSelectedSearchClient(null)}
            className="hover:bg-gray-100 h-8 text-sm flex items-center"
          >
            Cancel
          </CustomButton>
          <CustomButton
            variant="default"
            onClick={handleOpenClient}
            className="h-8 text-sm flex items-center"
          >
            Open
          </CustomButton>
        </div>
      )}
    </div>
  );
}

function renderClientCard(
  client,
  selectedSearchClient,
  isSelectable = false,
  selectSearchClient: ((client: any) => void) | null = null
) {
  const clientActiveMembership = getCurrentActiveMembership(
    client?.memberships
  );

  const startDate =
    client?.memberships?.length > 0
      ? formatDate(
          client.memberships?.length > 1
            ? clientActiveMembership?.start_date
            : client.memberships[0]?.start_date
        )
      : "N/A";

  const endDate =
    client?.memberships?.length > 0
      ? formatDate(
          client.memberships?.length > 1
            ? clientActiveMembership?.end_date
            : client.memberships[0]?.end_date
        )
      : "N/A";

  const usedPoints =
    clientActiveMembership?.used_points ??
    client?.memberships?.[0]?.used_points ??
    0;

  const totalPoints =
    clientActiveMembership?.total_points ??
    client?.memberships?.[0]?.total_points ??
    0;

  const availablePoints =
    clientActiveMembership?.available_points ??
    client?.memberships?.[0]?.available_points ??
    0;

  const isSelected = selectedSearchClient?.id === client.id;

  return (
    <div
      className={`border-b mb-5 last:border-b-0 transition-colors ${
        isSelectable
          ? `cursor-pointer ${
              isSelected ? "bg-blue-50 border-blue-200" : "hover:bg-gray-100"
            }`
          : ""
      }`}
      onClick={() =>
        isSelectable && selectSearchClient && selectSearchClient(client)
      }
    >
      <div
        className={`text-white px-4 py-3 flex justify-between items-center ${
          isSelected ? "bg-blue-600" : "bg-gray-500"
        }`}
      >
        <p className="text-lg font-semibold">
          {client.first_name} {client.last_name}
        </p>
        {client?.additional_info?.preferred_language && (
          <span className="bg-blue-50 text-gray-800 text-xs font-medium px-2 py-1 rounded">
            {client.additional_info.preferred_language.charAt(0).toUpperCase() +
              client.additional_info.preferred_language.slice(1)}
          </span>
        )}
      </div>

      <div className="p-4 grid grid-cols-3 gap-4 text-sm">
        <div>
          <p className="font-semibold">
            Client #: <span className="font-normal">{client.id}</span>
          </p>
          <p>Phone 1: {client.contact_info.primary_number}</p>
          <p>{client.address}</p>
        </div>

        <div>
          <p className="font-semibold">Membership</p>
          <p>
            Start: {startDate}
            <br />
            End: {endDate}
            <br />
            Used {usedPoints} of {totalPoints} points
          </p>
          <div className="w-full mt-2 bg-gray-200 rounded-full h-1">
            <div
              className="bg-blue-500 h-1 rounded-full"
              style={{
                width: `${(usedPoints / totalPoints) * 100}%`,
              }}
            />
          </div>
        </div>

        <div>
          <p className="font-semibold">Authorized Shoppers</p>
          {Array.isArray(JSON.parse(client.authorized_shopper)) &&
            JSON.parse(client.authorized_shopper).filter(
              (shopper) => shopper.trim() !== ""
            ).length > 0 && (
              <p className="text-sm text-gray-600">
                {JSON.parse(client.authorized_shopper)
                  .filter((shopper) => shopper.trim() !== "")
                  .join(", ")}
              </p>
            )}
        </div>
      </div>

      {client.notes?.[0]?.description && (
        <div
          className={`rounded-lg p-2.5 ${getNoteStyles(
            client?.checkoutStatus
          )}`}
        >
          <div className="flex items-start gap-2">
            <div className="flex-1">
              <h4 className="text-xs font-medium text-amber-800">
                Important Note
              </h4>
              <p className="text-amber-700 text-sm">
                <div
                  dangerouslySetInnerHTML={{
                    __html: client.notes[0].description,
                  }}
                  className="prose max-w-none"
                />
              </p>
            </div>
            <div className="flex flex-col items-end text-xs">
              <span className="font-medium bg-amber-200 text-amber-800 px-1.5 py-0.5 rounded">
                {getInitials(client.notes[0].modified_by)}
              </span>
              <span className="text-amber-600 mt-0.5">
                {formatDate(client.notes[0].created_at)}
              </span>
            </div>
          </div>
        </div>
      )}

      <StatusBadge
        checkoutStatus={client?.checkoutStatus}
        hasAvailablePoints={availablePoints > 0}
      />
    </div>
  );
}

function StatusBadge({
  checkoutStatus,
  hasAvailablePoints,
}: {
  checkoutStatus?: CheckoutStatusType;
  hasAvailablePoints?: boolean;
}) {
  return getMembershipStatusMessage(checkoutStatus, hasAvailablePoints);
}

function NoClientFound({ searchQuery, filteredClients }) {
  return (
    <React.Fragment>
      {searchQuery?.length >= 3 && filteredClients.length === 0 && (
        <div className="text-center py-8">
          <Search className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-semibold text-gray-900">
            No clients found
          </h3>
          <p className="mt-1 text-sm text-gray-500">
            Try adjusting your search terms.
          </p>
        </div>
      )}
    </React.Fragment>
  );
}

function getMembershipStatusMessage(
  checkoutStatus?: string,
  hasAvailablePoints?: boolean
) {
  const statusConfig: Record<string, { bgColor: string; message: string }> = {
    "no-membership": { bgColor: "bg-gray-500", message: "No membership found" },
    expired: {
      bgColor: "bg-red-500",
      message: "Membership subscription expired",
    },
    inactive: {
      bgColor: "bg-orange-500",
      message: hasAvailablePoints
        ? "Membership inactive but has available points"
        : "Membership inactive - No available points",
    },
    "not-checked-in": {
      bgColor: "bg-blue-500",
      message: "Member has not checked in yet. Send to check-in desk",
    },
    "has-checked-in": {
      bgColor: "bg-green-500",
      message: "Member has checked in and is ready to check out",
    },
    "already-checked-out": {
      bgColor: "bg-yellow-500",
      message:
        "Member has already checked out today. Members can only check out once per day",
    },
  };

  const { bgColor, message } = statusConfig[checkoutStatus || ""] || {
    bgColor: "bg-gray-500",
    message: "Membership status unknown",
  };

  return (
    <div
      className={`${bgColor} text-white text-center p-2 text-md font-semibold`}
    >
      {message}
    </div>
  );
}
