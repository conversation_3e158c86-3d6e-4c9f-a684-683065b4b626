import React from "react";
import { CustomButton } from "../../ui/custom-button";

export interface ProductCategory {
  id: string;
  name: string;
  pointValue: number;
  limit?: number;
}

interface ProductCategoriesProps {
  categories: ProductCategory[];
  onSelectCategory: (category: ProductCategory) => void;
}

export function ProductCategories({
  categories,
  onSelectCategory,
}: ProductCategoriesProps) {
  return (
    <div className="grid grid-cols-4 gap-4">
      {categories?.map((category) => (
        <CustomButton
          key={category.id}
          variant="outline"
          className="h-24 flex flex-col items-center justify-center text-center p-2"
          onClick={() => onSelectCategory(category)}
        >
          <span className="font-bold">{category.name}</span>
          <span className="text-sm">{category.pointValue} points</span>
          {category.limit && (
            <span className="text-xs text-muted-foreground">
              Limit: {category.limit}
            </span>
          )}
        </CustomButton>
      ))}
    </div>
  );
}
