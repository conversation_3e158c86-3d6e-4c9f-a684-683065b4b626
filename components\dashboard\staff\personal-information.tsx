import { SelectItem } from "@/components/ui/select";
import { SelectContent } from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { Select, SelectValue, SelectTrigger } from "@/components/ui/select";
import { Skeleton } from "@/components/ui/skeleton";
import React, { useEffect, useState } from "react";
import { useSelectClient } from "context/useSelectClientContext";
import { useClientSearch } from "context/useClientSearchContext";
import { genderTypes, raceTypes } from "utils";

export default function PersonalInformation({ isDisabled }) {
  const { selectedClient } = useSelectClient();
  const { isSearching } = useClientSearch();
  const [personalInfo, setPersonalInfo] = useState(
    selectedClient?.additional_info?.personal_info || {}
  );

  useEffect(() => {
    if (selectedClient?.additional_info?.personal_info) {
      setPersonalInfo(selectedClient.additional_info.personal_info);
    }
  }, [selectedClient]);

  return (
    <div className="space-y-4">
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
        <div className="space-y-2">
          <Label htmlFor="hispanic">Hispanic</Label>
          {isSearching ? (
            <Skeleton className="w-full h-9" />
          ) : (
            <Select
              value={personalInfo.hispanic?.toString() ?? "false"}
              onValueChange={(value) =>
                setPersonalInfo({ ...personalInfo, hispanic: value })
              }
              disabled={isDisabled}
              name="hispanic"
            >
              <SelectTrigger>
                <SelectValue placeholder="Select option" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="true">Yes</SelectItem>
                <SelectItem value="false">No</SelectItem>
              </SelectContent>
            </Select>
          )}
        </div>

        <div className="space-y-2">
          <Label htmlFor="race">Race</Label>
          {isSearching ? (
            <Skeleton className="w-full h-9" />
          ) : (
            <Select
              value={personalInfo.race || raceTypes[0].toLowerCase()}
              onValueChange={(value) =>
                setPersonalInfo({ ...personalInfo, race: value })
              }
              name="race"
              disabled={isDisabled}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select race" />
              </SelectTrigger>
              <SelectContent>
                {raceTypes.map((race) => (
                  <SelectItem key={race} value={race.toLowerCase()}>
                    {race}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          )}
        </div>

        <div className="space-y-2">
          <Label htmlFor="gender">Gender</Label>
          {isSearching ? (
            <Skeleton className="w-full h-9" />
          ) : (
            <Select
              value={personalInfo.gender || genderTypes[0].toLowerCase()}
              onValueChange={(value) =>
                setPersonalInfo({ ...personalInfo, gender: value })
              }
              name="gender"
              disabled={isDisabled}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select gender" />
              </SelectTrigger>
              <SelectContent>
                {genderTypes.map((gender) => (
                  <SelectItem key={gender} value={gender.toLowerCase()}>
                    {gender}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          )}
        </div>

        <div className="space-y-2">
          <Label htmlFor="disabled">Disabled</Label>
          {isSearching ? (
            <Skeleton className="w-full h-9" />
          ) : (
            <Select
              value={personalInfo.disabled?.toString() ?? "false"}
              onValueChange={(value) =>
                setPersonalInfo({ ...personalInfo, disabled: value })
              }
              name="disabled"
              disabled={isDisabled}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select option" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="true">Yes</SelectItem>
                <SelectItem value="false">No</SelectItem>
              </SelectContent>
            </Select>
          )}
        </div>

        <div className="space-y-2">
          <Label htmlFor="veteran">Veteran</Label>
          {isSearching ? (
            <Skeleton className="w-full h-9" />
          ) : (
            <Select
              value={personalInfo.veteran?.toString() ?? "true"}
              onValueChange={(value) =>
                setPersonalInfo({ ...personalInfo, veteran: value })
              }
              name="veteran"
              disabled={isDisabled}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select option" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="true">Yes</SelectItem>
                <SelectItem value="false">No</SelectItem>
              </SelectContent>
            </Select>
          )}
        </div>
      </div>
    </div>
  );
}
