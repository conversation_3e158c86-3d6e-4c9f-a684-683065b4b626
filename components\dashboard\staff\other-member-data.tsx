import ReadOnly<PERSON>ield from "@/components/ui/read-only-field";
import { useClientSearch } from "context/useClientSearchContext";
import { useSelectClient } from "context/useSelectClientContext";
import React from "react";
import { formatDate } from "utils";
import { calculateMembershipPrice } from "utils/membership-calculator";

export default function OtherMemberData() {
  const { selectedClient } = useSelectClient();
  const { isSearching } = useClientSearch();

  const firstOrder = selectedClient?.orders?.[0];
  const lastOrder = selectedClient?.orders?.[selectedClient.orders.length - 1];

  const membershipFee = calculateMembershipPrice(
    selectedClient?.household_size || 1,
    selectedClient?.annual_income || 0
  )[0];

  const couponRedeemed =
    selectedClient?.memberships
      ?.flatMap((membership) => membership.payments || [])
      ?.find(
        (payment) =>
          payment?.coupons?.coupon_code && payment?.coupons?.referred_by
      ) || null;

  const fields = [
    [
      {
        label: "Client ID",
        value: selectedClient?.id ?? "N/A",
      },
      {
        label: "Last Updated",
        value: formatDate(
          selectedClient?.updated_at ?? selectedClient?.created_at
        ),
      },
    ],
    [
      {
        label: "Monthly Membership Fee",
        value: `${membershipFee ? `$${membershipFee}` : "N/A"}`,
      },
      {
        label: "Status",
        value:
          selectedClient?.status?.charAt(0).toUpperCase() +
            selectedClient?.status?.slice(1) || "N/A",
      },
    ],
    [
      {
        label: "Coupon Redeemed Date",
        value: formatDate(couponRedeemed?.date) || "N/A",
      },
      {
        label: "Coupon Code",
        value: couponRedeemed?.coupons?.coupon_code || "N/A",
      },
    ],
    [
      {
        label: "First Order Date",
        value: formatDate(firstOrder?.date_of_order),
      },
      {
        label: "Last Order Date",
        value: formatDate(lastOrder?.date_of_order),
      },
    ],
    [
      {
        label: "Date Created",
        value: formatDate(selectedClient?.created_at),
      },
      {
        label: "Last Verified",
        value: formatDate(selectedClient?.last_verified_at),
      },
    ],
    [
      {
        label: "Referred by",
        value: couponRedeemed?.coupons?.referred_by || "N/A",
      },
    ],
  ];

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
      {fields.map((row, rowIndex) => (
        <React.Fragment key={rowIndex}>
          {row.map((field, fieldIndex) => (
            <ReadOnlyField
              key={`${rowIndex}-${fieldIndex}`}
              label={field.label}
              value={field.value}
              isLoading={isSearching}
            />
          ))}
        </React.Fragment>
      ))}
    </div>
  );
}
