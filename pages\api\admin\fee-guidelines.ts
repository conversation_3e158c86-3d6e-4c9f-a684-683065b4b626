import { NextApiRequest, NextApiResponse } from "next";
import { supabaseClient } from "utils/supabase/client";
import { createServerSupabaseClient } from "@supabase/auth-helpers-nextjs";

async function withAuth(
  req: NextApiRequest,
  res: NextApiResponse,
  handler: (req: NextApiRequest, res: NextApiResponse) => Promise<void>
) {
  try {
    const supabase = createServerSupabaseClient({ req, res });
    const {
      data: { session },
    } = await supabase.auth.getSession();

    if (!session) {
      return res.status(401).json({
        error: true,
        message: "Unauthorized - Please login",
      });
    }

    const userRole = session.user.user_metadata.role;

    if (!userRole || !["admin", "staff"].includes(userRole)) {
      return res.status(403).json({
        error: true,
        message: "Forbidden - Admin or Staff access required",
      });
    }

    if (req.method === "DELETE" && userRole !== "admin") {
      return res.status(403).json({
        error: true,
        message: "Forbidden - Only admins can delete guidelines",
      });
    }

    return handler(req, res);
  } catch (error) {
    console.error("Auth error:", error);
    return res.status(500).json({
      error: true,
      message: "Internal server error",
    });
  }
}

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  return withAuth(req, res, async (req, res) => {
    switch (req.method) {
      case "GET":
        try {
          const { data: guidelines, error: guidelinesError } =
            await supabaseClient
              .from("fee_guidelines")
              .select("*")
              .order("household_size", { ascending: true });

          if (guidelinesError) {
            console.error("Error fetching guidelines:", guidelinesError);
            return res.status(500).json({
              error: true,
              message: "Error fetching guidelines",
            });
          }

          return res.status(200).json(guidelines || []);
        } catch (error) {
          console.error("Error in GET guidelines:", error);
          return res.status(500).json({
            error: true,
            message: "Error fetching guidelines",
          });
        }

      case "POST":
        try {
          const {
            household_size,
            household_points,
            lower_threshold,
            middle_threshold,
            upper_threshold,
            lower_price,
            middle_price,
            upper_price,
          } = req.body;

          if (
            !household_size ||
            !household_points ||
            !lower_threshold ||
            !middle_threshold ||
            !upper_threshold ||
            !lower_price ||
            !middle_price ||
            !upper_price
          ) {
            return res.status(400).json({
              error: true,
              message: "Missing required fields",
            });
          }

          const { data: guidelineData, error: guidelineError } =
            await supabaseClient
              .from("fee_guidelines")
              .insert([
                {
                  household_size: parseInt(household_size),
                  household_points: parseInt(household_points),
                  lower_threshold: parseFloat(lower_threshold),
                  middle_threshold: parseFloat(middle_threshold),
                  upper_threshold: parseFloat(upper_threshold),
                  lower_price: parseFloat(lower_price),
                  middle_price: parseFloat(middle_price),
                  upper_price: parseFloat(upper_price),
                },
              ])
              .select()
              .single();

          if (guidelineError) {
            console.error("Error adding guideline:", guidelineError);
            return res.status(500).json({
              error: true,
              message: guidelineError.message || "Error adding guideline",
            });
          }

          return res.status(201).json(guidelineData);
        } catch (error) {
          console.error("Error in POST guideline:", error);
          return res.status(500).json({
            error: true,
            message: "Error adding guideline",
          });
        }

      case "PUT":
        try {
          const {
            id,
            household_size,
            household_points,
            lower_threshold,
            middle_threshold,
            upper_threshold,
            lower_price,
            middle_price,
            upper_price,
          } = req.body;

          if (
            !id ||
            !household_size ||
            !household_points ||
            !lower_threshold ||
            !middle_threshold ||
            !upper_threshold ||
            !lower_price ||
            !middle_price ||
            !upper_price
          ) {
            return res.status(400).json({
              error: true,
              message: "Missing required fields",
            });
          }

          const { data: guidelineData, error: guidelineError } =
            await supabaseClient
              .from("fee_guidelines")
              .update({
                household_size: parseInt(household_size),
                household_points: parseInt(household_points),
                lower_threshold: parseFloat(lower_threshold),
                middle_threshold: parseFloat(middle_threshold),
                upper_threshold: parseFloat(upper_threshold),
                lower_price: parseFloat(lower_price),
                middle_price: parseFloat(middle_price),
                upper_price: parseFloat(upper_price),
              })
              .eq("id", id)
              .select()
              .single();

          if (guidelineError) {
            console.error("Error updating guideline:", guidelineError);
            return res.status(500).json({
              error: true,
              message: guidelineError.message || "Error updating guideline",
            });
          }

          return res.status(200).json(guidelineData);
        } catch (error) {
          console.error("Error in PUT guideline:", error);
          return res.status(500).json({
            error: true,
            message: "Error updating guideline",
          });
        }

      case "DELETE":
        try {
          const { id } = req.body;

          if (!id) {
            return res.status(400).json({
              error: true,
              message: "Missing guideline ID",
            });
          }

          const { error: deleteError } = await supabaseClient
            .from("fee_guidelines")
            .delete()
            .eq("id", id);

          if (deleteError) {
            console.error("Error deleting guideline:", deleteError);
            return res.status(500).json({
              error: true,
              message: deleteError.message || "Error deleting guideline",
            });
          }

          return res.status(200).json({
            error: false,
            message: "Guideline deleted successfully",
          });
        } catch (error) {
          console.error("Error in DELETE guideline:", error);
          return res.status(500).json({
            error: true,
            message: "Error deleting guideline",
          });
        }

      default:
        res.setHeader("Allow", ["GET", "POST", "PUT", "DELETE"]);
        return res.status(405).json({
          error: true,
          message: `Method ${req.method} Not Allowed`,
        });
    }
  });
}
