import { DashboardNav } from "../../components/dashboard/dashboard-nav";
import { useAuthCheck } from "hooks/useAuthCheck";
import React, { useState, useRef, useEffect } from "react";
import {
  Tabs,
  Ta<PERSON>Content,
  Ta<PERSON>List,
  TabsTrigger,
} from "../../components/ui/tabs";
import { Card } from "../../components/ui/card";
import { CustomButton } from "../../components/ui/custom-button";
import { Input } from "../../components/ui/input";
import { Badge } from "../../components/ui/badge";
import { Search, Plus, Minus, X } from "lucide-react";
import {
  ProductList,
  Product,
} from "../../components/dashboard/checkout/product-list";
import { ReceiptModal } from "../../components/dashboard/checkout/receipt-modal";
import {
  MemberSearch,
  Member,
} from "../../components/dashboard/checkout/member-search";
import { HoldOrderDialog } from "../../components/dashboard/checkout/hold-order-dialog";
import { CancelOrderDialog } from "../../components/dashboard/checkout/cancel-order-dialog";
import { useAlert } from "context/useAlert";
import { useProductData } from "context/useProductCategoryData";
import OnholdOrderContent from "@/components/dashboard/checkout/onhold-order-content";
import { supabaseClient } from "utils/supabase/client";
import { CheckoutStatusType } from "types";
import { getCurrentActiveMembership, playSound } from "utils";
import { useSelectClient } from "context/useSelectClientContext";
import { useCheckedInToday } from "context/useCheckedInToday";
import { useHeldOrderData } from "context/useHeldOrderData";
import { useLocation } from "hooks/useLocation";
import ReturnItemsContent from "@/components/dashboard/checkout/return-items-content";
import { useReturnItem } from "context/useReturnItem";
import ModalAlert from "@/components/ui/modal-alert";

export interface OrderItem {
  id: string;
  product_name: string;
  quantity: number;
  points: number;
  order_max_quantity: number;
  monthly_max_quantity: number;
}

interface HeldOrderItem {
  product_id: string;
  product_name: string;
  quantity: number;
  points_per_unit: number;
  total_points: number;
  order_max_quantity: number;
  monthly_max_quantity: number;
}

export interface HeldOrder {
  id: string;
  member_id: string;
  member: Member;
  items: HeldOrderItem[];
  order_number: string;
  date_of_order: string;
  total_points_used: number;
  on_hold: boolean;
}

export default function CheckoutPage() {
  const { user, loading, token } = useAuthCheck();
  const { productData, fetchingProduct } = useProductData();
  const { showAlert } = useAlert();
  const { refreshSelectedClient } = useSelectClient();
  const { refreshData } = useCheckedInToday();
  const { heldOrderData, refreshData: refreshHeldOrderData } =
    useHeldOrderData();
  const { returnItemData } = useReturnItem();
  const { city, region, country, error } = useLocation();
  const [completingOrder, setCompletingOrder] = useState(false);
  const [activeTab, setActiveTab] = useState("new-order");
  const [cart, setCart] = useState<OrderItem[]>([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedMember, setSelectedMember] = useState<Member | undefined>();
  const [checkoutStatus, setCheckoutStatus] =
    useState<CheckoutStatusType>("not-checked-in");
  const [showReceiptModal, setShowReceiptModal] = useState(false);
  const [showHoldDialog, setShowHoldDialog] = useState(false);
  const [showCancelDialog, setShowCancelDialog] = useState(false);
  const [orderNumber, setOrderNumber] = useState<string | null>(null);
  const [showModal, setShowModal] = useState(false);
  const [modalConfig, setModalConfig] = useState({
    title: "",
    message: "",
    type: "error" as "warning" | "error" | "success" | "info",
    confirmText: "OK",
  });

  const cartScrollRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (cartScrollRef.current) {
      cartScrollRef.current.scrollTop = cartScrollRef.current.scrollHeight;
    }
  }, [cart]);

  const hasAccess = ["admin", "staff", "volunteer"].includes(
    user?.user_metadata?.role
  );

  if (!hasAccess && !loading) {
    return (
      <React.Fragment>
        <DashboardNav />
        <div className="text-center text-red-500 h-screen flex items-center justify-center">
          Access Denied: Your role does not have access to this page.
        </div>
      </React.Fragment>
    );
  }

  const totalPoints = cart.reduce(
    (sum, item) => sum + item.points * item.quantity,
    0
  );

  const handleMemberSelect = (member: Member) => {
    setSelectedMember(member);
    setCheckoutStatus(member.checkoutStatus);
  };

  const handleAddToCart = (product: Product) => {
    if (!selectedMember) {
      setModalConfig({
        title: "Checkout Error",
        message: "Please select a member first",
        type: "error",
        confirmText: "OK",
      });
      setShowModal(true);
      return;
    }

    if (checkoutStatus !== "has-checked-in" && checkoutStatus !== "ready") {
      setModalConfig({
        title: "Checkout Error",
        message: "Member is not ready for checkout",
        type: "error",
        confirmText: "OK",
      });
      setShowModal(true);
      return;
    }

    // Find previous orders for this product
    const previousOrders = selectedMember.orders.flatMap((order) =>
      order.items.filter((orderedItem) => orderedItem.product_id === product.id)
    );

    const totalOrderedQuantity = previousOrders.reduce(
      (sum, orderedItem) => sum + orderedItem.quantity,
      0
    );

    const existingItem = cart.find((item) => item.id === product.id);
    const newQuantity = existingItem ? existingItem.quantity + 1 : 1;
    const totalIncludingNewQuantity = totalOrderedQuantity + newQuantity;

    // Check if adding exceeds monthly max quantity
    if (totalIncludingNewQuantity > product.monthly_max_quantity) {
      setModalConfig({
        title: "Limit Reached",
        message: `You've reached the monthly limit (${product.monthly_max_quantity}) for ${product.product_name}`,
        type: "error",
        confirmText: "I Understand",
      });
      setShowModal(true);
      return;
    }

    if (existingItem) {
      if (existingItem.quantity >= product.order_max_quantity) {
        setModalConfig({
          title: "Limit Reached",
          message: `You've reached the order limit (${product.order_max_quantity}) for ${product.product_name}`,
          type: "error",
          confirmText: "I Understand",
        });
        setShowModal(true);
        return;
      }

      setCart(
        cart.map((item) =>
          item.id === product.id
            ? { ...item, quantity: item.quantity + 1 }
            : item
        )
      );
    } else {
      setCart([
        ...cart,
        {
          id: product.id,
          product_name: product.product_name,
          quantity: 1,
          points: product.unit_points,
          order_max_quantity: product.order_max_quantity,
          monthly_max_quantity: product.monthly_max_quantity,
        },
      ]);
    }
  };

  const handleUpdateQuantity = (itemId: string, change: number) => {
    setCart(
      cart.map((item) => {
        if (item.id === itemId) {
          const newQuantity = item.quantity + change;

          // Find all previous orders for this product by the selected member
          const previousOrders = selectedMember?.orders.flatMap((order) =>
            order.items.filter((order) => order.product_id === item.id)
          );

          const totalOrderedQuantity = previousOrders?.reduce(
            (sum, order) => sum + order.quantity,
            0
          );

          const totalIncludingNewQuantity = totalOrderedQuantity + newQuantity;

          // Check if newQuantity exceeds order_max_quantity
          if (newQuantity > item.order_max_quantity) {
            setModalConfig({
              title: "Limit Reached",
              message: `You've reached the order limit (${item.order_max_quantity}) for ${item.product_name}`,
              type: "error",
              confirmText: "I Understand",
            });
            setShowModal(true);
            return item;
          }

          // Check if totalIncludingNewQuantity exceeds monthly_max_quantity
          if (totalIncludingNewQuantity > item.monthly_max_quantity) {
            setModalConfig({
              title: "Limit Reached",
              message: `You've reached the monthly limit (${item.monthly_max_quantity}) for ${item.product_name}`,
              type: "error",
              confirmText: "I Understand",
            });
            setShowModal(true);
            return item;
          }

          if (newQuantity < 1) {
            return item;
          }

          return { ...item, quantity: newQuantity };
        }
        return item;
      })
    );
  };

  const handleRemoveItem = (itemId: string) => {
    setCart(cart.filter((item) => item.id !== itemId));
  };

  const handleFinishCheckout = () => {
    if (!selectedMember) {
      setModalConfig({
        title: "Checkout Error",
        message: "Please select a member first",
        type: "error",
        confirmText: "OK",
      });
      setShowModal(true);
      return;
    }

    if (checkoutStatus !== "has-checked-in" && checkoutStatus !== "ready") {
      setModalConfig({
        title: "Checkout Error",
        message: "Member is not ready for checkout",
        type: "error",
        confirmText: "OK",
      });
      setShowModal(true);
      return;
    }

    if (cart.length === 0) {
      setModalConfig({
        title: "Checkout Error",
        message: "Cart is empty",
        type: "error",
        confirmText: "OK",
      });
      setShowModal(true);
      return;
    }

    if (
      totalPoints >
      getCurrentActiveMembership(selectedMember?.memberships)?.available_points
    ) {
      setModalConfig({
        title: "Checkout Error",
        message: "Not enough points available",
        type: "error",
        confirmText: "OK",
      });
      setShowModal(true);
      return;
    }

    setShowReceiptModal(true);
  };

  const handlePrintReceipt = () => {
    console.log("Printing receipt...");
  };

  const handleCompleteTransaction = async () => {
    setCompletingOrder(true);
    const currentActiveMembership = getCurrentActiveMembership(
      selectedMember?.memberships
    );

    try {
      const response = await fetch("/api/user-data/checkout/complete", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({
          orderId: orderNumber,
          member_id: selectedMember?.id,
          total_points_used: totalPoints,
          items: cart,
          on_hold: false,
          role: user?.user_metadata?.role,
          checkout_by: user?.id,
          membership_id: currentActiveMembership?.id,
          store_location: !error ? `${city}, ${region}, ${country}` : error,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || "Failed to complete transaction");
      }

      showAlert(data.message, "success");
      await refreshData();
      await refreshSelectedClient();
      setShowReceiptModal(false);
      setCart([]);
      setSelectedMember(undefined);
      setCheckoutStatus("not-checked-in");
    } catch (error) {
      console.error("Transaction error:", error);
      showAlert(
        error instanceof Error
          ? error.message
          : "Failed to complete transaction",
        "error"
      );
    } finally {
      setCompletingOrder(false);
    }
  };

  const handlePutOnHold = () => {
    if (!selectedMember || cart.length === 0) {
      showAlert(
        "Please select a member and add items to the cart first",
        "error"
      );
      return;
    }
    setShowHoldDialog(true);
  };

  const handleConfirmHold = async () => {
    if (selectedMember) {
      setCart([]);
      setSelectedMember(undefined);
      setCheckoutStatus("not-checked-in");

      try {
        const response = await fetch(
          "/api/user-data/checkout/insert-checkout",
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${token}`,
            },
            body: JSON.stringify({
              orderId: orderNumber,
              member_id: selectedMember.id,
              total_points_used: totalPoints,
              items: cart,
              on_hold: true,
              role: user?.user_metadata?.role,
              store_location: !error ? `${city}, ${region}, ${country}` : error,
            }),
          }
        );

        const responseData = await response.json();
        if (response.ok) {
          await refreshSelectedClient();
          await refreshHeldOrderData();
          showAlert(responseData.message, "success");
        } else {
          showAlert(responseData.message, "error");
        }
      } catch (error) {
        showAlert("Error adding on hold order", "error");
        throw error;
      }
    }
  };

  const handleResumeOrder = async (order: HeldOrder) => {
    //filter out the order item based on the order id. update on hold status to false
    const { data, error } = await supabaseClient
      .from("orders")
      .update({ on_hold: false })
      .eq("id", order.id)
      .select();

    if (error) {
      console.error("Error updating on hold status:", error);
      return;
    }

    const mappedItems = order.items.map((item) => ({
      id: item.product_id,
      product_name: item.product_name,
      quantity: item.quantity,
      points: item.points_per_unit,
      order_max_quantity: item.order_max_quantity,
      monthly_max_quantity: item.monthly_max_quantity,
    }));

    // Set the cart with mapped items
    setCart(mappedItems);

    // Set the selected member with all necessary data
    const memberData = {
      id: order.member_id,
      first_name: order.member.first_name,
      last_name: order.member.last_name,
      memberships: order.member.memberships,
      checkoutStatus: order.member.checkoutStatus,
      check_in: order.member.check_in || [],
      role: order.member.role,
      orders: order.items,
    };

    await refreshSelectedClient();
    handleMemberSelect(memberData);
    setActiveTab("new-order");

    setOrderNumber(order.id);
  };

  const handleRestartOrder = async () => {
    if (orderNumber) {
      try {
        const response = await fetch(
          `/api/user-data/checkout/delete-held-order`,
          {
            method: "DELETE",
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${token}`,
            },
            body: JSON.stringify({
              id: orderNumber,
              role: user?.user_metadata?.role,
            }),
          }
        );

        if (!response.ok) {
          throw new Error("Failed to restart order");
        }

        showAlert("Order restarted successfully", "success");
        await refreshSelectedClient();
      } catch (error) {
        console.error("Error restarting order:", error);
        showAlert("Failed to restart order", "error");
      } finally {
        setCart([]);
      }
    }

    setCart([]);
  };

  const handleCancelOrder = async () => {
    if (orderNumber) {
      try {
        const response = await fetch(
          `/api/user-data/checkout/delete-held-order`,
          {
            method: "DELETE",
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${token}`,
            },
            body: JSON.stringify({
              id: orderNumber,
              role: user?.user_metadata?.role,
            }),
          }
        );

        const data = await response.json();

        if (!response.ok) {
          throw new Error(data.message || "Failed to cancel order");
        }

        showAlert(data.message || "Order cancelled successfully", "success");

        await refreshSelectedClient();
      } catch (error) {
        console.error("Cancel order error:", error);
        showAlert(
          error instanceof Error ? error.message : "Failed to cancel order",
          "error"
        );
      }
    }

    setCart([]);
    setSelectedMember(undefined);
    setCheckoutStatus("not-checked-in");
    setOrderNumber(null);
    setShowCancelDialog(false);
  };

  const getStatusBadge = (checkoutStatus: string) => {
    switch (checkoutStatus) {
      case "not-checked-in":
      case "inactive":
      case "expired":
        return <Badge variant="destructive">Not Checked In</Badge>;
      case "already-checked-out":
        return <Badge variant="warning">Already Checked Out Today</Badge>;
      case "has-checked-in":
        return <Badge variant="success">Ready to Checkout</Badge>;
      case "no-membership":
        return <Badge variant="none">No Membership</Badge>;
      default:
        return <Badge variant="success">Ready to Checkout</Badge>;
    }
  };

  return (
    <React.Fragment>
      <DashboardNav />
      <div className="max-w-full mx-auto p-6">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="mb-4">
            <TabsTrigger value="new-order">New Order</TabsTrigger>
            <TabsTrigger value="on-hold">
              On Hold Orders{" "}
              {heldOrderData?.length > 0 && `(${heldOrderData?.length})`}
            </TabsTrigger>
            <TabsTrigger value="return-items">
              Return Items{" "}
              {returnItemData?.length > 0 && `(${returnItemData?.length})`}
            </TabsTrigger>
          </TabsList>
          <TabsContent value="new-order">
            <div className="flex gap-4 h-[calc(100vh-8rem)]">
              <div className="w-1/5 flex flex-col gap-2">
                <Card className="p-3">
                  <h2 className="text-lg font-bold mb-2">Order Information</h2>
                  <div className="space-y-2">
                    <MemberSearch
                      onSelectMember={handleMemberSelect}
                      selectedMember={selectedMember}
                    />
                    {selectedMember && (
                      <div className="text-sm">
                        {getStatusBadge(selectedMember.checkoutStatus)}
                        <div className="mt-1 space-y-0.5">
                          <p>Member ID: {selectedMember.id}</p>
                          {orderNumber && <p>Order #: {orderNumber}</p>}
                          <p>
                            Date:{" "}
                            {new Date().toLocaleDateString("en-US", {
                              month: "short",
                              day: "numeric",
                              year: "numeric",
                              hour: "2-digit",
                              minute: "2-digit",
                            })}
                          </p>
                        </div>
                      </div>
                    )}
                  </div>
                </Card>

                <Card className="p-3 flex-1 flex flex-col h-[320px] max-h-[320px]">
                  <h2 className="text-lg font-bold mb-2 flex-shrink-0">
                    Shopping Cart
                  </h2>

                  <div
                    className="flex-1 min-h-0 overflow-hidden"
                    style={{ maxHeight: "calc(100% - 150px)" }}
                  >
                    <div
                      ref={cartScrollRef}
                      className="h-full overflow-y-auto pr-1 scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100"
                    >
                      {cart.length === 0 ? (
                        <div className="flex items-center justify-center h-20 text-gray-500 text-sm">
                          Cart is empty
                        </div>
                      ) : (
                        <div className="space-y-1">
                          {cart.map((item) => (
                            <div
                              key={item.id}
                              className="flex items-center justify-between text-sm py-2 px-1 border-b border-gray-100 last:border-b-0 hover:bg-gray-50 transition-colors duration-150"
                            >
                              <span className="truncate mr-2 flex-1 font-medium">
                                {item.product_name}
                              </span>
                              <div className="flex items-center gap-1 flex-shrink-0">
                                <CustomButton
                                  size="sm"
                                  variant="outline"
                                  className="h-6 w-6 p-0 hover:bg-gray-100 transition-colors"
                                  onClick={() => {
                                    handleUpdateQuantity(item.id, -1);
                                    playSound();
                                  }}
                                  aria-label={`Decrease quantity for ${item.product_name}`}
                                >
                                  <Minus className="h-3 w-3" />
                                </CustomButton>
                                <span className="w-6 text-center font-semibold text-xs bg-gray-50 rounded px-1">
                                  {item.quantity}
                                </span>
                                <CustomButton
                                  size="sm"
                                  variant="outline"
                                  className="h-6 w-6 p-0 hover:bg-gray-100 transition-colors"
                                  onClick={() => {
                                    handleUpdateQuantity(item.id, 1);
                                    playSound();
                                  }}
                                  aria-label={`Increase quantity for ${item.product_name}`}
                                >
                                  <Plus className="h-3 w-3" />
                                </CustomButton>
                                <CustomButton
                                  size="sm"
                                  variant="destructive"
                                  className="h-6 w-6 p-0 hover:bg-red-600 transition-colors"
                                  onClick={() => {
                                    handleRemoveItem(item.id);
                                    playSound();
                                  }}
                                  aria-label={`Remove ${item.product_name} from cart`}
                                >
                                  <X className="h-3 w-3" />
                                </CustomButton>
                              </div>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>

                  <div className="mt-3 pt-3 border-t space-y-2 text-sm bg-white flex-shrink-0 shadow-sm">
                    <div className="flex justify-between items-center">
                      <span className="text-muted-foreground font-medium">
                        Available:
                      </span>
                      <span className="font-bold text-blue-600">
                        {getCurrentActiveMembership(selectedMember?.memberships)
                          ?.available_points || 0}
                      </span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-muted-foreground font-medium">
                        Total:
                      </span>
                      <span className="font-bold text-gray-900">
                        {totalPoints}
                      </span>
                    </div>
                    <div className="flex justify-between items-center pt-2 border-t border-gray-200">
                      <span className="text-muted-foreground font-medium">
                        Remaining:
                      </span>
                      <span
                        className={`font-bold text-lg ${
                          (getCurrentActiveMembership(
                            selectedMember?.memberships
                          )?.available_points || 0) -
                            totalPoints <
                          0
                            ? "text-red-500"
                            : "text-green-600"
                        }`}
                      >
                        {(getCurrentActiveMembership(
                          selectedMember?.memberships
                        )?.available_points || 0) - totalPoints}
                      </span>
                    </div>
                  </div>
                </Card>

                <div className="grid grid-cols-2 gap-1">
                  <CustomButton
                    size="sm"
                    variant="outline"
                    onClick={handleRestartOrder}
                  >
                    Restart
                  </CustomButton>
                  <CustomButton
                    size="sm"
                    variant="outline"
                    onClick={handlePutOnHold}
                  >
                    Hold
                  </CustomButton>
                  <CustomButton
                    size="sm"
                    variant="destructive"
                    onClick={handleCancelOrder}
                  >
                    Cancel
                  </CustomButton>
                  <CustomButton
                    size="sm"
                    variant="default"
                    onClick={handleFinishCheckout}
                    disabled={completingOrder}
                    className="bg-blue-600 hover:bg-blue-700"
                  >
                    Finish
                  </CustomButton>
                </div>
              </div>

              {/* Right Side - Product Grid - Made wider */}
              <div className="w-4/5">
                <div className="mb-3 flex gap-2">
                  <div className="relative flex-1">
                    <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Search products..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="pl-8"
                      disabled={fetchingProduct || !productData}
                    />
                  </div>
                </div>

                <div className="h-[calc(100vh-12rem)] overflow-y-auto">
                  <ProductList
                    products={productData?.filter((product) =>
                      searchQuery
                        ? product.product_name
                            .toLowerCase()
                            .includes(searchQuery.toLowerCase()) ||
                          product.category
                            .toLowerCase()
                            .includes(searchQuery.toLowerCase())
                        : true
                    )}
                    onSelectProduct={handleAddToCart}
                  />
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="on-hold">
            <OnholdOrderContent
              heldOrders={heldOrderData}
              handleResumeOrder={handleResumeOrder}
            />
          </TabsContent>

          <TabsContent value="return-items">
            <ReturnItemsContent />
          </TabsContent>
        </Tabs>

        <ReceiptModal
          isOpen={showReceiptModal}
          onClose={() => setShowReceiptModal(false)}
          orderItems={cart}
          orderNumber={orderNumber || ""}
          memberName={selectedMember?.first_name || "N/A"}
          totalPoints={totalPoints}
          onPrint={handlePrintReceipt}
          onComplete={handleCompleteTransaction}
          onCancel={() => setShowReceiptModal(false)}
          isCompletingOrder={completingOrder}
        />

        <HoldOrderDialog
          isOpen={showHoldDialog}
          onClose={() => setShowHoldDialog(false)}
          onConfirm={handleConfirmHold}
          memberName={selectedMember?.first_name || ""}
        />

        <CancelOrderDialog
          isOpen={showCancelDialog}
          onClose={() => setShowCancelDialog(false)}
          onConfirm={handleCancelOrder}
          memberName={selectedMember?.first_name || ""}
        />

        <ModalAlert
          isOpen={showModal}
          onClose={() => setShowModal(false)}
          title={modalConfig.title}
          message={modalConfig.message}
          type={modalConfig.type}
          confirmText={modalConfig.confirmText}
        />
      </div>
    </React.Fragment>
  );
}
