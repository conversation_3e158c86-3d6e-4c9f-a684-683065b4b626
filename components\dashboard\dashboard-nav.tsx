import Link from "next/link";
import { User } from "lucide-react";
import Image from "next/image";

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "../ui/dropdown-menu";
import { CustomButton } from "../ui/custom-button";
import { useRouter } from "next/router";
import { supabaseClient } from "utils/supabase/client";
import { useEffect, useState } from "react";

export function DashboardNav() {
  const router = useRouter();
  const [userData, setUserData] = useState<{
    name: string | null;
    role: string | null;
  }>({
    name: null,
    role: null,
  });

  useEffect(() => {
    const getUserProfile = async () => {
      const { data: authData } = await supabaseClient.auth.getUser();

      if (authData.user?.id) {
        const { data: profileData, error } = await supabaseClient
          .from("users")
          .select("first_name, last_name, role")
          .eq("user_auth_id", authData.user.id)
          .single();

        if (error) {
          console.error("Error fetching user profile:", error);
          return;
        }

        setUserData({
          name: `${profileData?.first_name} ${profileData?.last_name}`,
          role: profileData?.role,
        });
      }
    };

    getUserProfile();
  }, []);

  const signOutAction = async () => {
    try {
      await supabaseClient.auth.signOut();
      router.push("/login");
    } catch (error) {
      console.error("Error signing out:", error);
    }
  };

  return (
    <nav className="border-b bg-background">
      <div className="w-full mx-auto px-6">
        <div className="flex h-16 items-center justify-between">
          <div className="flex items-center gap-4">
            {/* <Sheet>
              <SheetTrigger asChild>
                <CustomButton variant="ghost" size="icon" className="md:hidden">
                  <Menu className="h-5 w-5" />
                  <span className="sr-only">Toggle navigation menu</span>
                </CustomButton>
              </SheetTrigger>
              {["admin", "staff", "volunteer"].includes(
                userData?.role || ""
              ) && (
                <SheetContent side="left">
                  <nav className="grid gap-2">
                    <Link
                      href="/membership"
                      className="flex items-center gap-2 text-lg font-semibold"
                    >
                      Dashboard
                    </Link>
                  </nav>
                </SheetContent>
              )}
            </Sheet> */}
            {["admin", "staff", "volunteer"].includes(userData?.role || "") && (
              <Link href="/membership" className="flex items-center gap-2">
                <Image
                  src="/cfclogo.png"
                  alt="CFC Logo"
                  width={120}
                  height={40}
                  priority
                />
              </Link>
            )}
            {/* <nav className="hidden md:flex gap-4">
              <Link
                href="#"
                className="text-muted-foreground hover:text-foreground"
              >
                Analytics
              </Link>
              <Link
                href="#"
                className="text-muted-foreground hover:text-foreground"
              >
                Reports
              </Link>
              <Link
                href="#"
                className="text-muted-foreground hover:text-foreground"
              >
                Settings
              </Link>
            </nav> */}
          </div>
          <div className="flex items-center gap-4">
            {userData.name && (
              <h1 className="text-md font-medium">Hi, {userData.name}!</h1>
            )}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <CustomButton variant="ghost" size="icon">
                  <User className="h-5 w-5" />
                  <span className="sr-only">Open user menu</span>
                </CustomButton>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuLabel>My Account</DropdownMenuLabel>
                <DropdownMenuSeparator />
                {["admin", "staff"].includes(userData?.role || "") && (
                  <>
                    <DropdownMenuItem>
                      <Link href="/administrator?tab=staff">
                        {userData?.role === "admin"
                          ? "Admin"
                          : "Volunteer Account Management"}
                      </Link>
                    </DropdownMenuItem>
                  </>
                )}
                {/* {["admin"].includes(userData?.role || "") && (
                  <>
                    <DropdownMenuItem>
                      <Link href="/administrator?tab=categories">
                        Categories
                      </Link>
                    </DropdownMenuItem>
                    <DropdownMenuItem>
                      <Link href="/administrator?tab=products">Products</Link>
                    </DropdownMenuItem>
                  </>
                )} */}
                <DropdownMenuItem>
                  <Link href="/account-settings">Account Settings</Link>
                </DropdownMenuItem>
                <DropdownMenuItem>
                  <CustomButton onClick={signOutAction} variant="destructive">
                    Sign out
                  </CustomButton>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </div>
    </nav>
  );
}
