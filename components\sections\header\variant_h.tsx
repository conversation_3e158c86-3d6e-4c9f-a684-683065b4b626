import { Button } from "@stackshift-ui/button";
import { Container } from "@stackshift-ui/container";
import { Flex } from "@stackshift-ui/flex";
import { Heading } from "@stackshift-ui/heading";
import { Section } from "@stackshift-ui/section";
import { ButtonProps, HeaderProps } from ".";

export default function Header_H({ title, videos }: HeaderProps) {
  return (
    <Section className="py-8 md:py-10">
      <Container maxWidth={1280}>
        <div className="w-full grid md:grid-cols-2 grid-cols-1 gap-6 md:px-14">
          {videos &&
            videos.map((youtube, index) => {
              let videoLinkId;

              if (youtube?.youtubeLink) {
                const link = youtube.youtubeLink;
                if (link.includes("embed")) {
                  videoLinkId = link.split("/").pop();
                } else if (link.includes("watch?v=")) {
                  videoLinkId = link.split("watch?v=")[1].split("&")[0];
                } else if (link.includes("youtu.be")) {
                  videoLinkId = link.split("youtu.be/")[1];
                }
              }

              if (!videoLinkId) return null;
              return (
                <div className="w-full aspect-video" key={index}>
                  <div className="w-full flex items-center justify-center">
                    <span className="text-secondary md:text-3xl text-2xl text-center mb-4">{youtube?.title}</span>
                  </div>

                  <iframe
                    aria-label="Show Video Frame"
                    className="w-full h-full md:h-[340px]"
                    src={`https://www.youtube.com/embed/${videoLinkId}`}
                    // srcDoc={`<style>*{padding:0;margin:0;overflow:hidden;}html,body{height:100%}img,span{position:absolute;width:100%;top:0;bottom:0;margin:auto}span{height:1.5em;text-align:center;font:48px/1.5 sans-serif;color:white;text-shadow:0 0 0.5em black}</style><a href="https://www.youtube.com/embed/${videoLinkId}"><img src="https://i.ytimg.com/vi/${videoLinkId}/maxresdefault.jpg" alt="YouTube video thumbnail" loading="lazy" /><span>▶</span></a>`}
                    loading="lazy"
                    allow="accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture"
                    allowFullScreen
                  />
                </div>
              );
            })}
        </div>
      </Container>
    </Section>
  );
}

// function VideoPlayer({ videoLinkId, title }: { videoLinkId?: string; title?: string }) {
//   return (
//     <div className="aspect-video">
//       <iframe
//         aria-label="Show Video Frame"
//         className="w-full h-full border-4 rounded-3xl border-primary"
//         // src={`https://www.youtube.com/embed/${videoLinkId}`}
//         srcDoc={`<style>*{padding:0;margin:0;overflow:hidden;border-radius:24px}html,body{height:100%}img,span{position:absolute;width:100%;top:0;bottom:0;margin:auto}span{height:1.5em;text-align:center;font:48px/1.5 sans-serif;color:white;text-shadow:0 0 0.5em black}</style><a href=${`https://www.youtube.com/embed/${videoLinkId}`}><img src=${`https://i.ytimg.com/vi_webp/${videoLinkId}/maxresdefault.webp`} alt=${title} loading="lazy" /><span>▶</span></a>`}
//         loading="lazy"
//         allow="accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture"
//         allowFullScreen
//       />
//     </div>
//   );
// }

function Buttons({
  primaryButton,
  secondaryButton,
}: {
  primaryButton?: ButtonProps;
  secondaryButton?: ButtonProps;
}) {
  return (
    <Flex
      align="center"
      gap={4}
      justify="center"
      className="flex-col lg:flex-row"
    >
      {primaryButton?.label && (
        <Button as="link" ariaLabel={primaryButton?.label} link={primaryButton}>
          {primaryButton?.label}
        </Button>
      )}
      {secondaryButton?.label && (
        <Button
          as="link"
          ariaLabel={secondaryButton?.label}
          link={secondaryButton}
          className="bg-secondary hover:bg-secondary/50 inline-block rounded-global font-bold transition duration-200 px-3 py-4"
        >
          {secondaryButton?.label}
        </Button>
      )}
    </Flex>
  );
}

export { Header_H };
