import React from "react";
import { DashboardNav } from "@/components/dashboard/dashboard-nav";
import { useAuthCheck } from "hooks/useAuthCheck";
import StaffDashboardPage from "@/components/dashboard/staff/staff-dashboard";

export default function AdminPage() {
  const { user, loading } = useAuthCheck();

  const hasAccess = ["admin", "staff", "volunteer"].includes(
    user?.user_metadata?.role
  );

  return (
    <div className="min-h-screen bg-background">
      <DashboardNav />
      {hasAccess && <StaffDashboardPage />}

      {!hasAccess && !loading && (
        <div className="text-center text-red-500 h-screen flex items-center justify-center">
          Access Denied: Your role does not have access to this page.
        </div>
      )}
    </div>
  );
}
