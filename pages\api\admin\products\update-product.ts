import { NextApiRequest, NextApiResponse } from "next";
import { supabaseClient } from "utils/supabase/client";

export default async function PUT(req: NextApiRequest, res: NextApiResponse) {
  try {
    const {
      productName,
      productCode,
      description,
      unitPoints,
      unitPrice,
      unitWeight,
      orderMaxQty,
      monthlyMaxQty,
      isActive,
      category,
      productId,
      image_url,
      role,
    } = req.body;

    if (role !== "admin") {
      return res.status(403).json({
        message: "Unauthorized. Only admin can update product data.",
      });
    }

    const { data: categoryData, error: categoryError } = await supabaseClient
      .from("category")
      .select("id, name")
      .eq("name", category)
      .single();

    if (categoryError) {
      return res.status(404).json({
        message: "Category not found",
      });
    }

    const hasMonthMaxQty =
      monthlyMaxQty !== "" &&
      monthlyMaxQty !== null &&
      monthlyMaxQty !== undefined;

    const { error: productError } = await supabaseClient
      .from("products")
      .update({
        product_name: productName,
        product_code: productCode,
        description,
        unit_points: unitPoints,
        unit_price: unitPrice,
        unit_weight: unitWeight,
        order_max_quantity: orderMaxQty,
        monthly_max_quantity: hasMonthMaxQty ? monthlyMaxQty : 9999,
        is_active: isActive,
        category: categoryData?.id,
        icon: image_url,
      })
      .eq("id", productId)
      .order("created_at", { ascending: false });

    if (productError) {
      console.log("productError", productError);

      return res.status(404).json({
        message: "Product not found",
      });
    }

    return res.status(200).json({ message: "Product updated successfully!" });
  } catch (error) {
    return res.status(500).json({ message: "Failed to process the request." });
  }
}
