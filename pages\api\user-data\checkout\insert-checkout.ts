import { NextApiRequest, NextApiResponse } from "next";
import { supabaseClient } from "utils/supabase/client";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  const {
    orderId,
    member_id,
    items,
    total_points_used,
    on_hold,
    role,
    store_location,
  } = req.body;

  if (!role || !["staff", "admin"].includes(role)) {
    return res.status(403).json({
      message: "Unauthorized. Only staff and admin can create on hold orders.",
    });
  }

  // If order doesn't exist, insert new order
  const formattedItems = items.map((item) => ({
    product_id: item.id,
    product_name: item.product_name,
    quantity: item.quantity,
    points_per_unit: parseInt(item.points),
    total_points: parseInt(item.points) * item.quantity,
    order_max_quantity: parseInt(item.order_max_quantity),
    monthly_max_quantity: parseInt(item.monthly_max_quantity),
  }));

  try {
    // Check if order already exists
    if (orderId) {
      const { data: existingOrder } = await supabaseClient
        .from("orders")
        .select()
        .eq("id", orderId)
        .single();

      if (existingOrder) {
        // Update existing order
        const { data: updatedOrder, error: updateError } = await supabaseClient
          .from("orders")
          .update({
            on_hold: on_hold,
            items: formattedItems,
            date_of_order: new Date().toISOString(),
            total_points_used: total_points_used,
          })
          .eq("id", orderId)
          .select();

        if (updateError) {
          console.log("updateError", updateError);
          return res.status(500).json({
            message: "Error updating order hold status",
            error: updateError.message,
          });
        }

        return res.status(200).json({
          message: on_hold
            ? "Order put on hold successfully"
            : "Order resumed successfully",
          data: updatedOrder,
        });
      }
    }

    const { data: orderData, error: orderError } = await supabaseClient
      .from("orders")
      .insert({
        member_id,
        items: formattedItems,
        total_points_used,
        on_hold,
        date_of_order: new Date().toISOString(),
        store_location,
        is_completed: false,
      })
      .select();

    if (orderError) {
      console.log("orderError", orderError);
      return res.status(500).json({
        message: "Error adding on hold order",
        error: orderError.message,
      });
    }

    return res.status(200).json({
      message: "On hold order added successfully",
      data: orderData,
    });
  } catch (error) {
    console.log("error adding on hold order", error);
    return res.status(500).json({
      message: error.message || "Error adding on hold order",
    });
  }
}
