import React from "react";
import { Skeleton } from "./ui/skeleton";
import { CustomButton } from "./ui/custom-button";
import { useMemberData } from "context/useMemberData";

type DataTableProps<T> = {
  headers: string[];
  data: T[];
  renderRow: (item: T) => React.ReactNode;
  currentPage?: number;
  totalPages?: number;
  pageSize?: number;
  total?: number;
  onPageChange?: (page: number) => void;
  isLoading?: boolean;
};

export function DataTable<T>({
  headers,
  data,
  renderRow,
  currentPage = 1,
  totalPages = 1,
  pageSize = 10,
  total = 0,
  onPageChange,
  isLoading = false,
}: DataTableProps<T>) {
  const { fetchingMember } = useMemberData();
  if (isLoading || fetchingMember) {
    return <Skeleton className="w-full h-20" />;
  }

  return (
    <React.Fragment>
      <div className="border rounded-lg overflow-x-auto">
        <table className="min-w-full table-auto border-collapse divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr className="border-b bg-gray-50">
              {headers.map((header, index) => (
                <th
                  key={index}
                  className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider"
                >
                  {header}
                </th>
              ))}
            </tr>
          </thead>
          <tbody className="bg-white text-center divide-y divide-gray-200">
            {data?.map((item) => renderRow(item))}
          </tbody>
        </table>
      </div>
      {/* Pagination Controls */}
      {totalPages > 1 && (
        <div className="flex justify-end gap-2 items-center p-4">
          <CustomButton
            variant="outline"
            disabled={currentPage === 1}
            onClick={() => onPageChange?.(currentPage - 1)}
          >
            Previous
          </CustomButton>

          <span>
            Page {currentPage} of {totalPages}
          </span>

          <CustomButton
            variant="outline"
            disabled={currentPage === totalPages}
            onClick={() => onPageChange?.(currentPage + 1)}
          >
            Next
          </CustomButton>
        </div>
      )}
    </React.Fragment>
  );
}
