import React, {
  createContext,
  useState,
  useContext,
  useEffect,
  ReactNode,
} from "react";
import axios from "axios";
import { useAuthCheck } from "hooks/useAuthCheck";

interface PaginationType {
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

interface MemberContextType {
  memberData: any[];
  staffAndAdminData: any[];
  memberPagination: PaginationType;
  staffPagination: PaginationType;
  fetchingMember: boolean;
  setFetchingMember: React.Dispatch<React.SetStateAction<boolean>>;
  refreshData: () => void;
  fetchMemberPage: (page: number) => Promise<void>;
  fetchStaffPage: (page: number) => Promise<void>;
}

const MemberContext = createContext<MemberContextType | null>(null);

export const useMemberData = () => {
  const context = useContext(MemberContext);
  if (!context) {
    throw new Error("useMemberData must be used within a MemberDataProvider");
  }
  return context;
};

interface MemberDataProviderProps {
  children: ReactNode;
}

const defaultPagination: PaginationType = {
  total: 0,
  page: 1,
  pageSize: 5,
  totalPages: 0,
};

export const MemberDataProvider: React.FC<MemberDataProviderProps> = ({
  children,
}) => {
  const [memberData, setMemberData] = useState<any[]>([]);
  const [staffAndAdminData, setStaffAndAdminData] = useState<any[]>([]);
  const [memberPagination, setMemberPagination] =
    useState<PaginationType>(defaultPagination);
  const [staffPagination, setStaffPagination] =
    useState<PaginationType>(defaultPagination);
  const [fetchingMember, setFetchingMember] = useState<boolean>(false);
  const { token } = useAuthCheck();

  const fetchMemberPage = async (page: number) => {
    setFetchingMember(true);
    try {
      const response = await axios.get(
        `/api/members-data?role=member&page=${page}&pageSize=${memberPagination.pageSize}`,
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
        }
      );

      setMemberData(response.data.data);

      setMemberPagination(response.data.pagination);
    } catch (error) {
      console.error("Error fetching member data:", error);
    } finally {
      setFetchingMember(false);
    }
  };

  const fetchStaffPage = async (page: number) => {
    setFetchingMember(true);
    try {
      const response = await axios.get(
        `/api/members-data?role=staff&role=admin&role=volunteer&page=${page}&pageSize=${staffPagination.pageSize}`,
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
        }
      );

      setStaffAndAdminData(response.data.data);
      setStaffPagination(response.data.pagination);
    } catch (error) {
      console.error("Error fetching staff and admin data:", error);
    } finally {
      setFetchingMember(false);
    }
  };

  const refreshData = () => {
    fetchMemberPage(1);
    fetchStaffPage(1);
  };

  useEffect(() => {
    refreshData();
  }, []);

  return (
    <MemberContext.Provider
      value={{
        memberData,
        staffAndAdminData,
        memberPagination,
        staffPagination,
        fetchingMember,
        setFetchingMember,
        refreshData,
        fetchMemberPage,
        fetchStaffPage,
      }}
    >
      {children}
    </MemberContext.Provider>
  );
};
