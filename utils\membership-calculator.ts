import { supabaseClient } from "./supabase/client";

interface FeeGuideline {
  id: number;
  household_size: number;
  household_points: number;
  lower_threshold: number;
  middle_threshold: number;
  upper_threshold: number;
  lower_price: number;
  middle_price: number;
  upper_price: number;
}

async function getFeeGuidelines(): Promise<FeeGuideline[]> {
  try {
    const { data: guidelines, error } = await supabaseClient
      .from("fee_guidelines")
      .select("*")
      .order("household_size", { ascending: true });

    if (error) {
      console.error("Error fetching fee guidelines:", error);
      return [];
    }

    return guidelines || [];
  } catch (error) {
    console.error("Error in getFeeGuidelines:", error);
    return [];
  }
}

export async function calculatePoints(householdSize: number): Promise<number> {
  const guidelines = await getFeeGuidelines();
  const guideline = guidelines.find((g) => g.household_size === householdSize);

  if (!guideline) {
    // If no guideline found, use the highest household size's points
    const maxGuideline = guidelines[guidelines.length - 1];
    return maxGuideline?.household_points || 0;
  }

  return guideline.household_points;
}

function calculateThresholds(
  householdSize: number,
  maxGuideline: FeeGuideline
) {
  const baseIncrease = 2750;
  const difference = householdSize - maxGuideline.household_size;

  return {
    lowerThreshold: maxGuideline.lower_threshold + difference * baseIncrease,
    middleThreshold:
      maxGuideline.middle_threshold + difference * (baseIncrease * 2),
    upperThreshold:
      maxGuideline.upper_threshold + difference * (baseIncrease * 4),
  };
}

export async function calculateMembershipPrice(
  householdSize: number,
  annualIncome: number
): Promise<number[]> {
  // Return empty array for invalid inputs
  if (!householdSize || !annualIncome) return [];

  const guidelines = await getFeeGuidelines();
  if (guidelines.length === 0) return [];

  const guideline = guidelines.find((g) => g.household_size === householdSize);
  const maxGuideline = guidelines[guidelines.length - 1];

  // If household size is greater than the highest in database, calculate thresholds dynamically
  if (householdSize > maxGuideline.household_size) {
    const { lowerThreshold, middleThreshold, upperThreshold } =
      calculateThresholds(householdSize, maxGuideline);

    if (annualIncome <= lowerThreshold) {
      return [maxGuideline.lower_price];
    } else if (annualIncome <= middleThreshold) {
      return [maxGuideline.middle_price];
    } else if (annualIncome <= upperThreshold) {
      return [maxGuideline.upper_price];
    }
    return [];
  }

  // If guideline not found in database but within range, use the highest guideline
  if (!guideline) {
    if (annualIncome <= maxGuideline.lower_threshold) {
      return [maxGuideline.lower_price];
    } else if (annualIncome <= maxGuideline.middle_threshold) {
      return [maxGuideline.middle_price];
    } else if (annualIncome <= maxGuideline.upper_threshold) {
      return [maxGuideline.upper_price];
    }
    return [];
  }

  if (annualIncome <= guideline.lower_threshold) {
    return [guideline.lower_price];
  } else if (annualIncome <= guideline.middle_threshold) {
    return [guideline.middle_price];
  } else if (annualIncome <= guideline.upper_threshold) {
    return [guideline.upper_price];
  }

  return [];
}
