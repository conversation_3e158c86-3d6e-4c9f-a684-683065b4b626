import React, {
  createContext,
  useState,
  useContext,
  useEffect,
  ReactNode,
} from "react";
import axios from "axios";
import { useAuthCheck } from "hooks/useAuthCheck";
import { supabaseClient } from "utils/supabase/client";

interface CouponsDataContextType {
  couponsData: any;
  fetchingCouponsData: boolean;
  setFetchingCouponsData: React.Dispatch<React.SetStateAction<boolean>>;
  refreshData: () => void;
}

const CouponsDataContext = createContext<CouponsDataContextType | null>(null);

export const useCouponsData = () => {
  const context = useContext(CouponsDataContext);
  if (!context) {
    throw new Error("useCouponsData must be used within a CouponsDataProvider");
  }
  return context;
};

interface CouponsDataProviderProps {
  children: ReactNode;
}

export const CouponsDataProvider: React.FC<CouponsDataProviderProps> = ({
  children,
}) => {
  const [couponsData, setCouponsData] = useState<any>([]);
  const [fetchingCouponsData, setFetchingCouponsData] = useState<boolean>(true);
  const { user, token } = useAuthCheck();

  const fetchData = async () => {
    try {
      const response = await axios.get("/api/admin/coupons/get", {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        params: { role: user?.user_metadata?.role },
      });
      setCouponsData(response.data);
    } catch (error) {
      console.error("Error fetching coupons data:", error);
    } finally {
      setFetchingCouponsData(false);
    }
  };

  const refreshData = () => {
    fetchData();
  };

  const handleRealtimeUpdate = async (payload: any) => {
    const { eventType, new: newData, old: oldData } = payload;

    if (eventType === "INSERT") {
      setCouponsData((prevData: any) => [newData, ...prevData]);
    }

    if (eventType === "UPDATE") {
      setCouponsData((prevData: any) =>
        prevData.map((item: any) => (item.id === newData.id ? newData : item))
      );
    }

    if (eventType === "DELETE") {
      setCouponsData((prevData: any) =>
        prevData.filter((item: any) => item.id !== oldData.id)
      );
    }
  };

  useEffect(() => {
    if (user) {
      fetchData();

      const couponSubscription = supabaseClient
        .channel("realtime_coupons")
        .on(
          "postgres_changes",
          { event: "*", schema: "public", table: "coupons" },
          handleRealtimeUpdate
        )
        .subscribe();

      return () => {
        supabaseClient.removeChannel(couponSubscription);
      };
    }
  }, [user]);

  return (
    <CouponsDataContext.Provider
      value={{
        couponsData,
        fetchingCouponsData,
        setFetchingCouponsData,
        refreshData,
      }}
    >
      {children}
    </CouponsDataContext.Provider>
  );
};
