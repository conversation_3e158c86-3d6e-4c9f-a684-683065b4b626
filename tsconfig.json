{"compilerOptions": {"baseUrl": ".", "target": "ES2017", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": false, "forceConsistentCasingInFileNames": true, "noEmit": true, "incremental": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "<PERSON><PERSON><PERSON>", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "plugins": [{"name": "next"}], "strictNullChecks": true, "paths": {"@/components/*": ["components/*"], "@/lib/*": ["lib/*"]}}, "include": ["next-env.d.ts", "modules.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts", "**/*.js", "**/*.jsx", "context/EcwidContext.tjs", "ecwid.d.ts"], "exclude": ["node_modules"]}