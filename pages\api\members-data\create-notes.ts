import { NextApiRequest, NextApiResponse } from "next";
import { supabaseClient } from "utils/supabase/client";

export default async function POST(req: NextApiRequest, res: NextApiResponse) {
  try {
    const { member_id, subject, description, modified_by, noteType } = req.body;

    const { data: userData, error: userError } = await supabaseClient
      .from("users")
      .select("first_name, last_name")
      .eq("user_auth_id", modified_by);

    if (userError) {
      console.error("Error fetching user data:", userError.message);
      return res.status(500).json({
        message: "Error fetching user data to the database",
        error: userError.message,
      });
    }

    const { error: notesError } = await supabaseClient
      .from("notes")
      .insert({
        member_id: member_id,
        subject: subject,
        description: description,
        modified_by: `${userData?.[0]?.first_name} ${userData?.[0]?.last_name}`,
        type: noteType,
      })
      .select();

    if (notesError) {
      console.error("Error inserting notes data:", notesError.message);
      return res.status(500).json({
        message: "Error inserting notes data to the database",
        error: notesError.message,
      });
    }

    return res.status(200).json({
      message: "Insert notes data successfully!",
    });
  } catch (error) {
    console.error(error);
    return res.status(500).json({
      message: "Failed to process the request.",
    });
  }
}
