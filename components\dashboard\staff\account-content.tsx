import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "../../ui/card";
import { useSelectClient } from "context/useSelectClientContext";
import { useAlert } from "context/useAlert";
import { useAuthCheck } from "hooks/useAuthCheck";
import OtherMemberData from "./other-member-data";
import CurrentMembershipData from "./current-membership-data";
import ContactInformation from "./contact-information";
import { useHouseholdData } from "context/useHousehold";
import { useClientSearch } from "context/useClientSearchContext";
import MemberAges from "./member-ages";
import HouseholdMembers from "./household-members";
import AuthorizedShoppers from "./authorized-shoppers";
import PersonalInformation from "./personal-information";
import AccountInformation from "./account-information";
import { CustomButton } from "@/components/ui/custom-button";

export default function AccountContent() {
  const { selectedClient, refreshSelectedClient, setSelectedClient } =
    useSelectClient();
  const { fetchSearchResults } = useClientSearch();
  const { showAlert } = useAlert();
  const { token } = useAuthCheck();
  const { totalMemberIncome, getAgeGroups, memberCount } = useHouseholdData();
  const [isLoading, setIsLoading] = useState(false);

  const handleUpdateAccount = async (
    event: React.FormEvent<HTMLFormElement>
  ) => {
    event.preventDefault();
    setIsLoading(true);

    const formData = new FormData(event.currentTarget);
    const data = Object.fromEntries(formData.entries());

    const authorizedShoppers = Object.keys(data)
      .filter((key) => key.startsWith("authorizedShopper-"))
      .map((key) => data[key]);

    const householdMembers = Object.keys(data)
      .filter((key) => key.startsWith("member"))
      .reduce((acc, key) => {
        const match = key.match(/-(\d+)$/);
        if (match) {
          const index = match[1];
          acc[index] = acc[index] || {};
          acc[index][key.replace(`-${index}`, "")] = data[key];
        }
        return acc;
      }, []);

    const personalInfo = {
      hispanic: data.hispanic,
      race: data.race,
      gender: data.gender,
      disabled: data.disabled,
      veteran: data.veteran,
    };

    const contactInfo = {
      primary_number: data.primary_number,
      primary_phone_type: data.primary_phone_type,
      secondary_number: data.secondary_number,
      secondary_phone_type: data.secondary_phone_type,
      alternate_contact_name: data.alternate_contact_name,
    };

    const ageGroups = getAgeGroups();

    try {
      const response = await fetch("/api/members-data/update-account-info", {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({
          ...data,
          authorizedShoppers: authorizedShoppers?.join(","),
          userId: selectedClient?.user_id,
          memberId: selectedClient?.id,
          householdSize: memberCount,
          contactInfo: JSON.stringify(contactInfo),
          personalInfo: JSON.stringify(personalInfo),
          householdMembers: JSON.stringify(householdMembers),
          ageGroups: JSON.stringify(ageGroups),
          totalMemberIncome: totalMemberIncome,
        }),
      });
      const responseData = await response.json();
      if (!response.ok) {
        showAlert(responseData?.message, "error");
        throw new Error(responseData.message || "An error occurred");
      }
      showAlert(responseData?.message, "success");
    } catch (error) {
      console.error("Error:", error);
      showAlert(error, "error");
    } finally {
      await fetchSearchResults();
      await refreshSelectedClient();
      setIsLoading(false);
    }
  };

  const isDisabled = isLoading || !selectedClient;

  return (
    <div>
      <form onSubmit={handleUpdateAccount} className="space-y-6">
        <div className="flex flex-col gap-4">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex flex-col gap-4 w-full sm:w-[70%]">
              <Card className="w-full mb-4">
                <CardHeader>
                  <CardTitle>Account Details</CardTitle>
                </CardHeader>
                <CardContent>
                  <AccountInformation isLoading={isLoading} />
                </CardContent>
              </Card>

              <Card className="w-full mb-4">
                <CardHeader>
                  <CardTitle>Authorized Shoppers</CardTitle>
                </CardHeader>
                <CardContent>
                  <AuthorizedShoppers isDisabled={isDisabled} />
                </CardContent>
              </Card>

              <Card className="w-full mb-4">
                <CardHeader>
                  <CardTitle>Personal Information</CardTitle>
                </CardHeader>
                <CardContent>
                  <PersonalInformation isDisabled={isDisabled} />
                </CardContent>
              </Card>

              <Card className="w-full mb-4">
                <CardContent>
                  <HouseholdMembers isDisabled={isDisabled} />
                </CardContent>
              </Card>

              <Card className="w-full mb-4">
                <CardHeader>
                  <CardTitle>Member Ages</CardTitle>
                </CardHeader>
                <CardContent>
                  <MemberAges />
                </CardContent>
              </Card>
              <div className="flex justify-end gap-4">
                <CustomButton
                  variant="outline"
                  disabled={isDisabled}
                  onClick={() => setSelectedClient(null)}
                >
                  Cancel
                </CustomButton>
                <CustomButton disabled={isDisabled}>
                  {isLoading ? "Saving..." : "Save Changes"}
                </CustomButton>
              </div>
            </div>

            <div className="flex flex-col gap-4 w-full sm:w-[30%]">
              <Card className="w-full h-fit">
                <CardHeader>
                  <CardTitle>Other Member Data</CardTitle>
                </CardHeader>
                <CardContent>
                  <OtherMemberData />
                </CardContent>
              </Card>

              <Card className="w-full h-fit">
                <CardHeader>
                  <CardTitle>Current Membership</CardTitle>
                </CardHeader>
                <CardContent>
                  <CurrentMembershipData />
                </CardContent>
              </Card>

              <Card className="w-full h-fit">
                <CardHeader>
                  <CardTitle>Contact Information</CardTitle>
                </CardHeader>
                <CardContent>
                  <ContactInformation isLoading={isLoading} />
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </form>
    </div>
  );
}
