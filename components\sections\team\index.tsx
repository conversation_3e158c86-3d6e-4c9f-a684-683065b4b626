import React from "react";
import dynamic from "next/dynamic";
import { SectionsProps, Team as ITeam } from "../../../types";
import * as TeamVariants from "@stackshift-ui/header";

const Variants = {
  variant_a: TeamVariants.Header_A,
  variant_b: TeamVariants.Header_B,
  variant_c: TeamVariants.Header_C,
  variant_d: TeamVariants.Header_D,
  variant_e: dynamic(() => import("./variant_e")),
};

export interface MemberTextProps {
  member: {
    name?: string;
    plainText?: string;
    jobTitle?: string;
  };
}

export interface TeamsProps {
  caption?: string;
  title?: string;
  team?: ITeam[];
}

const displayName = "Team";

export const Team: React.FC<SectionsProps> = ({ data }) => {
  const variant = data?.variant;
  const Variant = variant && Variants[variant as keyof typeof Variants];

  const props = {
    caption: data?.variants?.subtitle ?? undefined,
    title: data?.variants?.title ?? undefined,
    team: data?.variants?.teams ?? undefined,
  };
  return Variant ? <Variant {...props} /> : null;
};

Team.displayName = displayName;
