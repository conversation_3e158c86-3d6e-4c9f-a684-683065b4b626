import { Container } from "@stackshift-ui/container";
import { Flex } from "@stackshift-ui/flex";
import { Heading } from "@stackshift-ui/heading";
import { Image } from "@stackshift-ui/image";
import { Section } from "@stackshift-ui/section";
import React from "react";
import { LogoCloudProps } from ".";
import { Images } from "types";

export default function LogoCloud_E({ title, images }: LogoCloudProps) {
  return (
    <Section className="py-20 bg-white">
      <Container maxWidth={1280}>
        <Title title={title} />
        <Flex
          justify="center"
          align="center"
          className="md:max-w-5xl mx-auto w-full flex md:flex-row flex-wrap"
        >
          <LogoCloudImages images={images} />
        </Flex>
      </Container>
    </Section>
  );
}

function Title({ title }: { title?: string }) {
  if (!title) return null;

  return (
    <Heading
      type="h2"
      fontSize="4xl"
      weight="thin"
      className="mb-5 text-center !text-cfc-secondary"
    >
      {title}
    </Heading>
  );
}

function LogoCloudImages({ images }: { images?: Images[] }) {
  if (!images) return null;

  return (
    <React.Fragment>
      {images?.map((image, index) => (
        <div className="md:mt-0 mt-4" key={index}>
          {image?.image ? (
            <Flex
              align="center"
              justify="center"
              className="mx-auto w-[80%] md:w-[75%] md:h-[192px] rounded-global bg-white"
            >
              <Image
                className="object-scale-down grayscale opacity-50"
                src={`${image?.image}`}
                sizes="192px"
                width={192}
                height={192}
                alt={image?.alt ?? `logoCloud-image${index}`}
              />
            </Flex>
          ) : null}
        </div>
      ))}
    </React.Fragment>
  );
}

export { LogoCloud_E };
