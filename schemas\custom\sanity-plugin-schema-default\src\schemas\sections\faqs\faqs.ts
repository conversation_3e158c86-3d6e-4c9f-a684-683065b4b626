import { rootSchema } from "@webriq-pagebuilder/sanity-plugin-schema-default"
import { MdQuestionAnswer } from "react-icons/md"

// Images
import variantAImage from "./images/variant_a.jpg"
import variantBImage from "./images/variant_b.jpg"
import variantCImage from "./images/variant_c.jpg"
import variantDImage from "./images/variant_d.png"

import initialValue from "./initialValue"
import { faqsSchema } from "./schema"

export const variantsList = [
  {
    title: "Variant A",
    description: "Frequently Asked Questions with search bar",
    value: "variant_a",
    image: variantAImage.src,
  },
  {
    title: "Variant B",
    description: "Frequently Asked Questions with Categories",
    value: "variant_b",
    image: variantBImage.src,
  },
  {
    title: "Variant C",
    description: "Frequently Asked Questions in grid form",
    value: "variant_c",
    image: variantCImage.src,
  },
  {
    title: "Variant D",
    description: "Frequently Asked Questions in grid form",
    value: "variant_d",
    image: variantDImage.src,
  },
]

export default rootSchema(
  "faqs",
  "Frequently Asked Questions",
  MdQuestionAnswer,
  variantsList,
  faqsSchema,
  initialValue
)
