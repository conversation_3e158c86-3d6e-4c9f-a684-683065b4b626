import { NextApiRequest, NextApiResponse } from "next";
import { supabaseClient } from "utils/supabase/client";

export default async function POST(req: NextApiRequest, res: NextApiResponse) {
  try {
    const {
      productName,
      productCode,
      description,
      unitPoints,
      unitPrice,
      unitWeight,
      orderMaxQty,
      monthlyMaxQty,
      isActive,
      category,
      image_url,
      role,
    } = req.body;

    if (role !== "admin") {
      return res.status(403).json({
        message: "Unauthorized. Only admin can create product data.",
      });
    }

    const { data: categoryData, error: categoryError } = await supabaseClient
      .from("category")
      .select("id")
      .ilike("name", category);

    if (categoryError) {
      console.error("Error product category data:", categoryError.message);
      return res.status(500).json({
        message: "Error getting product category data from the database",
        error: categoryError.message,
      });
    }

    const hasMonthMaxQty =
      monthlyMaxQty !== "" &&
      monthlyMaxQty !== null &&
      monthlyMaxQty !== undefined;

    const { error: productError } = await supabaseClient
      .from("products")
      .insert({
        product_name: productName,
        product_code: productCode,
        description,
        unit_points: unitPoints,
        unit_price: unitPrice,
        unit_weight: unitWeight,
        order_max_quantity: orderMaxQty,
        monthly_max_quantity: hasMonthMaxQty ? monthlyMaxQty : 9999,
        category: categoryData[0]?.id,
        is_active: isActive,
        icon: image_url,
      });

    if (productError) {
      console.error("Error product data:", productError.message);
      return res.status(500).json({
        message: "Error inserting product data to the database",
        error: productError.message,
      });
    }

    return res.status(200).json({
      message: "Insert product data successfully!",
    });
  } catch (error) {
    console.error(error);
    return res.status(500).json({
      message: "Failed to process the request.",
    });
  }
}
