import { NextApiRequest, NextApiResponse } from "next";
import { Receipt } from "types";
import { supabaseClient } from "utils/supabase/client";

interface Payment {
  id: number;
  amount: number;
  method: string;
  date: string;
  added_store_location: string;
  memberships: {
    id: number;
    member_id: number;
    total_paid: number;
    members: {
      id: number;
      first_name: string;
      last_name: string;
      address: string;
    };
    added_by_member: {
      id: number;
      first_name: string;
      last_name: string;
    };
  };
}

const handleError = (res: NextApiResponse, message: string, error: any) => {
  console.error(message, error);
  return res.status(500).json({ message, error: error.message });
};

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== "GET") {
    return res.status(405).json({ message: "Method not allowed" });
  }

  try {
    const { data, error } = await supabaseClient
      .from("payments")
      .select(
        `
        id,
        amount,
        method,
        date,
        added_store_location,
        memberships!inner (
          id,
          member_id,
          added_by,
          members:members!memberships_member_id_fkey!inner (
            id,
            first_name,
            last_name,
            address
          ),
          added_by_member:members!memberships_added_by_fkey!inner (
            id,
            first_name,
            last_name
          )
        )
      `
      )
      .order("date", { ascending: false });

    if (error) {
      return handleError(res, "Error fetching receipt data", error);
    }

    if (!data || !Array.isArray(data)) {
      return res.status(200).json([]);
    }

    const response: Receipt[] = (data as unknown as Payment[]).map(
      (payment) => ({
        member_id: payment.memberships.member_id,
        member_name: `${payment.memberships.members.first_name} ${payment.memberships.members.last_name}`,
        member_address: payment.memberships.members.address,
        membership_id: payment.memberships.id,
        added_by: `${payment.memberships.added_by_member.first_name} ${payment.memberships.added_by_member.last_name}`,
        payment_id: payment.id,
        payment_amount: payment.amount,
        payment_method: payment.method,
        payment_date: payment.date,
        store_location: payment.added_store_location,
      })
    );

    return res.status(200).json(response);
  } catch (error) {
    handleError(res, "Error fetching receipts", error);
  }
}
