import { Input } from "@/components/ui/input";
import { Search, ChevronUp, ChevronDown } from "lucide-react";
import React, { useEffect, useMemo, useState } from "react";
import TableFilters from "../table-filters";
import { useFilters } from "hooks/useFilters";
import { formatDate, formatDateWithTimestamp } from "utils";
import { CustomButton } from "@/components/ui/custom-button";
import { Printer } from "lucide-react";
import { Skeleton } from "@/components/ui/skeleton";
import { DateRangeFilter } from "@/components/ui/date-range-filter";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Receipt } from "types";
import { useAuthCheck } from "hooks/useAuthCheck";

type SortField =
  | "payment_id"
  | "member_name"
  | "payment_amount"
  | "payment_date";
type SortOrder = "asc" | "desc";

export default function ReceiptsManagementContent() {
  const { token } = useAuthCheck();
  const [receiptSearchTerm, setReceiptSearchTerm] = useState("");
  const [selectedReceipt, setSelectedReceipt] = useState<any>(null);
  const [receipts, setReceipts] = useState<Receipt[]>([]);
  const [fetchingReceipts, setFetchingReceipts] = useState(false);
  const [dateRange, setDateRange] = useState<{
    from: Date | undefined;
    to: Date | undefined;
  }>({
    from: undefined,
    to: undefined,
  });
  const [selectedLocation, setSelectedLocation] =
    useState<string>("All Locations");
  const [sortField, setSortField] = useState<SortField>("payment_date");
  const [sortOrder, setSortOrder] = useState<SortOrder>("desc");

  const { selectedFilters, handleFilterChange } = useFilters({
    role: [],
    status: [],
    paymentMethod: [],
    distributionCategory: [],
    distributionStatus: [],
  });

  useEffect(() => {
    fetchReceipts();
  }, []);

  const fetchReceipts = async () => {
    setFetchingReceipts(true);
    try {
      const response = await fetch("/api/admin/receipts", {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
      });

      const data = await response.json();
      setReceipts(data);
    } catch (error) {
      console.error("Error fetching receipts:", error);
    } finally {
      setFetchingReceipts(false);
    }
  };

  // Get unique store locations dynamically
  const uniqueLocations = useMemo(() => {
    const locations = receipts
      .map((receipt) => receipt.store_location)
      .filter(Boolean);
    return ["All Locations", ...new Set(locations)];
  }, [receipts]);

  const uniquePaymentMethods = useMemo(() => {
    const methods = receipts
      .map((receipt) => receipt.payment_method)
      .filter(Boolean);
    return [...new Set(methods)];
  }, [receipts]);

  const receiptFilters = {
    paymentMethod: {
      label: "Payment Method",
      options: uniquePaymentMethods.map((method, index) => ({
        id: index.toString(),
        label: method.charAt(0).toUpperCase() + method.slice(1),
        value: method,
      })),
    },
  };

  const handleSort = (field: SortField) => {
    if (sortField === field) {
      setSortOrder(sortOrder === "asc" ? "desc" : "asc");
    } else {
      setSortField(field);
      setSortOrder("desc");
    }
  };

  const getSortIcon = (field: SortField) => {
    if (sortField !== field) return null;
    return sortOrder === "asc" ? (
      <ChevronUp className="h-4 w-4 inline ml-1" />
    ) : (
      <ChevronDown className="h-4 w-4 inline ml-1" />
    );
  };

  const sortReceipts = (receipts: Receipt[]) => {
    return [...receipts].sort((a, b) => {
      let aValue: any = a[sortField];
      let bValue: any = b[sortField];

      if (sortField === "payment_date") {
        aValue = new Date(aValue);
        bValue = new Date(bValue);
      } else if (sortField === "payment_amount") {
        aValue = Number(aValue);
        bValue = Number(bValue);
      } else if (sortField === "payment_id") {
        aValue = Number(aValue);
        bValue = Number(bValue);
      } else {
        aValue = aValue?.toLowerCase() || "";
        bValue = bValue?.toLowerCase() || "";
      }

      if (sortOrder === "asc") {
        return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
      } else {
        return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
      }
    });
  };

  const filterReceipts = (receipts: Receipt[]) => {
    const filtered =
      receipts?.filter((receipt) => {
        const searchTerm = receiptSearchTerm.toLowerCase();
        const memberName = (receipt.member_name || "").toLowerCase();
        const memberAddress = (receipt.member_address || "").toLowerCase();
        const paymentId = (receipt.payment_id || "").toString().toLowerCase();

        const matchesSearch =
          searchTerm === "" ||
          memberName.includes(searchTerm) ||
          memberAddress.includes(searchTerm) ||
          paymentId.includes(searchTerm);

        // Payment method filtering
        const matchesPaymentMethod =
          selectedFilters.paymentMethod.length === 0 ||
          selectedFilters.paymentMethod.includes(receipt.payment_method);

        // Location filtering
        const locationMatches =
          selectedLocation === "All Locations" ||
          selectedLocation === receipt.store_location;

        // Date filtering
        let matchesDateRange = true;
        if (dateRange.from && dateRange.to) {
          const receiptDate = new Date(receipt.payment_date);
          const fromDate = new Date(dateRange.from);
          const toDate = new Date(dateRange.to);

          fromDate.setHours(0, 0, 0, 0);
          toDate.setHours(23, 59, 59, 999);

          matchesDateRange = receiptDate >= fromDate && receiptDate <= toDate;
        }

        return (
          matchesSearch &&
          matchesPaymentMethod &&
          matchesDateRange &&
          locationMatches
        );
      }) || [];

    return sortReceipts(filtered);
  };

  const handleReceiptClick = (receipt: any) => {
    if (selectedReceipt?.payment_id === receipt.payment_id) {
      setSelectedReceipt(null);
      return;
    }

    const receiptData = {
      payment_id: receipt.payment_id,
      date: formatDate(receipt.payment_date),
      client_name: receipt.member_name,
      client_id: receipt.member_id,
      address: receipt.member_address,
      amount: receipt.payment_amount,
      payment_method:
        receipt.payment_method?.charAt(0).toUpperCase() +
        receipt.payment_method?.slice(1),
    };

    setSelectedReceipt(receiptData);
  };

  const filteredAndSortedReceipts = filterReceipts(receipts);

  return (
    <React.Fragment>
      <div className="p-6">
        <style jsx global>{`
          @media print {
            html,
            body {
              height: 100%;
              margin: 0 !important;
              padding: 0 !important;
              overflow: hidden;
            }

            @page {
              size: A4;
              margin: 15mm;
            }

            body * {
              visibility: hidden;
            }

            .no-print {
              display: none !important;
            }

            #printArea,
            #printArea * {
              visibility: visible;
            }

            #printArea {
              position: absolute;
              left: 0;
              top: 0;
              width: 100%;
              height: 100%;
              padding: 15px;
              margin: 0;
            }
          }
        `}</style>

        <div className="flex justify-between items-center mb-6">
          <h2 className="text-xl font-semibold">Member Receipt</h2>
          <div className="flex gap-4">
            <div className="flex flex-col gap-4">
              <div className="flex flex-col md:flex-row gap-4 justify-between">
                <div className="relative w-full md:w-auto">
                  <Search className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400" />
                  <Input
                    placeholder="Search by name, address, or receipt number..."
                    className="pl-10 w-full md:w-[350px]"
                    value={receiptSearchTerm}
                    onChange={(e) => setReceiptSearchTerm(e.target.value)}
                  />
                </div>

                <div className="flex flex-col md:flex-row gap-4 items-start md:items-center">
                  <DateRangeFilter
                    isCustom={false}
                    onDateRangeChange={setDateRange}
                  />
                  <Select
                    value={selectedLocation}
                    onValueChange={setSelectedLocation}
                  >
                    <SelectTrigger className="w-[200px]">
                      <SelectValue placeholder="Select Location" />
                    </SelectTrigger>
                    <SelectContent>
                      {uniqueLocations?.map((location, index) => (
                        <SelectItem key={index} value={location as string}>
                          {location as string}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <TableFilters
                    filters={receiptFilters}
                    selectedFilters={selectedFilters}
                    onFilterChange={handleFilterChange}
                  />
                </div>
              </div>
            </div>
          </div>
        </div>

        {fetchingReceipts ? (
          <Skeleton className="w-full h-20" />
        ) : (
          <div className="border rounded-lg">
            <table className="min-w-full table-auto border-collapse divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr className="border-b bg-gray-50">
                  <th
                    className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider"
                    onClick={() => handleSort("payment_id")}
                  >
                    Receipt Number
                    {getSortIcon("payment_id")}
                  </th>
                  <th
                    className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider"
                    onClick={() => handleSort("member_name")}
                  >
                    Client Name
                    {getSortIcon("member_name")}
                  </th>
                  <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Client Address
                  </th>
                  <th
                    className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider"
                    onClick={() => handleSort("payment_amount")}
                  >
                    Amount
                    {getSortIcon("payment_amount")}
                  </th>
                  <th
                    className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider"
                    onClick={() => handleSort("payment_date")}
                  >
                    Payment Date
                    {getSortIcon("payment_date")}
                  </th>
                  <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Added By
                  </th>
                  <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Method
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white text-center divide-y divide-gray-200">
                {filteredAndSortedReceipts.length > 0 ? (
                  filteredAndSortedReceipts.map((receipt) => (
                    <React.Fragment key={receipt.payment_id}>
                      <tr
                        className="border-b text-center cursor-pointer hover:bg-gray-100"
                        onClick={() => handleReceiptClick(receipt)}
                      >
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {receipt.payment_id}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {receipt.member_name}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {receipt.member_address}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          <span className="font-medium">
                            ${Number(receipt.payment_amount).toFixed(2)}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {formatDateWithTimestamp(receipt.payment_date)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {receipt.added_by}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          <span
                            className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                          ${
                            receipt.payment_method === "credit"
                              ? "bg-blue-100 text-blue-800"
                              : receipt.payment_method === "cash"
                              ? "bg-green-100 text-green-800"
                              : receipt.payment_method === "coupon"
                              ? "bg-yellow-100 text-yellow-800"
                              : receipt.payment_method === "split"
                              ? "bg-orange-100 text-orange-800"
                              : "bg-gray-100 text-gray-800"
                          }`}
                          >
                            {receipt.payment_method?.charAt(0).toUpperCase() +
                              receipt.payment_method?.slice(1)}
                          </span>
                        </td>
                      </tr>
                      {selectedReceipt?.payment_id === receipt.payment_id && (
                        <tr>
                          <td colSpan={7} className="p-0">
                            <div
                              id="printArea"
                              className="p-4 bg-gray-50 border-b"
                            >
                              <div className="text-center mb-8">
                                <div className="flex items-center justify-between relative">
                                  <div className="flex-1">
                                    <h2 className="font-semibold text-xl absolute w-full text-center">
                                      PAYMENT RECEIPT
                                    </h2>
                                  </div>
                                  <CustomButton
                                    onClick={() => window.print()}
                                    variant="outline"
                                    size="sm"
                                    className="no-print ml-auto"
                                  >
                                    <Printer className="h-4 w-4" />
                                  </CustomButton>
                                </div>
                              </div>

                              <div className="space-y-2">
                                <div className="flex justify-between">
                                  <span className="font-medium">
                                    Receipt Number:
                                  </span>
                                  <span>{selectedReceipt?.payment_id}</span>
                                </div>
                                <div className="flex justify-between">
                                  <span className="font-medium">
                                    Payment Date:
                                  </span>
                                  <span>{selectedReceipt?.date}</span>
                                </div>
                                <div className="flex justify-between">
                                  <span className="font-medium">
                                    Client Name:
                                  </span>
                                  <span>{selectedReceipt?.client_name}</span>
                                </div>
                                <div className="flex justify-between">
                                  <span className="font-medium">
                                    Client ID:
                                  </span>
                                  <span>{selectedReceipt?.client_id}</span>
                                </div>
                                <div className="flex justify-between">
                                  <span className="font-medium">
                                    Payment Method:
                                  </span>
                                  <span>{selectedReceipt?.payment_method}</span>
                                </div>
                                <div className="flex justify-between font-semibold">
                                  <span>Total Amount:</span>
                                  <span>${selectedReceipt?.amount}</span>
                                </div>

                                <div className="mt-8 text-center text-sm text-muted-foreground hidden print:block">
                                  <p>Thank you for your payment!</p>
                                  <p>
                                    Printed on: {new Date().toLocaleString()}
                                  </p>
                                </div>
                              </div>
                            </div>
                          </td>
                        </tr>
                      )}
                    </React.Fragment>
                  ))
                ) : (
                  <tr>
                    <td colSpan={7} className="p-8 text-center text-gray-500">
                      {receiptSearchTerm ||
                      selectedFilters.paymentMethod.length > 0 ||
                      selectedLocation !== "All Locations" ||
                      (dateRange.from && dateRange.to)
                        ? "No receipts found matching your filters."
                        : "No receipts available."}
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </React.Fragment>
  );
}
