import React, { useEffect, useState } from "react";
import { Calendar } from "@/components/ui/calendar";
import { CustomButton } from "@/components/ui/custom-button";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Calendar as CalendarIcon } from "lucide-react";
import {
  format,
  startOfWeek,
  endOfWeek,
  startOfMonth,
  endOfMonth,
  startOfYear,
  endOfYear,
} from "date-fns";

interface DateRange {
  from: Date | undefined;
  to: Date | undefined;
}

interface DateRangeFilterProps {
  onDateRangeChange: (dateRange: DateRange) => void;
  className?: string;
  isCustom?: boolean;
}

export function DateRangeFilter({
  onDateRangeChange,
  className = "",
  isCustom = true,
}: DateRangeFilterProps) {
  const [dateRange, setDateRange] = useState<DateRange>({
    from: new Date(),
    to: new Date(),
  });
  const [dateFilterType, setDateFilterType] = useState<string>(
    isCustom ? "custom" : "all"
  );
  const [isOpen, setIsOpen] = useState(false);

  useEffect(() => {
    // Update date range based on selected filter type
    let newRange: DateRange = {
      from: undefined,
      to: undefined,
    };

    switch (dateFilterType) {
      case "this-week":
        newRange = {
          from: startOfWeek(new Date(), { weekStartsOn: 0 }),
          to: endOfWeek(new Date(), { weekStartsOn: 0 }),
        };
        break;
      case "this-month":
        newRange = {
          from: startOfMonth(new Date()),
          to: endOfMonth(new Date()),
        };
        break;
      case "this-year":
        newRange = {
          from: startOfYear(new Date()),
          to: endOfYear(new Date()),
        };
        break;
      case "fiscal-year":
        // Assuming fiscal year starts July 1
        const today = new Date();
        const currentYear = today.getFullYear();
        const fiscalYearStart = new Date(
          today.getMonth() < 6 ? currentYear - 1 : currentYear,
          6, // July (0-indexed)
          1
        );
        const fiscalYearEnd = new Date(
          fiscalYearStart.getFullYear() + 1,
          5, // June (0-indexed)
          30
        );
        newRange = {
          from: fiscalYearStart,
          to: fiscalYearEnd,
        };
        break;
      case "custom":
        // Keep existing custom date range
        newRange = dateRange;
        break;
      case "all":
      default:
        // Leave as undefined
        break;
    }

    setDateRange(newRange);
    onDateRangeChange(newRange);
  }, [dateFilterType]);

  const handleCalendarChange = (value: Date | Date[]) => {
    if (Array.isArray(value) && value.length === 2) {
      const newRange = {
        from: value[0],
        to: value[1],
      };
      setDateRange(newRange);
      onDateRangeChange(newRange);
      setIsOpen(false);
    }
  };

  return (
    <div className={`flex items-center gap-2 ${className}`}>
      <Select value={dateFilterType} onValueChange={setDateFilterType}>
        <SelectTrigger className="w-[180px]">
          <SelectValue placeholder="Select date range" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="all">All Time</SelectItem>
          <SelectItem value="this-week">This Week</SelectItem>
          <SelectItem value="this-month">This Month</SelectItem>
          <SelectItem value="this-year">This Year</SelectItem>
          <SelectItem value="fiscal-year">This Fiscal Year</SelectItem>
          <SelectItem value="custom">Custom Range</SelectItem>
        </SelectContent>
      </Select>

      {dateFilterType === "custom" && (
        <Popover open={isOpen} onOpenChange={setIsOpen}>
          <PopoverTrigger asChild>
            <CustomButton variant="outline" size="sm" className="h-9">
              <CalendarIcon className="h-4 w-4 mr-2" />
              {dateRange.from ? (
                dateRange.to ? (
                  <>
                    {format(dateRange.from, "MMM d, yyyy")} -{" "}
                    {format(dateRange.to, "MMM d, yyyy")}
                  </>
                ) : (
                  format(dateRange.from, "MMM d, yyyy")
                )
              ) : (
                "Pick a date range"
              )}
            </CustomButton>
          </PopoverTrigger>
          <PopoverContent className="w-auto p-0" align="start">
            <Calendar
              selectRange={true}
              value={
                dateRange.from && dateRange.to
                  ? [dateRange.from, dateRange.to]
                  : undefined
              }
              onChange={handleCalendarChange}
            />
          </PopoverContent>
        </Popover>
      )}

      {dateRange.from && dateRange.to && dateFilterType !== "custom" && (
        <div className="text-sm text-gray-500">
          {format(dateRange.from, "MMM d, yyyy")} -{" "}
          {format(dateRange.to, "MMM d, yyyy")}
        </div>
      )}
    </div>
  );
}
