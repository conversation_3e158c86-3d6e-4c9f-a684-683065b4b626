import { useState } from "react";
import { useRouter } from "next/router";
import { CustomButton } from "components/ui/custom-button";
import { Input } from "components/ui/input";
import { Label } from "components/ui/label";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "components/ui/card";
import { AlertCircle, Eye, EyeOff } from "lucide-react";
import { Alert, AlertDescription } from "components/ui/alert";
import { supabaseClient } from "utils/supabase/client";
import { useAlert } from "context/useAlert";

export default function LoginForm() {
  const [username, setUsername] = useState("");
  const [password, setPassword] = useState("");
  const [error, setError] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const router = useRouter();
  const { showAlert } = useAlert();

  async function handleSubmit(e: React.FormEvent) {
    e.preventDefault();
    setError("");
    setIsLoading(true);

    try {
      const form = e.target as HTMLFormElement;
      const usernameInput = form.querySelector<HTMLInputElement>("#username");
      const passwordInput = form.querySelector<HTMLInputElement>("#password");

      if (!usernameInput?.value || !passwordInput?.value) {
        throw new Error("Both username and password are required");
      }

      const username = usernameInput.value.trim();
      const password = passwordInput.value;

      if (username.length < 3) {
        throw new Error("Username must be at least 3 characters");
      }

      if (password.length < 6) {
        throw new Error("Password must be at least 6 characters");
      }

      const { data: userData, error: userError } = await supabaseClient
        .from("users")
        .select("email")
        .eq("username", username)
        .single();

      if (userError || !userData) {
        throw new Error("Username not found. Please try again.");
      }

      const email = userData.email;

      const { data, error } = await supabaseClient.auth.signInWithPassword({
        email,
        password,
      });

      if (error) {
        throw new Error(error.message || "Failed to log in. Please try again.");
      }

      const role = data?.user?.user_metadata?.role?.toLowerCase();

      if (!role) {
        showAlert("User role not found. Please contact support.", "error");
        throw new Error("User role not found. Please contact support.");
      }

      showAlert("Login Successfully!", "success");

      switch (role) {
        case "admin":
        case "staff":
        case "volunteer":
          router.push("/membership");
          break;
        default:
          router.push("/points-checker");
      }
    } catch (err) {
      const message = err instanceof Error ? err.message : "An error occurred";
      showAlert(message, "error");
      setError(message);
    } finally {
      setIsLoading(false);
    }
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 px-4">
      <Card className="w-full max-w-md shadow-lg rounded-lg bg-white">
        <CardHeader className="text-center">
          <h2 className="text-xl font-bold bg-none text-blue-600">
            Welcome Back
          </h2>
          <CardDescription className="text-gray-500">
            Sign in to access your account
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-5">
            {error && (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}

            {/* Username Input */}
            <div>
              <Label htmlFor="username" className="text-sm font-medium">
                Username
              </Label>
              <Input
                id="username"
                type="text"
                placeholder="Enter your username"
                value={username}
                onChange={(e) => setUsername(e.target.value)}
                required
                autoComplete="username"
                disabled={isLoading}
                className="h-12 px-4 text-sm rounded-md"
                autoFocus
              />
            </div>

            {/* Password Input */}
            <div>
              <Label htmlFor="password" className="text-sm font-medium">
                Password
              </Label>
              <div className="relative">
                <Input
                  id="password"
                  type={showPassword ? "text" : "password"}
                  placeholder="••••••••"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  required
                  autoComplete="current-password"
                  disabled={isLoading}
                  className="h-12 px-4 text-sm rounded-md pr-10"
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700"
                  tabIndex={-1}
                >
                  {showPassword ? (
                    <EyeOff className="h-5 w-5" />
                  ) : (
                    <Eye className="h-5 w-5" />
                  )}
                </button>
              </div>
            </div>

            {/* Submit Button */}
            <CustomButton
              type="submit"
              className="w-full h-12 text-lg font-semibold bg-blue-600 text-white hover:bg-blue-700 transition"
              disabled={isLoading}
            >
              {isLoading ? "Logging in..." : "Login"}
            </CustomButton>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
