import { Input } from "@/components/ui/input";
import { CustomButton } from "@/components/ui/custom-button";
import { Pencil, Search, Trash2, Plus } from "lucide-react";
import React, { useState, useMemo } from "react";
import TableFilters from "../table-filters";
import { useFilters } from "hooks/useFilters";
import { useMemberData } from "context/useMemberData";
import { DataTable } from "@/components/data-table";
import AddUserForm from "./add-user-form";
import { getCurrentActiveMembership } from "utils";
import { useAuthCheck } from "hooks/useAuthCheck";
import { useClientSearch } from "context/useClientSearchContext";

export default function MemberManagement() {
  const { memberData, fetchMemberPage, memberPagination } = useMemberData();
  const { user } = useAuthCheck();
  const [showForm, setShowForm] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedUser, setSelectedUser] = useState<any>(null);
  const { selectedFilters, handleFilterChange } = useFilters({
    status: [],
  });
  const { searchResults, setSearchQuery, isSearching } = useClientSearch();

  // Filter configurations
  const memberFilters = {
    status: {
      label: "Status",
      options: [
        { id: "1", label: "Active", value: "active" },
        { id: "2", label: "Expired", value: "expired" },
      ],
    },
  };

  const memberUsers = useMemo(() => {
    if (!memberData) return [];

    return memberData.map((member) => ({
      id: member.id,
      name: `${member.first_name} ${member.last_name}`,
      email: member.email,
      contact_info: JSON.parse(member.contact_info),
      status:
        getCurrentActiveMembership(member.memberships)?.status || "inactive",
      username: member.username,
      points:
        getCurrentActiveMembership(member.memberships)?.available_points || 0,
      user_auth_id: member.user_auth_id,
      secondaryAddress: member.secondary_address,
      address: member.address,
      city: member.city,
      state: member.state,
      zip_code: member.zip_code,
      user_id: member.user_id,
      role: member.role,
      last_login: member.last_login,
      first_name: member.first_name,
      last_name: member.last_name,
      middle_initial: member.middle_initial,
      household_size: member.household_size,
      annual_income: member.annual_income,
      is_head_of_household: member.head_of_household,
      authorized_shopper: member.authorized_shopper,
      payment_method: getCurrentActiveMembership(member.memberships)
        ?.payments[0]?.method,
      membership_id: getCurrentActiveMembership(member.memberships)?.id,
      payment_id: getCurrentActiveMembership(member.memberships)?.payments[0]
        ?.id,
      additional_info: member.additionalInfo,
    }));
  }, [memberData]);

  const filterMembers = (members: typeof memberUsers) => {
    return members.filter((member) => {
      const authorizedShoppers = member.authorized_shopper
        ? JSON.parse(member.authorized_shopper)
        : [];

      const matchesSearch =
        member.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        member.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
        member.address.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (member.contact_info?.primary_number &&
          member.contact_info?.primary_number
            .toLowerCase()
            .includes(searchTerm.toLowerCase())) ||
        member.id.toString().includes(searchTerm.toString()) ||
        authorizedShoppers.some((shopper: string) =>
          shopper.toLowerCase().includes(searchTerm.toLowerCase())
        );

      const matchesStatus =
        selectedFilters.status.length === 0 ||
        selectedFilters.status.includes(member.status);

      return matchesSearch && matchesStatus;
    });
  };

  const isDisabled =
    user?.user_metadata?.role !== "admin" &&
    user?.user_metadata?.role !== "staff";

  const handleSelectedUser = (user: any) => {
    setSelectedUser(user);
    setShowForm(true);
  };

  const pageSize = memberPagination.pageSize;
  const [searchPage, setSearchPage] = useState(1);

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
    setSearchQuery({ query: e.target.value, role: "member" });
    setSearchPage(1);
  };

  const searchMode = searchTerm.trim().length > 0;
  const displayedMembers = searchMode
    ? searchResults
    : filterMembers(memberUsers);
  const total = searchMode ? searchResults.length : memberPagination.total;
  const totalPages = searchMode
    ? Math.max(1, Math.ceil(searchResults.length / pageSize))
    : memberPagination.totalPages;
  const currentPage = searchMode ? searchPage : memberPagination.page;
  const paginatedMembers = searchMode
    ? searchResults.slice((searchPage - 1) * pageSize, searchPage * pageSize)
    : displayedMembers;

  const handlePageChange = (newPage: number) => {
    if (searchMode) {
      setSearchPage(newPage);
    } else {
      fetchMemberPage(newPage);
    }
  };

  const headers = [
    "Member ID",
    "Name",
    "Username",
    "Email",
    "Phone",
    "Status",
    "Points",
    "Actions",
  ];

  const renderRow = (member: any) => (
    <tr key={member.id} className="border-b">
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
        {member.id}
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
        {member.name}
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
        {member.username}
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
        {member.email}
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
        {member.contact_info?.primary_number}
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
        <span
          className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
            member.status === "active"
              ? "bg-green-100 text-green-800"
              : member.status === "expired"
              ? "bg-red-100 text-red-800"
              : member.status === "inactive"
              ? "bg-orange-100 text-orange-800"
              : "bg-gray-100 text-gray-800"
          }`}
        >
          {member.status.charAt(0).toUpperCase() + member.status.slice(1)}
        </span>
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
        {member.points}
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
        <div className="flex justify-center space-x-2">
          <CustomButton
            variant="ghost"
            size="icon"
            onClick={() => handleSelectedUser(member)}
            disabled={isDisabled}
          >
            <Pencil className="h-4 w-4" />
          </CustomButton>
        </div>
      </td>
    </tr>
  );

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-semibold">Member Management</h2>
        <CustomButton
          variant="outline"
          onClick={() => {
            setShowForm(!showForm);
            setSelectedUser(null);
          }}
          disabled={isDisabled}
        >
          <Plus className="h-4 w-4 mr-2" />
          Add New Member
        </CustomButton>
      </div>

      <div className="flex gap-4 items-center mb-6">
        <div className="relative flex-1">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
          <Input
            placeholder="Search members..."
            className="pl-8"
            value={searchTerm}
            onChange={handleSearchChange}
          />
        </div>
        <TableFilters
          filters={memberFilters}
          selectedFilters={selectedFilters}
          onFilterChange={handleFilterChange}
        />
      </div>

      {showForm && (
        <AddUserForm
          role="member"
          setShowUserForm={setShowForm}
          selectedUser={selectedUser}
          setSelectedUser={setSelectedUser}
        />
      )}

      <DataTable
        headers={headers}
        data={paginatedMembers}
        renderRow={renderRow}
        currentPage={currentPage}
        totalPages={totalPages}
        pageSize={pageSize}
        total={total}
        onPageChange={handlePageChange}
      />

      {isSearching && searchTerm.trim().length > 0 && (
        <div className="mb-4 text-gray-500 text-center">Searching...</div>
      )}
    </div>
  );
}
