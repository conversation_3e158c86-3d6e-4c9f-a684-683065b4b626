import React, {
  createContext,
  useContext,
  useState,
  ReactNode,
  useCallback,
} from "react";
import { useClientSearch } from "./useClientSearchContext";

type SelectClientContextType = {
  selectedClient: any | null;
  setSelectedClient: React.Dispatch<React.SetStateAction<any | null>>;
  refreshSelectedClient: () => Promise<void>;
};

const SelectClientContext = createContext<SelectClientContextType | undefined>(
  undefined
);

export const useSelectClient = () => {
  const context = useContext(SelectClientContext);

  if (!context) {
    throw new Error(
      "useSelectClient must be used within a SelectClientProvider"
    );
  }

  return context;
};

export const SelectClientProvider = ({ children }: { children: ReactNode }) => {
  const [selectedClient, setSelectedClient] = useState<any | null>(null);
  const { fetchSearchResults } = useClientSearch();

  const refreshSelectedClient = useCallback(async () => {
    console.log("Refreshing selected client...");

    if (!selectedClient) return;

    const latestResults = await fetchSearchResults();

    const updatedClient = latestResults.find(
      (s: { id: any }) => s.id === selectedClient.id
    );

    if (updatedClient) {
      setSelectedClient(updatedClient);
    }
  }, [selectedClient, fetchSearchResults]);

  return (
    <SelectClientContext.Provider
      value={{
        selectedClient,
        setSelectedClient,
        refreshSelectedClient,
      }}
    >
      {children}
    </SelectClientContext.Provider>
  );
};
