import { NextApiRequest, NextApiResponse } from "next";
import { supabaseClient } from "utils/supabase/client";

export default async function DELETE(
  req: NextApiRequest,
  res: NextApiResponse
) {
  try {
    const { id, role } = req.body;

    if (!["admin", "staff"].includes(role)) {
      return res.status(403).json({
        message: "Unauthorized. Only staff or admin can delete held orders.",
      });
    }

    const { error: heldOrderError } = await supabaseClient
      .from("orders")
      .delete()
      .eq("id", id);

    if (heldOrderError) {
      return res.status(404).json({
        message: "Held Order not found",
      });
    }

    return res.status(200).json({ message: "Held Order deleted successfully" });
  } catch (error) {
    return res
      .status(500)
      .json({ message: "Internal Server Error", error: error.message });
  }
}
