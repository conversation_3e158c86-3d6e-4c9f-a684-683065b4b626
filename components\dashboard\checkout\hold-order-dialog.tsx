import React from "react";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "../../ui/dialog";
import { CustomButton } from "../../ui/custom-button";

interface HoldOrderDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  memberName: string;
}

export function HoldOrderDialog({
  isOpen,
  onClose,
  onConfirm,
  memberName,
}: HoldOrderDialogProps) {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Put Order on Hold</DialogTitle>
          <DialogDescription>
            Are you sure you want to put {memberName}'s order on hold? This will
            save the current cart and make it available in the "On Hold Orders"
            tab.
          </DialogDescription>
        </DialogHeader>
        <DialogFooter className="flex gap-2">
          <CustomButton variant="outline" onClick={onClose}>
            Cancel
          </CustomButton>
          <CustomButton
            onClick={() => {
              onConfirm();
              onClose();
            }}
          >
            Confirm
          </CustomButton>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
