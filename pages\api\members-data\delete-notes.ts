import { NextApiRequest, NextApiResponse } from "next";
import { supabaseClient } from "utils/supabase/client";

export default async function DELETE(
  req: NextApiRequest,
  res: NextApiResponse
) {
  try {
    const { id } = req.body;

    if (!id) {
      return res.status(400).json({ message: "Note ID is required" });
    }

    const { error } = await supabaseClient.from("notes").delete().eq("id", id);

    if (error) {
      return res
        .status(500)
        .json({ message: "Failed to delete the note", error: error.message });
    }

    return res.status(200).json({ message: "Note deleted successfully" });
  } catch (error) {
    return res
      .status(500)
      .json({ message: "Internal Server Error", error: error.message });
  }
}
