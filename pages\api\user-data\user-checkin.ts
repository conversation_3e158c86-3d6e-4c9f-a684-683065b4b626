import { supabaseClient } from "utils/supabase/client";

export default async function POST(req, res) {
  try {
    const {
      member_id,
      checked_in_by,
      is_forced_checked_in,
      added_store_location,
    } = req.body;

    //Check if the member has already checked in today
    const todayStart = new Date();
    todayStart.setHours(0, 0, 0, 0);
    const todayEnd = new Date();
    todayEnd.setHours(23, 59, 59, 999);

    const { data: existingCheckin, error: existingCheckinError } =
      await supabaseClient
        .from("checkins")
        .select("id")
        .eq("member_id", member_id)
        .gte("checkin_date", todayStart.toISOString())
        .lte("checkin_date", todayEnd.toISOString())
        .select();

    if (existingCheckinError) {
      console.error(
        "Error checking existing check-in data:",
        existingCheckinError.message
      );
      return res.status(500).json({
        message: "Error checking existing check-in data in the database",
        error: existingCheckinError.message,
      });
    }

    if (existingCheckin?.length > 0 && !is_forced_checked_in) {
      return res.status(400).json({
        message: "You have already checked in today.",
      });
    }

    const { data: userData, error: userError } = await supabaseClient
      .from("users")
      .select("id")
      .eq("user_auth_id", checked_in_by)
      .single();

    if (userError) {
      console.error("Error fetching user data:", userError.message);
      return res.status(500).json({
        message: "Error fetching user data to the database",
        error: userError.message,
      });
    }

    const { data: memberData, error: memberError } = await supabaseClient
      .from("members")
      .select("id")
      .eq("user_id", userData?.id)
      .single();

    if (memberError) {
      console.error("Error fetching member data:", memberError?.message);
      return res.status(500).json({
        message: "Error fetching member data to the database",
        error: memberError?.message,
      });
    }

    const { data: checkinData, error: checkinError } = await supabaseClient
      .from("checkins")
      .insert({
        member_id: member_id,
        checkin_date: new Date(),
        checked_in_by: memberData?.id,
        is_forced_checked_in,
        added_store_location,
      })
      .select();

    if (checkinError) {
      console.error("Error inserting checkin data:", checkinError.message);
      return res.status(500).json({
        message: "Error inserting checkin data to the database",
        error: checkinError.message,
      });
    }

    return res.status(200).json({
      message: "Checked in successfully!",
    });
  } catch (error) {
    console.error(error);
    return res.status(500).json({
      message: "Failed to process the request.",
    });
  }
}
