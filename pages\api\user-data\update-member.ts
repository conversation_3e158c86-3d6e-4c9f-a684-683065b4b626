import { checkEmailExist, checkUsernameExist } from "helper/supabase";
import { supabaseAdmin } from "utils/supabase/admin";
import { supabaseClient } from "utils/supabase/client";

export default async function handler(req, res) {
  if (req.method !== "PUT") {
    return res.status(405).json({ message: "Method Not Allowed" });
  }

  try {
    const {
      userAuthId,
      userId,
      memberId,
      firstName,
      middleInitial,
      lastName,
      username,
      email,
      newPassword,
      confirmNewPassword,
      contact_info,
      address,
      secondaryAddress,
      city,
      state,
      zipCode,
      currentEmail,
      currentUsername,
      paymentOption,
      authorizedShoppers,
      isHeadOfHousehold,
      householdSize,
      annualIncome,
      amountPaid,
      totalPoints,
      membershipId,
      paymentId,
      addedBy,
      currentAmountPaid,
      currentTotalPoints,
      preferredLanguage,
      housingType,
      personalInfo,
      householdMembers,
      ageGroups,
      householdNotes,
    } = req.body;

    if (
      !userId ||
      !memberId ||
      !firstName ||
      !lastName ||
      !username ||
      !email ||
      !contact_info ||
      !address ||
      !city ||
      !state ||
      !zipCode ||
      !paymentOption ||
      !isHeadOfHousehold ||
      !householdSize ||
      !annualIncome ||
      !amountPaid ||
      !totalPoints ||
      !membershipId ||
      !paymentId ||
      !addedBy
    ) {
      return res.status(400).json({ message: "Missing required fields" });
    }

    const { data: userData, error: userError } = await supabaseClient
      .from("users")
      .select("id, role")
      .eq("user_auth_id", addedBy)
      .single();

    if (userError) {
      return res.status(404).json({
        message: "User not found",
      });
    }

    if (!userData || !["admin"].includes(userData.role)) {
      return res.status(403).json({
        message: "Unauthorized. Only admin can update user data.",
      });
    }

    // Check for email and username conflicts
    if (email !== currentEmail) {
      const emailError = await checkEmailExist(email);
      if (emailError) {
        return res
          .status(400)
          .json({ message: "This email is already in use" });
      }
    }

    if (username !== currentUsername) {
      const usernameError = await checkUsernameExist(username);
      if (usernameError) {
        return res
          .status(400)
          .json({ message: "This username is already taken" });
      }
    }

    // Update password if provided
    if (newPassword && confirmNewPassword) {
      if (newPassword !== confirmNewPassword) {
        return res.status(400).json({ message: "Passwords do not match" });
      }

      const { error: passwordUpdateError } = await supabaseAdmin.updateUserById(
        userAuthId,
        {
          password: newPassword,
        }
      );

      if (passwordUpdateError) {
        return res.status(400).json({ message: "Failed to update password" });
      }
    }

    // Update user metadata (username and email)
    const metadataUpdates: any = {};
    if (username !== currentUsername)
      metadataUpdates.user_metadata = { username };
    if (email !== currentEmail) metadataUpdates.email = email;

    if (Object.keys(metadataUpdates).length > 0) {
      const { error: metadataUpdateError } = await supabaseAdmin.updateUserById(
        userAuthId,
        metadataUpdates
      );
      if (metadataUpdateError) {
        return res
          .status(400)
          .json({ message: "Failed to update user metadata" });
      }
    }

    // Update user information
    const userUpdateData = {
      first_name: firstName,
      last_name: lastName,
      email,
      username,
    };

    const { error: updateUserError } = await supabaseClient
      .from("users")
      .update(userUpdateData)
      .eq("user_auth_id", userAuthId);

    if (updateUserError) {
      return res.status(400).json({ message: "Failed to update user" });
    }

    // Update member details
    const memberUpdateData = {
      address,
      secondary_address: secondaryAddress,
      contact_info,
      first_name: firstName,
      last_name: lastName,
      middle_initial: middleInitial,
      city,
      state,
      zip_code: zipCode,
      head_of_household: isHeadOfHousehold,
      authorized_shopper: authorizedShoppers.split(","),
      household_size: householdSize,
      annual_income: annualIncome,
      updated_at: new Date().toISOString(),
    };

    const { error: updateMemberError } = await supabaseClient
      .from("members")
      .update(memberUpdateData)
      .eq("id", memberId);

    if (updateMemberError) {
      return res.status(400).json({ message: "Failed to update member" });
    }

    const additionalInfoUpdateData = {
      household_members: householdMembers,
      personal_info: personalInfo,
      housing_type: housingType,
      preferred_language: preferredLanguage,
      age_groups: ageGroups,
      household_note_verification: householdNotes,
    };

    const { error: updateAdditionalInfoError } = await supabaseClient
      .from("additional_info")
      .update(additionalInfoUpdateData)
      .eq("member_id", memberId);

    if (updateAdditionalInfoError) {
      return res
        .status(400)
        .json({ message: "Failed to update additional info" });
    }

    if (
      currentAmountPaid !== amountPaid ||
      currentTotalPoints !== totalPoints
    ) {
      const { error: updateMembershipError } = await supabaseClient
        .from("memberships")
        .update({
          total_paid: amountPaid,
          total_points: totalPoints,
          updated_by: addedBy,
        })
        .eq("id", membershipId);

      if (updateMembershipError) {
        console.error("Error updating membership:", updateMembershipError);
      }
    }

    const { error: paymentError } = await supabaseClient
      .from("payments")
      .update({
        amount: amountPaid,
        method: paymentOption,
      })
      .eq("id", paymentId);

    if (paymentError) {
      return res.status(400).json({ message: "Failed to update payment" });
    }

    return res.status(200).json({ message: "Staff updated successfully" });
  } catch (error) {
    console.error("Error updating staff:", error);
    return res.status(500).json({
      message: "Internal server error",
      error: error.message || "Unknown error occurred",
    });
  }
}
