import {
  customText,
  logo,
  plainText,
  socialLinks,
  title,
} from "../../../common/fields"
import { hideIfVariantIn } from "@webriq-pagebuilder/sanity-plugin-schema-default"

export const footerSchema = [
  logo(hideIfVariantIn(["variant_e"])),
  plainText(hideIfVariantIn(["variant_b", "variant_c", "variant_e"])),
  customText(
    "contactDetails",
    "Contact Details",
    "Enter contact information to display at the footer of the page.",
    "",
    3,
    hideIfVariantIn(["variant_b", "variant_c", "variant_d", "variant_e"])
  ),


  customText(
    "copyright",
    "Footer text",
    "Add footer text by typing in the text field below. Example: 'Copyright' or 'Established'",
    "© 2021. All rights reserved.",
    1
  ),
  socialLinks(),

  {
    name: "menu",
    title: "Navigation menu",
    hidden: hideIfVariantIn(["variant_a", "variant_d", "variant_e"]),
    description:
      "Click the 'Add item' button to add navigation menu. If you want to edit what is added, click this ⋮ icon found on its right.",
    type: "array",
    of: [{ type: "conditionalLink" }],
  },

  {
    name: "multipleMenus",
    title: "Navigation menus",
    hidden: hideIfVariantIn(["variant_a", "variant_b", "variant_c", "variant_e"]),
    description:
      "Click the 'Add item' button to add a navigation menu. If you want to edit what is added, click this ⋮ icon found on its right.",
    type: "array",
    of: [
      {
        type: "object",
        fields: [
          title(),
          {
            name: "links",
            title: "Links",
            description:
              "Click the 'Add item' button to add a link. If you want to edit what is added, click this ⋮ icon found on its right.",
            type: "array",
            of: [{ type: "conditionalLink" }],
          },
        ],
      },
    ],
  },
]
