import { PortableText } from "@portabletext/react";
import { Container } from "@stackshift-ui/container";
import { Flex } from "@stackshift-ui/flex";
import { Heading } from "@stackshift-ui/heading";
import { Section } from "@stackshift-ui/section";
import { TextComponentProps, ButtonProps } from ".";
import { MyPortableTextComponents } from "types";
import { urlFor } from "lib/sanity";
import Image from "next/image";
import { usePathname } from "next/navigation";

import { Button } from "@stackshift-ui/button";

export default function TextComponent_H({
  heading,
  firstColumn,
  primaryButton,
  secondaryButton,
}: TextComponentProps) {
  const pathname = usePathname();

  // block styling as props to `components` of the PortableText component
  const textComponentBlockStyling: MyPortableTextComponents = {
    block: {
      h1: ({ children }) => (
        <Heading
          fontSize="3xl"
          weight="bold"
          className="mb-8 leading-normal text-black dark:text-white"
        >
          {children}
        </Heading>
      ),
      h2: ({ children }) => (
        <Heading
          type="h2"
          weight="light"
          fontSize="4xl"
          className="mb-2 !text-cfc-secondary dark:text-white"
        >
          {children}
        </Heading>
      ),
      h3: ({ children }) => (
        <Heading
          type="h3"
          fontSize="xl"
          className="mb-8 leading-normal font-extrabold !text-cfc-gray dark:text-white"
        >
          {children}
        </Heading>
      ),
      h4: ({ children }) => (
        <Heading
          type="h4"
          weight="bold"
          fontSize="lg"
          className="mb-8 leading-normal text-black dark:text-white"
        >
          {children}
        </Heading>
      ),
      normal: ({ children }) => (
        <p className="mb-8 leading-relaxed text-cfc-gray text-base">
          {children}
        </p>
      ),
      blockquote: ({ children }) => (
        <blockquote className="mb-6 italic leading-loose text-gray-500 px-14">
          - {children}
        </blockquote>
      ),
    },
    code: ({ value }) => (
      <pre data-language={value.language}>
        <code>{value.code}</code>
      </pre>
    ),
    list: {
      bullet: ({ children }) => (
        <ul className="flex flex-col pl-10 mb-8 space-y-4 leading-relaxed list-disc">
          {children}
        </ul>
      ),
      number: ({ children }) => (
        <ol className="flex flex-col pl-10 mb-8 space-y-4 leading-relaxed list-decimal">
          {children}
        </ol>
      ),
    },
    listItem: {
      bullet: ({ children }) => <li className="leading-relaxed">{children}</li>,
      number: ({ children }) => <li className="leading-relaxed">{children}</li>,
    },
    marks: {
      strong: ({ children }) => <strong>{children}</strong>,
      em: ({ children }) => <em>{children}</em>,
      code: ({ children }) => <code>{children}</code>,
      link: ({ children, value }) => (
        <a
          className="hover:text-primary-foreground text-primary"
          href={value.href}
          target="_blank"
          rel="noopener noreferrer"
        >
          {children}
        </a>
      ),
    },

    types: {
      addImage: ({ value }) => (
        <Image
          className="w-full h-full mb-5"
          width={500}
          height={500}
          sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
          src={urlFor(value?.image)}
          alt={value?.alt ?? value?.image?.asset?._ref}
        />
      ),

      addIframe: ({ value }) => {
        return (
          <div className="relative flex">
            <iframe
              loading="lazy"
              frameBorder="0"
              src={value?.iframe}
              width="100%"
              data-rocket-lazyload="fitvidscompatible"
              className={`entered w-full ${
                pathname.includes("monthly-giving")
                  ? "md:h-[70rem] h-[80rem]"
                  : "h-[85rem]"
              }`}
            />
          </div>
        );
      },

      contentTable: ({ value }) => {
        return value?.cells?.map((cell, idx) => (
          <div key={idx} className="flex items-center border-t p-2.5">
            {cell.rowArray?.map((row, index) => (
              <span
                key={index}
                className={`p-2 ${index === 0 ? "flex-[2]" : "flex-1"}`}
              >
                {row}
              </span>
            ))}
          </div>
        ));
      },
    },
  };
  return (
    <Section className="md:pb-32 py-10 bg-white relative overflow-hidden">
      <Container maxWidth={1280}>
        {heading && (
          <Heading
            type="h2"
            fontSize="4xl"
            weight="thin"
            className="text-center !text-cfc-secondary"
          >
            {heading}
          </Heading>
        )}

        <Flex wrap justify="center" className="mx-auto">
          {firstColumn && (
            <div className="mb-2 text-xs w-full text-center overflow-visible">
              <PortableText
                value={firstColumn}
                components={textComponentBlockStyling}
                onMissingComponent={false} // Disabling warnings / handling unknown types
              />
            </div>
          )}

          <Buttons
            primaryButton={primaryButton}
            secondaryButton={secondaryButton}
          />
        </Flex>
      </Container>
    </Section>
  );
}

import {
  MdOutlineArrowCircleRight,
  MdOutlineArrowCircleLeft,
} from "react-icons/md";

function Buttons({
  primaryButton,
  secondaryButton,
}: {
  primaryButton?: ButtonProps;
  secondaryButton?: ButtonProps;
}) {
  return (
    <Flex
      align="center"
      gap={2}
      className={`
      w-full md:max-w-5xl px-4 md:justify-between justify-center md:flex-row flex-col md:gap-0 gap-4`}
    >
      {primaryButton?.label && (
        <Button
          as="link"
          link={primaryButton}
          ariaLabel={primaryButton?.label}
          className="bg-[#5cb85c] hover:scale-110 gap-2 items-center justify-center flex flex-row rounded-2xl text-white font-semibold hover:bg-cfc-primary-foreground ease-in-out transition duration-300 px-6 py-2"
        >
          <MdOutlineArrowCircleLeft />
          {primaryButton?.label}
        </Button>
      )}
      {secondaryButton?.label && (
        <Button
          as="link"
          link={secondaryButton}
          className="bg-[#5cb85c] gap-2 hover:scale-110 items-center justify-center rounded-2xl text-white font-semibold hover:bg-cfc-primary-foreground flex flex-row transition duration-300 ease-in-out px-6 py-2"
          ariaLabel={secondaryButton?.label}
        >
          {secondaryButton?.label}
          <MdOutlineArrowCircleRight />
        </Button>
      )}
    </Flex>
  );
}

export { TextComponent_H };
