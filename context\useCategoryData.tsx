import React, {
  createContext,
  useState,
  useContext,
  useEffect,
  ReactNode,
} from "react";
import axios from "axios";
import { useAuthCheck } from "hooks/useAuthCheck";

interface CategoryContextType {
  categoryData: any;
  fetchingCategory: boolean;
  setFetchingCategory: React.Dispatch<React.SetStateAction<boolean>>;
  refreshCategory: () => void;
}

const CategoryContext = createContext<CategoryContextType | null>(null);

export const useCategoryData = () => {
  const context = useContext(CategoryContext);
  if (!context) {
    throw new Error(
      "useCategoryData must be used within a CategoryDataProvider"
    );
  }
  return context;
};

interface CategoryDataProviderProps {
  children: ReactNode;
}

export const CategoryDataProvider: React.FC<CategoryDataProviderProps> = ({
  children,
}) => {
  const [categoryData, setCategoryData] = useState<any>([]);
  const [fetchingCategory, setFetchingCategory] = useState<boolean>(false);
  const { user, token } = useAuthCheck();

  const fetchData = async () => {
    setFetchingCategory(true);
    try {
      const response = await axios.get("/api/admin/category/get-category", {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        params: { role: user?.user_metadata?.role },
      });
      setCategoryData(response.data);
    } catch (error) {
      console.error("Error fetching category data:", error);
    } finally {
      setFetchingCategory(false);
    }
  };

  const refreshCategory = () => {
    fetchData();
  };

  useEffect(() => {
    if (user) {
      fetchData();
    }
  }, [user]);

  return (
    <CategoryContext.Provider
      value={{
        categoryData,
        fetchingCategory,
        setFetchingCategory,
        refreshCategory,
      }}
    >
      {children}
    </CategoryContext.Provider>
  );
};
