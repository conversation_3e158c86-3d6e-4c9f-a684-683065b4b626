import {
  arrayOfImages,
  description,
  formLinks,
  signInLink,
  mainImage,
  plainText,
  primaryButton,
  secondaryButton,
  title,
  webriqForms,
  youtubeLink,
  blockContentNormalStyle,
} from "../../../common/fields";
import { hideIfVariantIn } from "@webriq-pagebuilder/sanity-plugin-schema-default";

export const headerSchema = [
  title(hideIfVariantIn(["variant_p", "variant_q"])),
  description(
    hideIfVariantIn([
      "variant_c",
      "variant_h",
      "variant_k",
      "variant_n",
      "variant_p",
      "variant_q",
    ])
  ),

  blockContentNormalStyle(
    "firstColumn",
    "Content",
    "Add text content in a single column.",
    hideIfVariantIn([
      "variant_a",
      "variant_b",
      "variant_c",
      "variant_d",
      "variant_e",
      "variant_f",
      "variant_g",
      "variant_h",
      "variant_i",
      "variant_j",
      "variant_m",
      "variant_n",
      "variant_r",
    ])
  ),

  blockContentNormalStyle(
    "secondColumn",
    "Second Content",
    "Add text content in a single column.",
    hideIfVariantIn([
      "variant_a",
      "variant_b",
      "variant_c",
      "variant_d",
      "variant_e",
      "variant_f",
      "variant_g",
      "variant_h",
      "variant_i",
      "variant_j",
      "variant_k",
      "variant_m",
      "variant_n",
      "variant_r",
    ])
  ),

  primaryButton(
    hideIfVariantIn([
      "variant_a",
      "variant_b",
      "variant_c",
      "variant_d",
      "variant_e",
      "variant_f",
      "variant_h",
      "variant_j",
      "variant_k",
      "variant_m",
      "variant_n",
      "variant_o",
      "variant_p",
    ])
  ),
  secondaryButton(
    hideIfVariantIn([
      "variant_a",
      "variant_b",
      "variant_c",
      "variant_d",
      "variant_e",
      "variant_f",
      "variant_h",
      "variant_i",
      "variant_k",
      "variant_j",
      "variant_m",
      "variant_n",
      "variant_p",
      "variant_q",
      "variant_r",
    ])
  ),
  arrayOfImages(
    hideIfVariantIn([
      "variant_a",
      "variant_c",
      "variant_d",
      "variant_e",
      "variant_g",
      "variant_h",
      "variant_i",
      "variant_j",
      "variant_k",
      "variant_l",
      "variant_m",
      "variant_n",
      "variant_o",
      "variant_p",
      "variant_q",
      "variant_r",
    ])
  ),
  mainImage(
    hideIfVariantIn([
      "variant_b",
      "variant_c",
      "variant_e",
      "variant_h",
      "variant_n",
    ])
  ),

  {
    name: "bgImage",
    type: "image",
    title: "Upload an image for your background Image to display.",
    hidden: hideIfVariantIn([
      "variant_a",
      "variant_b",
      "variant_c",
      "variant_d",
      "variant_e",
      "variant_f",
      "variant_h",
      "variant_i",
      "variant_j",
      "variant_k",
      "variant_l",
      "variant_m",
      "variant_o",
      "variant_p",
      "variant_q",
      "variant_r",
    ]),
  },

  {
    name: "videos",
    title: "Videos",
    description: "Add the youtube video url",
    type: "array",
    hidden: hideIfVariantIn([
      "variant_a",
      "variant_b",
      "variant_d",
      "variant_e",
      "variant_g",
      "variant_i",
      "variant_j",
      "variant_k",
      "variant_l",
      "variant_m",
      "variant_n",
      "variant_o",
      "variant_p",
      "variant_q",
      "variant_r",
    ]),
    of: [
      {
        type: "object",
        title: "video",
        fields: [title(), youtubeLink()],
      },
    ],
  },

  webriqForms(
    hideIfVariantIn([
      "variant_a",
      "variant_b",
      "variant_c",
      "variant_d",
      "variant_g",
      "variant_h",
      "variant_i",
      "variant_k",
      "variant_j",
      "variant_m",
      "variant_n",
      "variant_o",
      "variant_p",
      "variant_q",
      "variant_r",
    ])
  ),
  formLinks(
    hideIfVariantIn([
      "variant_a",
      "variant_b",
      "variant_c",
      "variant_d",
      "variant_g",
      "variant_h",
      "variant_i",
      "variant_j",
      "variant_k",
      "variant_l",
      "variant_m",
      "variant_n",
      "variant_o",
      "variant_p",
      "variant_q",
      "variant_r",
    ])
  ),

  youtubeLink(
    hideIfVariantIn([
      "variant_a",
      "variant_b",
      "variant_d",
      "variant_e",
      "variant_g",
      "variant_h",
      "variant_i",
      "variant_j",
      "variant_k",
      "variant_l",
      "variant_m",
      "variant_n",
      "variant_o",
      "variant_p",
      "variant_q",
      "variant_r",
    ])
  ),
];
