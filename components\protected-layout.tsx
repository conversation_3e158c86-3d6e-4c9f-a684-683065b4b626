import { ReactNode } from "react";
import { MemberDataProvider } from "context/useMemberData";
import { SelectClientProvider } from "context/useSelectClientContext";
import { CategoryDataProvider } from "context/useCategoryData";
import { ProductDataProvider } from "context/useProductCategoryData";
import { ReturnItemProvider } from "context/useReturnItem";
import { CheckedInTodayProvider } from "context/useCheckedInToday";
import { ClientSearchProvider } from "context/useClientSearchContext";
import { HeldOrderDataProvider } from "context/useHeldOrderData";
import { LanguageDataProvider } from "context/useLanguageData";

interface ProtectedLayoutProps {
  children: ReactNode;
}

export default function ProtectedLayout({ children }: ProtectedLayoutProps) {
  return (
    <MemberDataProvider>
      <HeldOrderDataProvider>
        <ClientSearchProvider>
          <SelectClientProvider>
            <CategoryDataProvider>
              <ProductDataProvider>
                <ReturnItemProvider>
                  <LanguageDataProvider>
                    <CheckedInTodayProvider>{children}</CheckedInTodayProvider>
                  </LanguageDataProvider>
                </ReturnItemProvider>
              </ProductDataProvider>
            </CategoryDataProvider>
          </SelectClientProvider>
        </ClientSearchProvider>
      </HeldOrderDataProvider>
    </MemberDataProvider>
  );
}
