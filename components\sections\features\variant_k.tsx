import { Container } from "@stackshift-ui/container";
import { Flex } from "@stackshift-ui/flex";
import { Heading } from "@stackshift-ui/heading";
import { Image } from "@stackshift-ui/image";
import { Section } from "@stackshift-ui/section";
import { Text } from "@stackshift-ui/text";
import React from "react";
import { FeaturesProps } from ".";
import { ArrayOfImageTitleAndText, Images } from "../../../types";

interface FeatureItemProps {
  feature: {
    caption?: string | null;
    title?: string | null;
    plainText?: string | null;
    mainImage?: Images;
  };
}

export default function Features_K({
  caption,
  title,
  features,
}: FeaturesProps) {
  return (
    <Section className="py-10 bg-white w-full relative">
      <Container maxWidth={1280}>
        <Flex className="w-full lg:w-10/12 mx-auto">
          <FeatureItems features={features} />
        </Flex>
      </Container>
    </Section>
  );
}

function CaptionAndTitleText({
  caption,
  title,
}: {
  caption?: string;
  title?: string;
}) {
  return (
    <React.Fragment>
      {caption ? (
        <Text weight="bold" className="text-secondary">
          {caption}
        </Text>
      ) : null}
      {title ? <Heading fontSize="3xl">{title}</Heading> : null}
    </React.Fragment>
  );
}

function FeatureItems({ features }: { features?: ArrayOfImageTitleAndText[] }) {
  if (!features) return null;

  return (
    <Flex className="grid md:grid-cols-4 grid-cols-1 w-full h-full lg:gap-4 gap-2 rounded">
      {features?.map((feature) => {
        return <FeatureItem feature={feature} key={feature._key} />;
      })}
    </Flex>
  );
}

function FeatureItem({ feature }: FeatureItemProps) {
  return (
    <div className="w-full h-full  flex items-center justify-start flex-col border-2 border-gray-300 shadow-md rounded py-2 lg:px-4 md:px-1 px-4 bg-[#EDF7E7] hover:bg-[#61CE70]/20">
      {feature?.mainImage?.image ? <FeatureImage feature={feature} /> : null}

      {feature?.title ? (
        <Text
          weight="extrabold"
          className="mb-4 !text-center text-[#7a7a7a] !text-2xl leading-none"
        >
          {feature?.title}
        </Text>
      ) : null}
      {feature?.plainText ? (
        <Text
          muted
          className="leading-relaxed tracking-none !text-center mb-4 !text-base "
        >
          {feature?.plainText}
        </Text>
      ) : null}
    </div>
  );
}

function FeatureImage({ feature }: FeatureItemProps) {
  if (!feature?.mainImage?.image) return null;

  return (
    <span className="inline-block p-3 rounded bg-transparent">
      <Image
        className="object-cover w-1/2 mx-auto bg-transparent"
        src={`${feature?.mainImage?.image}`}
        width={100}
        height={100}
        alt={feature?.mainImage?.alt ?? `features-image-`}
      />
    </span>
  );
}

export { Features_K };
