"use client";

import {
  createContext,
  useCallback,
  useContext,
  useState,
  useEffect,
} from "react";
import { CustomAlert } from "@/components/ui/custom-alert";
import { createPortal } from "react-dom";

interface Alert {
  id: string;
  message: string;
  variant: "success" | "error";
}

interface AlertContextType {
  showAlert: (message: string, variant: "success" | "error") => void;
}

const AlertContext = createContext<AlertContextType | undefined>(undefined);

export function AlertProvider({ children }: { children: React.ReactNode }) {
  const [alerts, setAlerts] = useState<Alert | null>(null);
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
    return () => setMounted(false);
  }, []);

  const showAlert = useCallback(
    (message: string, variant: "success" | "error") => {
      const id = Math.random().toString(36).slice(2, 11);
      setAlerts({ id, message, variant });
    },
    []
  );

  const removeAlert = useCallback(() => {
    setAlerts(null);
  }, []);

  return (
    <AlertContext.Provider value={{ showAlert }}>
      {children}
      {mounted &&
        alerts &&
        (createPortal(
          <div className="fixed top-4 right-4 z-50 min-w-[300px] max-w-[500px]">
            <div className="animate-in slide-in-from-right duration-300">
              <CustomAlert
                variant={alerts.variant}
                message={alerts.message}
                onDismiss={removeAlert}
              />
            </div>
          </div>,
          document.body
        ) as React.ReactPortal)}
    </AlertContext.Provider>
  );
}

export const useAlert = () => {
  const context = useContext(AlertContext);
  if (context === undefined) {
    throw new Error("useAlert must be used within an AlertProvider");
  }
  return context;
};
