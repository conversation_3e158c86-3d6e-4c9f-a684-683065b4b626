import React from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Footer,
} from "../../ui/dialog";
import { CustomButton } from "../../ui/custom-button";
import { Input } from "../../ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../../ui/select";
import { Label } from "../../ui/label";
import { useAlert } from "context/useAlert";
import { useAuthCheck } from "hooks/useAuthCheck";
import { useReturnItem } from "context/useReturnItem";
import { useLocation } from "hooks/useLocation";
import { useProductData } from "context/useProductCategoryData";
import { useClientSearch } from "context/useClientSearchContext";
import { Search, X } from "lucide-react";
import { formatDate, getCurrentActiveMembership } from "utils";

interface ReturnItemFormProps {
  isOpen: boolean;
  onClose: () => void;
}

export interface ReturnItemData {
  productId: string;
  quantity: number;
  unitPoints: number;
  totalPoints: number;
  reason: string;
  orderId: string;
  itemIndex: number;
}

interface ClientData {
  id: string;
  first_name: string;
  last_name: string;
  address: string;
  check_in?: any[];
  orders?: any[];
  [key: string]: any;
}

interface OrderItem {
  product_id: string;
  product_name: string;
  quantity: number;
  unit_points: number;
  order_id: string;
  item_index: number;
  returned_quantity?: number;
}

export function ReturnItemForm({ isOpen, onClose }: ReturnItemFormProps) {
  const { productData: products } = useProductData();
  const { showAlert } = useAlert();
  const { user, token } = useAuthCheck();
  const { city, region, country, error } = useLocation();
  const { searchQuery, setSearchQuery, searchResults, isSearching } =
    useClientSearch();

  const [formData, setFormData] = React.useState<ReturnItemData>({
    productId: "",
    quantity: 1,
    unitPoints: 0,
    totalPoints: 0,
    reason: "",
    orderId: "",
    itemIndex: 0,
  });
  const [isLoading, setIsLoading] = React.useState(false);
  const [selectedClient, setSelectedClient] = React.useState<ClientData | null>(
    null
  );
  const [isSearchMode, setIsSearchMode] = React.useState(false);
  const [availableProducts, setAvailableProducts] = React.useState<OrderItem[]>(
    []
  );
  const [maxQuantity, setMaxQuantity] = React.useState(1);

  const activeMembership = getCurrentActiveMembership(
    selectedClient?.memberships
  );

  const getTodaysOrderItems = React.useCallback(
    (client: ClientData) => {
      if (!client.orders) return [];

      const today = new Date();
      const todayString = today.toISOString().split("T")[0];

      const todaysOrders = client.orders.filter((order: any) => {
        const orderDate = new Date(order.date_of_order);
        const orderDateString = orderDate.toISOString().split("T")[0];
        return orderDateString === todayString;
      });

      // Flatten all items from today's orders
      const allItems: OrderItem[] = [];
      todaysOrders.forEach((order: any) => {
        if (order.items && Array.isArray(order.items)) {
          order.items.forEach((item: any, index: number) => {
            // Calculate available quantity (original - returned)
            const returnedQuantity = item.returned_quantity || 0;
            const availableQuantity = item.quantity - returnedQuantity;

            // Only include items that haven't been fully returned
            if (availableQuantity > 0) {
              // Check if this product is already in the list
              const existingItem = allItems.find(
                (i) => i.product_id === item.product_id
              );

              // Get unit points from product data if not available in order item
              const productFromData = products.find(
                (p) => p.id === item.product_id
              );
              const unitPoints =
                item.unit_points || productFromData?.unit_points || 0;

              if (existingItem) {
                existingItem.quantity += availableQuantity;
              } else {
                allItems.push({
                  product_id: item.product_id,
                  product_name: item.product_name,
                  quantity: availableQuantity,
                  unit_points: unitPoints,
                  order_id: order.id,
                  item_index: index,
                  returned_quantity: returnedQuantity,
                });
              }
            }
          });
        }
      });

      return allItems;
    },
    [products]
  );

  React.useEffect(() => {
    if (selectedClient) {
      const todaysItems = getTodaysOrderItems(selectedClient);
      setAvailableProducts(todaysItems);

      if (todaysItems.length > 0) {
        const firstItem = todaysItems[0];
        setFormData({
          productId: firstItem.product_id,
          quantity: 1,
          unitPoints: firstItem.unit_points,
          totalPoints: firstItem.unit_points,
          reason: "",
          orderId: firstItem.order_id,
          itemIndex: firstItem.item_index,
        });
        setMaxQuantity(firstItem.quantity);
      } else {
        setFormData({
          productId: "",
          quantity: 1,
          unitPoints: 0,
          totalPoints: 0,
          reason: "",
          orderId: "",
          itemIndex: 0,
        });
        setMaxQuantity(1);
      }
    }
  }, [selectedClient, getTodaysOrderItems]);

  React.useEffect(() => {
    if (isOpen && !selectedClient) {
      setFormData({
        productId: "",
        quantity: 1,
        unitPoints: 0,
        totalPoints: 0,
        reason: "",
        orderId: "",
        itemIndex: 0,
      });
      setAvailableProducts([]);
      setMaxQuantity(1);
    }
  }, [isOpen, selectedClient]);

  const handleSearch = (query: string) => {
    setSearchQuery({ query, role: user?.user_metadata?.role });
    if (!query) {
      setSelectedClient(null);
    }
  };

  const clearSearch = () => {
    setSearchQuery({ query: "", role: user?.user_metadata?.role });
    setSelectedClient(null);
  };

  const selectClient = (client: ClientData) => {
    setSelectedClient(client);
    setSearchQuery({
      query: `${client.first_name} ${client.last_name}`,
      role: user?.user_metadata?.role,
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!selectedClient) {
      showAlert("Please select a member first", "error");
      return;
    }

    if (availableProducts.length === 0) {
      showAlert("No orders found for today", "error");
      return;
    }

    setIsLoading(true);

    try {
      const response = await fetch("/api/user-data/return-item/insert", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({
          ...formData,
          store_location: !error ? `${city}, ${region}, ${country}` : error,
          role: user?.user_metadata?.role,
          member_id: selectedClient.id,
          membership_id: activeMembership?.id,
          auth_user: user?.id,
        }),
      });
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || "Failed to submit return");
      }

      setIsLoading(false);
      onClose();
      showAlert(data.message, "success");
      setFormData({
        productId: "",
        quantity: 1,
        unitPoints: 0,
        totalPoints: 0,
        reason: "",
        orderId: "",
        itemIndex: 0,
      });
      setSelectedClient(null);
      clearSearch();
    } catch (error) {
      console.error("Error submitting return:", error);
      showAlert(
        error instanceof Error
          ? error.message
          : "Failed to complete transaction",
        "error"
      );
      setIsLoading(false);
    }
  };

  const handleProductChange = (value: string) => {
    const selectedProduct = availableProducts.find(
      (p) => p.product_id === value
    );
    if (selectedProduct) {
      setFormData((prev) => ({
        ...prev,
        productId: value,
        unitPoints: selectedProduct.unit_points,
        totalPoints: selectedProduct.unit_points * 1,
        quantity: 1,
        orderId: selectedProduct.order_id,
        itemIndex: selectedProduct.item_index,
      }));
      setMaxQuantity(selectedProduct.quantity);
    }
  };

  const handleQuantityChange = (value: number) => {
    const clampedValue = Math.min(Math.max(1, value), maxQuantity);
    setFormData((prev) => ({
      ...prev,
      quantity: clampedValue,
      totalPoints: prev.unitPoints * clampedValue,
    }));
  };

  const getLastCheckout = (checkIns: any[] = []) => {
    const checkouts = checkIns
      .filter((event) => event.checkout_date)
      .sort(
        (a, b) =>
          new Date(b.checkout_date).getTime() -
          new Date(a.checkout_date).getTime()
      );

    return checkouts.length > 0 ? checkouts[0] : null;
  };

  const lastCheckout = getLastCheckout(
    searchResults[0]?.check_in || selectedClient?.check_in || []
  );

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-lg max-h-[90vh] overflow-y-auto bg-white rounded-lg shadow-lg p-4">
        <DialogHeader>
          <DialogTitle className="text-lg font-semibold text-gray-800">
            Return Item
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4 p-2 rounded-lg">
          <div className="space-y-1">
            <div className="flex justify-between items-center">
              <Label
                htmlFor="client"
                className="text-sm font-medium text-gray-700"
              >
                Member Search
              </Label>
              {selectedClient && !isSearchMode && (
                <button
                  type="button"
                  onClick={() => {
                    setIsSearchMode(true);
                    setSearchQuery({
                      query: "",
                      role: user?.user_metadata?.role,
                    });
                  }}
                  className="text-xs text-blue-600 hover:text-blue-800"
                >
                  Search for another member
                </button>
              )}
            </div>

            {!selectedClient || isSearchMode ? (
              <MemberSearchInput
                searchQuery={searchQuery}
                handleSearch={handleSearch}
                isSearching={isSearching}
                searchResults={searchResults}
                selectClient={selectClient}
                selectedClient={selectedClient}
                isSearchMode={isSearchMode}
                setIsSearchMode={setIsSearchMode}
                setSearchQuery={setSearchQuery}
                user={user}
                lastCheckout={lastCheckout}
              />
            ) : (
              <SelectedClientDisplay
                selectedClient={selectedClient}
                clearSearch={() => {
                  setSelectedClient(null);
                  clearSearch();
                }}
                lastCheckout={lastCheckout}
                availableProducts={availableProducts}
              />
            )}
          </div>

          <ProductSection
            formData={formData}
            handleProductChange={handleProductChange}
            availableProducts={availableProducts}
            disabled={!selectedClient}
          />

          <QuantityPointsSection
            formData={formData}
            handleQuantityChange={handleQuantityChange}
            maxQuantity={maxQuantity}
            disabled={!selectedClient}
          />

          <TotalPointsSection totalPoints={formData.totalPoints} />

          <ReasonSection
            reason={formData.reason}
            onChange={(e) =>
              setFormData((prev) => ({ ...prev, reason: e.target.value }))
            }
            disabled={!selectedClient}
          />

          {selectedClient && availableProducts.length === 0 && (
            <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-md">
              <p className="text-sm text-yellow-800">
                No orders found for today. This member has not made any
                purchases today.
              </p>
            </div>
          )}

          <FormFooter
            onClose={onClose}
            isLoading={isLoading}
            hasSelectedClient={!!selectedClient}
            hasAvailableProducts={availableProducts.length > 0}
          />
        </form>
      </DialogContent>
    </Dialog>
  );
}

const MemberSearchInput = ({
  searchQuery,
  handleSearch,
  isSearching,
  searchResults,
  selectClient,
  selectedClient,
  isSearchMode,
  setIsSearchMode,
  setSearchQuery,
  user,
  lastCheckout,
}) => (
  <div className="relative">
    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
    <Input
      id="client"
      placeholder="Search for a member..."
      value={searchQuery.query}
      onChange={(e) => handleSearch(e.target.value)}
      className="pl-10 py-1.5 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:outline-none"
    />

    {searchQuery.query && (
      <div className="absolute z-20 w-full mt-1 bg-white border border-gray-200 rounded-md shadow-md max-h-40 overflow-y-auto">
        {isSearching ? (
          <div className="p-2 text-gray-500 text-center">Searching...</div>
        ) : searchResults.length === 0 ? (
          <div className="p-2 text-gray-500 text-center">No results found</div>
        ) : (
          searchResults.map((client) => (
            <div
              key={client.id}
              className="p-2 hover:bg-gray-100 cursor-pointer border-b border-gray-200 last:border-b-0"
              onClick={() => {
                selectClient(client);
                setIsSearchMode(false);
              }}
            >
              <div className="font-medium text-sm text-gray-800">
                {client.first_name} {client.last_name}
              </div>
              <div className="text-xs text-gray-500">
                Client #{client.id} - {client.address}
              </div>
              <div className="text-xs text-gray-600">
                Last Checkout:{" "}
                {lastCheckout?.checkout_date
                  ? formatDate(lastCheckout.checkout_date)
                  : "N/A"}
              </div>
            </div>
          ))
        )}
      </div>
    )}

    {selectedClient && isSearchMode && (
      <button
        type="button"
        className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
        onClick={() => {
          setIsSearchMode(false);
          setSearchQuery({
            query: "",
            role: user?.user_metadata?.role,
          });
        }}
      >
        Cancel
      </button>
    )}
  </div>
);

const SelectedClientDisplay = ({
  selectedClient,
  clearSearch,
  lastCheckout,
  availableProducts,
}) => (
  <div className="p-2 bg-blue-50 border border-blue-200 rounded-md shadow-sm">
    <div className="flex justify-between items-center">
      <div>
        <h3 className="text-sm font-medium text-gray-900">
          {selectedClient.first_name} {selectedClient.last_name}
        </h3>
        <p className="text-xs text-gray-500">
          Client #{selectedClient.id} - {selectedClient.address}
        </p>
      </div>
      <button
        type="button"
        onClick={clearSearch}
        className="text-sm text-gray-500 hover:text-red-600 transition"
      >
        <X className="h-4 w-4" />
      </button>
    </div>
    <p className="text-xs text-gray-500 mt-1">
      Last Checkout:{" "}
      {lastCheckout?.checkout_date
        ? formatDate(lastCheckout.checkout_date)
        : "N/A"}
    </p>
    {availableProducts.length > 0 && (
      <p className="text-xs text-green-600 mt-1">
        Found {availableProducts.length} product(s) from today's orders
      </p>
    )}
  </div>
);

const ProductSection = ({
  formData,
  handleProductChange,
  availableProducts,
  disabled,
}) => (
  <div className="space-y-1">
    <Label htmlFor="product" className="text-sm font-medium text-gray-700">
      Product (Today's Orders)
    </Label>
    <Select
      value={formData.productId}
      onValueChange={handleProductChange}
      disabled={disabled}
    >
      <SelectTrigger
        className={`w-full py-1.5 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 ${
          disabled ? "bg-gray-100 cursor-not-allowed" : ""
        }`}
      >
        <SelectValue>
          {availableProducts.find((p) => p.product_id === formData.productId)
            ?.product_name || "Select a product"}
        </SelectValue>
      </SelectTrigger>
      <SelectContent>
        {availableProducts.map((product) => (
          <SelectItem key={product.product_id} value={product.product_id}>
            {product.product_name} (Qty: {product.quantity})
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  </div>
);

const QuantityPointsSection = ({
  formData,
  handleQuantityChange,
  maxQuantity,
  disabled,
}) => {
  return (
    <div className="grid grid-cols-2 gap-3">
      <div className="space-y-1">
        <Label htmlFor="quantity" className="text-sm font-medium text-gray-700">
          Quantity (Max: {maxQuantity})
        </Label>
        <Input
          id="quantity"
          type="number"
          min="1"
          max={maxQuantity}
          value={formData.quantity}
          onChange={(e) => handleQuantityChange(parseInt(e.target.value) || 1)}
          disabled={disabled}
          className={`py-1.5 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 ${
            disabled ? "bg-gray-100 cursor-not-allowed" : ""
          }`}
        />
      </div>

      <div className="space-y-1">
        <Label htmlFor="points" className="text-sm font-medium text-gray-700">
          Points per Unit
        </Label>
        <Input
          id="points"
          type="number"
          value={formData.unitPoints || 0}
          readOnly
          className="py-1.5 border border-gray-300 rounded-md bg-gray-100"
        />
      </div>
    </div>
  );
};

const TotalPointsSection = ({ totalPoints }) => {
  return (
    <div className="space-y-1">
      <Label
        htmlFor="totalPoints"
        className="text-sm font-medium text-gray-700"
      >
        Total Points
      </Label>
      <Input
        id="totalPoints"
        type="number"
        value={totalPoints || 0}
        readOnly
        className="py-1.5 border border-gray-300 rounded-md bg-gray-100"
      />
    </div>
  );
};

const ReasonSection = ({ reason, onChange, disabled }) => (
  <div className="space-y-1">
    <Label htmlFor="reason" className="text-sm font-medium text-gray-700">
      Reason for Return
    </Label>
    <Input
      id="reason"
      value={reason}
      onChange={onChange}
      disabled={disabled}
      placeholder="Enter reason for return"
      className={`py-1.5 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 ${
        disabled ? "bg-gray-100 cursor-not-allowed" : ""
      }`}
    />
  </div>
);

const FormFooter = ({
  onClose,
  isLoading,
  hasSelectedClient,
  hasAvailableProducts,
}) => (
  <DialogFooter className="flex justify-end space-x-2 pt-2">
    <CustomButton
      type="button"
      variant="outline"
      onClick={onClose}
      className="px-3 py-1.5 text-sm border rounded-md text-gray-700 hover:bg-gray-100"
    >
      Cancel
    </CustomButton>
    <CustomButton
      type="submit"
      disabled={isLoading || !hasSelectedClient || !hasAvailableProducts}
      className={`px-3 py-1.5 text-sm rounded-md ${
        isLoading || !hasSelectedClient || !hasAvailableProducts
          ? "bg-gray-300 cursor-not-allowed"
          : "bg-blue-600 text-white hover:bg-blue-700 transition"
      }`}
    >
      {isLoading ? "Submitting..." : "Submit Return"}
    </CustomButton>
  </DialogFooter>
);
