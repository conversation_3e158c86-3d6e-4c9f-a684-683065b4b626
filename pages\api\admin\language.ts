import { NextApiRequest, NextApiResponse } from "next";
import { supabaseClient } from "utils/supabase/client";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  switch (req.method) {
    case "GET":
      try {
        const { data: languageData, error: languageError } =
          await supabaseClient.from("languages").select("*");

        if (languageError) {
          return res.status(500).json({ message: "Error fetching languages" });
        }

        return res.status(200).json(languageData);
      } catch (error) {
        return res.status(500).json({ message: "Error fetching languages" });
      }

    case "POST":
      try {
        const { name } = req.body;

        const { data: languageData, error: languageError } =
          await supabaseClient.from("languages").insert({ name }).select();

        if (languageError) {
          return res.status(500).json({ message: "Error adding language" });
        }

        return res.status(200).json(languageData);
      } catch (error) {
        return res.status(500).json({ message: "Error adding language" });
      }

    case "PUT":
      try {
        const { id, name } = req.body;

        const { data: languageData, error: languageError } =
          await supabaseClient
            .from("languages")
            .update({ name })
            .eq("id", id)
            .select();

        if (languageError) {
          return res.status(500).json({ message: "Error updating language" });
        }

        return res.status(200).json(languageData);
      } catch (error) {
        return res.status(500).json({ message: "Error updating language" });
      }

    case "DELETE":
      try {
        const { id } = req.body;

        const { error: languageError } = await supabaseClient
          .from("languages")
          .delete()
          .eq("id", id);

        if (languageError) {
          return res.status(500).json({ message: "Error deleting language" });
        }

        return res
          .status(200)
          .json({ message: "Language deleted successfully" });
      } catch (error) {
        return res.status(500).json({ message: "Error deleting language" });
      }

    default:
      return res.status(405).json({ message: "Method not allowed" });
  }
}
