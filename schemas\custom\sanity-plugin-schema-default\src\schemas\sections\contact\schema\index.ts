import {
  socialLinks,
  customText,
  webriqForms,
  blockOfText,
  title,
  blockContentNormalStyle,
} from "../../../common/fields";
import { hideIfVariantIn } from "@webriq-pagebuilder/sanity-plugin-schema-default";

export const contactSchema = [
  title(),
  customText(
    "contactDescription",
    "Description",
    "Add a description below the title by typing in the text field below",
    "Got any question? Let's talk about it.",
    1,
    hideIfVariantIn(["variant_c"])
  ),
  // customText(
  //   "officeInformation",
  //   "Office",
  //   "Add office information by typing in the text field below",
  //   "359 Hidden Valley Road, NY",
  //   1
  // ),

  blockContentNormalStyle(
    "firstColumn",
    "Content",
    "Add text content in a single column.",
    hideIfVariantIn(["variant_a", "variant_b"])
  ),
  customText(
    "contactEmail",
    "Email",
    "Add email information by typing in the text field below",
    "<EMAIL>",
    1,
    hideIfVariantIn(["variant_c"])
  ),
  customText(
    "contactNumber",
    "Contact Number",
    "Add contact number by typing in the text field below",
    "+48 698 033 101",
    1
  ),
  socialLinks(hideIfVariantIn(["variant_c"])),
  webriqForms(hideIfVariantIn(["variant_b", "variant_c"])),
  blockOfText(hideIfVariantIn(["variant_b", "variant_c"])),

  blockContentNormalStyle(
    "blockText",
    "Content",
    "Add text content in a single column.",
    hideIfVariantIn(["variant_a", "variant_b"])
  ),
];
