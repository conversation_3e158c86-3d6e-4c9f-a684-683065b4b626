import { useState } from "react";
import { Pencil, Trash2, Plus, Search } from "lucide-react";
import { CustomButton } from "@/components/ui/custom-button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import TableFilters from "../table-filters";
import { useFilters } from "hooks/useFilters";
import { useCategoryData } from "context/useCategoryData";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { useAuthCheck } from "hooks/useAuthCheck";
import { useAlert } from "context/useAlert";
import { useProductData } from "context/useProductCategoryData";
import ConfirmationModal from "../confirmation-modal";
import { DataTable } from "@/components/data-table";
import Image from "next/image";
import { RequiredLabel } from "@/components/ui/required-label";

export default function ProductCategoryContent() {
  const { categoryData } = useCategoryData();
  const { productData, refreshProduct, fetchingProduct } = useProductData();
  const { user, token } = useAuthCheck();
  const { showAlert } = useAlert();
  const [showForm, setShowForm] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const { selectedFilters, handleFilterChange } = useFilters({
    category: [],
    status: [],
  });
  const [editingProduct, setEditingProduct] = useState<any>(null);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [productToDelete, setProductToDelete] = useState<any>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const productFilters = {
    category: {
      label: "Category",
      options: categoryData?.map((category) => ({
        id: category.id,
        label: category.name,
        value: category.name,
      })),
    },
    status: {
      label: "Status",
      options: [
        { id: "1", label: "Active", value: "active" },
        { id: "2", label: "Inactive", value: "inactive" },
      ],
    },
  };

  const filterProducts = (products: typeof productData) => {
    return products.filter((product) => {
      const matchesSearch =
        product.product_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        product.product_code.toLowerCase().includes(searchTerm.toLowerCase()) ||
        product.category.toLowerCase().includes(searchTerm.toLowerCase());

      const matchesCategory =
        selectedFilters.category.length === 0 ||
        selectedFilters.category.includes(product.category);

      const matchesStatus =
        selectedFilters.status.length === 0 ||
        (selectedFilters.status.includes("active") &&
          product.is_active === true) ||
        (selectedFilters.status.includes("inactive") &&
          product.is_active === false);

      return matchesSearch && matchesCategory && matchesStatus;
    });
  };

  const handleCloseForm = () => {
    setShowForm(false);
    setEditingProduct(null);
  };

  const handleEdit = (product: any) => {
    setEditingProduct({
      ...product,
      icon: product.icon ? JSON.parse(product.icon) : null,
    });
    setShowForm(true);
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setIsSubmitting(true);

    const formData = new FormData(e.currentTarget);
    const imageFile = (
      document.getElementById("productImage") as HTMLInputElement
    )?.files?.[0];

    try {
      let imageUrl = editingProduct?.image_url;

      if (imageFile) {
        const base64Promise = new Promise((resolve, reject) => {
          const reader = new FileReader();
          reader.onload = () => resolve(reader.result);
          reader.onerror = (error) => reject(error);
          reader.readAsDataURL(imageFile);
        });

        const base64Data = await base64Promise;

        imageUrl = {
          fileName: imageFile.name,
          fileType: imageFile.type,
          base64Data: base64Data,
        };
      }

      const productData = {
        ...Object.fromEntries(formData.entries()),
        image_url: imageUrl,
        role: user?.user_metadata?.role,
        ...(editingProduct && {
          productId: editingProduct.id,
          category: editingProduct.category,
        }),
      };

      const endpoint = editingProduct
        ? "/api/admin/products/update-product"
        : "/api/admin/products/create-product";

      const response = await fetch(endpoint, {
        method: editingProduct ? "PUT" : "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify(productData),
      });

      const responseData = await response.json();
      if (!response.ok) {
        throw new Error(responseData?.message || "An error occurred");
      }

      refreshProduct();
      showAlert(responseData?.message, "success");
      handleCloseForm();
    } catch (error: any) {
      console.error(error);
      showAlert(error.message, "error");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDeleteClick = (product: any) => {
    setProductToDelete(product);
    setShowDeleteModal(true);
  };

  const handleDeleteConfirm = async () => {
    try {
      const response = await fetch("/api/admin/products/delete-product", {
        method: "DELETE",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({
          productId: productToDelete.id,
          role: user?.user_metadata?.role,
        }),
      });

      const responseData = await response.json();
      if (!response.ok) {
        showAlert(responseData?.message, "error");
        throw new Error(responseData?.message || "An error occurred");
      }

      refreshProduct();
      showAlert(responseData?.message, "success");
    } catch (error) {
      console.log(error);
      showAlert(error, "error");
    } finally {
      setShowDeleteModal(false);
      setProductToDelete(null);
      setShowForm(false);
    }
  };

  const handleDeleteCancel = () => {
    setShowDeleteModal(false);
    setProductToDelete(null);
  };

  const headers = [
    "Product Name",
    // "Code",
    "Category",
    "Points",
    // "Price",
    "Order Max Qty",
    "Monthly Max Qty",
    "Status",
    "Actions",
  ];

  const renderRow = (product: any) => (
    <tr key={product.id} className="border-b">
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
        {product.product_name}
      </td>
      {/* <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{product.product_code}</td> */}
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
          {product.category}
        </span>
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
        {product.unit_points}
      </td>
      {/* <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${product.unit_price}</td> */}
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
        {product.order_max_quantity}
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
        {product.monthly_max_quantity}
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
        <span
          className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
            product.is_active
              ? "bg-green-100 text-green-800"
              : "bg-gray-100 text-gray-800"
          }`}
        >
          {product.is_active ? "Active" : "Inactive"}
        </span>
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
        <div className="flex space-x-2 justify-center">
          <CustomButton
            variant="ghost"
            size="icon"
            onClick={() => handleEdit(product)}
            disabled={isSubmitting}
          >
            <Pencil className="h-4 w-4" />
          </CustomButton>
          <CustomButton
            variant="ghost"
            size="icon"
            className="text-destructive"
            onClick={() => handleDeleteClick(product)}
            disabled={isSubmitting}
          >
            <Trash2 className="h-4 w-4" />
          </CustomButton>
        </div>
      </td>
    </tr>
  );

  return (
    <div className="p-6 space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold">Product Categories</h2>
        <CustomButton
          variant="outline"
          onClick={() => {
            setShowForm(!showForm);
            setEditingProduct(null);
          }}
          disabled={isSubmitting}
        >
          <Plus className="h-4 w-4 mr-2" />
          Add New Product
        </CustomButton>
      </div>

      <div className="flex gap-4 items-center">
        <div className="relative flex-1">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
          <Input
            placeholder="Search products..."
            className="pl-8"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        <TableFilters
          filters={productFilters}
          selectedFilters={selectedFilters}
          onFilterChange={handleFilterChange}
        />
      </div>

      {showForm && (
        <div className="mb-8 p-6 border rounded-lg bg-gray-50">
          <h3 className="text-lg font-medium mb-4">
            {editingProduct ? "Edit Product" : "Create New Product"}
          </h3>
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <RequiredLabel htmlFor="productName">
                  Product Name
                </RequiredLabel>
                <Input
                  id="productName"
                  name="productName"
                  required
                  defaultValue={editingProduct?.product_name}
                />
              </div>
              <div className="space-y-2">
                <RequiredLabel htmlFor="productCode">
                  Product Code
                </RequiredLabel>
                <Input
                  id="productCode"
                  name="productCode"
                  required
                  defaultValue={editingProduct?.product_code}
                />
              </div>
            </div>

            <div className="space-y-2">
              <RequiredLabel htmlFor="category">Category</RequiredLabel>
              <Select
                name="category"
                required
                defaultValue={
                  editingProduct?.category?.toLowerCase() ||
                  categoryData?.[0]?.name.toLowerCase()
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select category data" />
                </SelectTrigger>
                <SelectContent>
                  {categoryData?.map((category) => (
                    <SelectItem value={category?.name?.toLowerCase()}>
                      {category?.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="description" className="text-sm font-medium">
                Description
              </Label>
              <Textarea
                id="description"
                name="description"
                defaultValue={editingProduct?.description}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="isActive" className="text-sm font-medium">
                Is Active?
              </Label>
              <RadioGroup
                name="isActive"
                defaultValue={editingProduct?.is_active ? "yes" : "no"}
                required
              >
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="yes" id="yes" />
                  <Label htmlFor="yes">Yes</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="no" id="no" />
                  <Label htmlFor="no">No</Label>
                </div>
              </RadioGroup>
            </div>

            <div className="grid grid-cols-3 gap-4">
              <div className="space-y-2">
                <RequiredLabel htmlFor="unitPoints">Unit Points</RequiredLabel>
                <Input
                  type="number"
                  id="unitPoints"
                  name="unitPoints"
                  min={1}
                  step=".1"
                  required
                  defaultValue={editingProduct?.unit_points}
                />
              </div>
              <div className="space-y-2">
                <RequiredLabel htmlFor="unitPrice">Retail Value</RequiredLabel>
                <div className="relative">
                  <span className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground">
                    $
                  </span>
                  <Input
                    type="number"
                    id="unitPrice"
                    name="unitPrice"
                    required
                    step="0.01"
                    defaultValue={editingProduct?.unit_price ?? ""}
                    placeholder="0.00"
                    className="pl-6"
                    onChange={(e) => {
                      let value = e.target.value;

                      // Allow only valid decimal numbers
                      value = value.replace(/[^0-9.]/g, "");

                      // Prevent multiple decimals
                      const parts = value.split(".");
                      if (parts.length > 2) {
                        value = parts[0] + "." + parts.slice(1).join("");
                      }

                      e.target.value = value;
                    }}
                    onBlur={(e) => {
                      const value = parseFloat(e.target.value);
                      e.target.value = !isNaN(value)
                        ? value.toFixed(2)
                        : "0.00";
                    }}
                  />
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="unitWeight" className="text-sm font-medium">
                  Unit Weight/lbs
                </Label>
                <Input
                  id="unitWeight"
                  name="unitWeight"
                  defaultValue={editingProduct?.unit_weight}
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <RequiredLabel htmlFor="orderMaxQty">
                  Order Max Qty
                </RequiredLabel>
                <Input
                  type="number"
                  id="orderMaxQty"
                  name="orderMaxQty"
                  min={0}
                  required
                  defaultValue={editingProduct?.order_max_quantity}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="monthlyMaxQty" className="text-sm font-medium">
                  Monthly Max Qty
                </Label>
                <Input
                  type="number"
                  id="monthlyMaxQty"
                  name="monthlyMaxQty"
                  min={0}
                  defaultValue={editingProduct?.monthly_max_quantity}
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="productImage" className="text-sm font-medium">
                Product Image
              </Label>
              <div className="flex flex-col space-y-2">
                {/* Current image preview if editing */}
                {editingProduct?.icon && (
                  <div className="w-32 h-32 border rounded-lg overflow-hidden">
                    <Image
                      src={editingProduct.icon?.base64Data}
                      alt={editingProduct.icon?.fileName}
                      className="w-full h-full object-cover"
                      width={500}
                      height={500}
                    />
                  </div>
                )}

                {/* File input and filename display */}
                <div className="flex items-center space-x-2">
                  <CustomButton
                    type="button"
                    variant="outline"
                    onClick={() =>
                      document.getElementById("productImage")?.click()
                    }
                    className="w-32"
                  >
                    Choose File
                  </CustomButton>
                  <input
                    id="productImage"
                    name="productImage"
                    type="file"
                    accept="image/*"
                    className="hidden"
                    onChange={(e) => {
                      const fileName = e.target.files?.[0]?.name;
                      if (fileName) {
                        const fileDisplay =
                          document.getElementById("fileDisplay");
                        if (fileDisplay) {
                          fileDisplay.textContent = fileName;
                        }
                      }
                    }}
                  />
                  <span id="fileDisplay" className="text-sm text-gray-600">
                    {editingProduct?.icon
                      ? editingProduct.icon?.fileName
                      : "No file chosen"}
                  </span>
                </div>
                <p className="text-xs text-gray-500">
                  Supported formats: PNG, JPG, JPEG
                </p>
              </div>
            </div>

            <div className="flex justify-end space-x-2">
              <CustomButton
                type="button"
                variant="outline"
                onClick={handleCloseForm}
                disabled={isSubmitting}
              >
                Cancel
              </CustomButton>
              <CustomButton type="submit" disabled={isSubmitting}>
                {isSubmitting ? (
                  <div className="flex items-center gap-2">
                    <span className="h-4 w-4 border-2 border-current border-r-transparent rounded-full animate-spin" />
                    {editingProduct ? "Updating..." : "Creating..."}
                  </div>
                ) : editingProduct ? (
                  "Update Product"
                ) : (
                  "Create Product"
                )}
              </CustomButton>
            </div>
          </form>
        </div>
      )}

      <DataTable
        headers={headers}
        data={[...filterProducts(productData)].sort((a, b) =>
          a.product_name.localeCompare(b.product_name)
        )}
        renderRow={renderRow}
        isLoading={fetchingProduct}
      />

      <ConfirmationModal
        isOpen={showDeleteModal}
        message={`Are you sure you want to delete "${productToDelete?.product_name}" product?`}
        onConfirm={handleDeleteConfirm}
        onCancel={handleDeleteCancel}
      />
    </div>
  );
}
