import { rootSchema } from "@webriq-pagebuilder/sanity-plugin-schema-default";
import { MdCloudCircle } from "react-icons/md";

// Images
import variantAImage from "./images/variant_a.jpg";
import variantBImage from "./images/variant_b.jpg";
import variantCImage from "./images/variant_c.jpg";
import variantDImage from "./images/variant_d.jpg";
import variantEImage from "./images/variant_e.png";
import variantFImage from "./images/variant_f.png";
import variantGImage from "./images/variant_g.png";

import { logoCloudSchema } from "./schema";

export const variantsList = [
  {
    title: "Variant A",
    description: "Logo cloud with heading",
    value: "variant_a",
    image: variantAImage.src,
  },
  {
    title: "Variant B",
    description: "Logo cloud with side heading and description",
    value: "variant_b",
    image: variantBImage.src,
  },
  {
    title: "Variant C",
    description: "Logo cloud with heading and primary button",
    value: "variant_c",
    image: variantCImage.src,
  },
  {
    title: "Variant D",
    description: "Logo cloud with logo images only",
    value: "variant_d",
    image: variantDImage.src,
  },
  {
    title: "Variant E",
    description: "Logo cloud with logo images only",
    value: "variant_e",
    image: variantEImage.src,
  },
  {
    title: "Variant F",
    description: "Logo cloud with logo images only",
    value: "variant_f",
    image: variantFImage.src,
  },
  {
    title: "Variant G",
    description: "Logo cloud with logo images only",
    value: "variant_g",
    image: variantGImage.src,
  },
];

export default rootSchema(
  "logoCloud",
  "Logo Cloud",
  MdCloudCircle,
  variantsList,
  logoCloudSchema
);
