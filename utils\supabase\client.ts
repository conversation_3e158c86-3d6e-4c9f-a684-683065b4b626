import { createClientComponentClient } from "@supabase/auth-helpers-nextjs";
import { createClient } from "@supabase/supabase-js";

const url = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const anonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;
const serviceRoleKey = process.env.NEXT_PUBLIC_SUPABASE_SERVICE_ROLE!;

export const supabaseClient = createClientComponentClient({
  supabaseUrl: url,
  supabaseKey: anonKey,
});

// For admin operations that bypass RLS (use carefully, server-side only)
export const supabaseClientAdmin = createClient(url, serviceRoleKey);

/** For server-side operations that require a JWT (for authenticated users)
*/
export function createSupabaseClient(jwt: string) {
  return createClient(url, anonKey, {
    global: {
      headers: {
        Authorization: `Bearer ${jwt}`,
      },
    },
  });
}
