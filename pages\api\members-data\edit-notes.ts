import { NextApiRequest, NextApiResponse } from "next";
import { supabaseClient } from "utils/supabase/client";

export default async function PUT(req: NextApiRequest, res: NextApiResponse) {
  try {
    const { subject, description, id, modified_by, noteType } = req.body;

    const { data: userData, error: userError } = await supabaseClient
      .from("users")
      .select("name")
      .eq("user_auth_id", modified_by)
      .single();

    if (userError) {
      console.error("Error fetching user data:", userError.message);
      return res.status(500).json({
        message: "Error fetching user data to the database",
        error: userError.message,
      });
    }

    const { error: notesError } = await supabaseClient
      .from("notes")
      .update({
        subject: subject,
        description: description,
        modified_by: userData?.[0]?.name,
        type: noteType,
      })
      .eq("id", id);

    if (notesError) {
      console.error("Error updating notes data:", notesError.message);
      return res.status(500).json({
        message: "Error updating notes data to the database",
        error: notesError.message,
      });
    }

    return res.status(200).json({
      message: "Update notes data successfully!",
    });
  } catch (error) {
    console.error(error);
    return res.status(500).json({
      message: "Failed to process the request.",
    });
  }
}
