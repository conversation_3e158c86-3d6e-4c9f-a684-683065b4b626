import React, { useState, useEffect } from "react";
import { CustomButton } from "@/components/ui/custom-button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useAlert } from "context/useAlert";
import { Eye, EyeOff, Info, Plus } from "lucide-react";
import { Tooltip } from "react-tooltip";
import { RequiredLabel } from "@/components/ui/required-label";
import { useMemberData } from "context/useMemberData";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import {
  calculateMembershipPrice,
  calculatePoints,
} from "utils/membership-calculator";
import { QualificationModal } from "@/components/ui/qualification-modal";
import { Skeleton } from "@/components/ui/skeleton";
import { useAuthCheck } from "hooks/useAuthCheck";
import { housingTypes, languages, phoneTypes } from "utils";
import { useLocation } from "hooks/useLocation";
import HouseholdMembers from "../staff/household-members";
import { useHouseholdData } from "context/useHousehold";
import { useLanguageData } from "context/useLanguageData";
import axios from "axios";
import PersonalInformation from "../staff/personal-information";

export default function AddUserForm({
  setShowUserForm,
  role = "staff",
  selectedUser,
  setSelectedUser,
}: {
  setShowUserForm: (show: boolean) => void;
  role?: string;
  selectedUser?: any;
  setSelectedUser: (user: any) => void;
}) {
  const { city, region, country, error } = useLocation();
  const { showAlert } = useAlert();
  const { refreshData, fetchingMember } = useMemberData();
  const { user, token } = useAuthCheck();
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isPending, setIsPending] = useState(false);
  const [passwordError, setPasswordError] = useState<string>("");
  const [selectedRole, setSelectedRole] = useState<string>(role || "staff");
  const [totalPoints, setTotalPoints] = useState<number>(
    selectedUser?.total_points || 0
  );
  const [amountPaid, setAmountPaid] = useState<number | null>(
    selectedUser?.amount_paid || null
  );
  const [showQualificationModal, setShowQualificationModal] = useState(false);
  const [contactInfo, setContactInfo] = useState({
    primary_number: selectedUser?.contact_info?.primary_number || "",
    primary_phone_type:
      selectedUser?.contact_info?.primary_phone_type || "Mobile",
    secondary_number: selectedUser?.contact_info?.secondary_number || null,
    secondary_phone_type:
      selectedUser?.contact_info?.secondary_phone_type || null,
    alternate_contact_name:
      selectedUser?.contact_info?.alternate_contact_name || null,
  });
  const { totalMemberIncome, getAgeGroups, memberCount } = useHouseholdData();
  const [isHeadOfHousehold, setIsHeadOfHousehold] = useState(
    selectedUser?.is_head_of_household || false
  );

  const { languageData } = useLanguageData();
  const [showCustomInput, setShowCustomInput] = useState(false);
  const [customLanguage, setCustomLanguage] = useState("");
  const [selectedLanguage, setSelectedLanguage] = useState(
    selectedUser?.additional_info?.preferred_language ||
      languages[0].toLowerCase()
  );
  const [submittingLanguage, setSubmittingLanguage] = useState(false);

  // Update total points whenever household size changes
  useEffect(() => {
    if (memberCount) {
      const calculatePointsAsync = async () => {
        const points = await calculatePoints(memberCount || 1);
        setTotalPoints(points);
      };
      calculatePointsAsync();
    }
  }, [memberCount]);

  // Add effect to update amount paid when household size or annual income changes
  useEffect(() => {
    if (memberCount && totalMemberIncome) {
      const calculatePriceAsync = async () => {
        const prices = await calculateMembershipPrice(
          memberCount,
          totalMemberIncome
        );
        setAmountPaid(prices[0] || null);
      };
      calculatePriceAsync();
      const prices = calculateMembershipPrice(memberCount, totalMemberIncome);
      setAmountPaid(prices[0] || null);
    } else {
      setAmountPaid(null);
    }
  }, [memberCount, totalMemberIncome]);

  const handleLanguageChange = (value) => {
    setSelectedLanguage(value);
  };

  const handleUserSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setIsPending(true);

    if (amountPaid === null && role !== "staff" && role !== "admin") {
      setShowQualificationModal(true);
      setIsPending(false);
      return;
    }

    const endpoint =
      selectedUser && role === "staff"
        ? "/api/user-data/update-staff"
        : selectedUser && role === "member"
        ? "/api/user-data/update-member"
        : "/api/user-data";

    const formData = new FormData(e.currentTarget);
    const data = Object.fromEntries(formData.entries());
    const authorizedShoppers = Object.keys(data)
      .filter((key) => key.startsWith("authorizedShopper-"))
      .map((key) => data[key]);

    // Extract household members data
    const householdMembers: any[] = Object.keys(data)
      .filter((key) => key.startsWith("member"))
      .reduce((acc, key) => {
        const match = key.match(/-(\d+)$/);
        if (match) {
          const index = match[1];
          acc[index] = acc[index] || {};
          acc[index][key.replace(`-${index}`, "")] = data[key];
        }
        return acc;
      }, []);

    if (data.isHeadOfHousehold === "yes") {
      householdMembers[0] = householdMembers[0] || {};
      householdMembers[0].memberName =
        `${data.firstName} ${data.lastName}`.trim();
    }

    if (data.password !== data.confirmPassword) {
      setPasswordError("Passwords do not match");
      setIsPending(false);
      return;
    }

    const personalInfo = {
      hispanic: data.hispanic,
      race: data.race,
      gender: data.gender,
      disabled: data.disabled,
      veteran: data.veteran,
    };
    const ageGroups = getAgeGroups();

    try {
      const response = await fetch(endpoint, {
        method: selectedUser ? "PUT" : "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({
          ...data,
          role: role === "staff" ? selectedRole : role,
          totalPoints,
          amountPaid: amountPaid,
          authorizedShoppers: authorizedShoppers.join(","),
          contactInfo: JSON.stringify(contactInfo),
          householdMembers: JSON.stringify(householdMembers),
          personalInfo: JSON.stringify(personalInfo),
          ageGroups: JSON.stringify(ageGroups),
          annualIncome: totalMemberIncome,
          householdSize: memberCount,
          addedBy: user?.id,
          added_store_location: !error
            ? `${city}, ${region}, ${country}`
            : error,
          ...(selectedUser && {
            userId: selectedUser?.user_id,
            memberId: selectedUser?.id,
            currentEmail: selectedUser?.email,
            currentUsername: selectedUser?.username,
            userAuthId: selectedUser?.user_auth_id,
            membershipId: selectedUser?.membership_id,
            paymentOption: selectedUser?.payment_method,
            paymentId: selectedUser?.payment_id,
            currentAmountPaid: selectedUser?.amount_paid,
            currentTotalPoints: selectedUser?.total_points,
            amountPaid: amountPaid,
            currentRole: selectedUser?.role,
            role: role === "staff" ? selectedRole : role,
            updated_store_location: !error
              ? `${city}, ${region}, ${country}`
              : error,
          }),
        }),
      });
      const responseData = await response.json();
      if (!response.ok) {
        throw new Error(
          responseData.message ||
            (selectedUser
              ? "Failed to update user account"
              : "Failed to create user account")
        );
      }
      showAlert(
        selectedUser
          ? "User account updated successfully!"
          : "User account created successfully!",
        "success"
      );
      refreshData();
      setShowUserForm(false);
      setSelectedUser(null);
    } catch (error) {
      showAlert(
        error instanceof Error ? error.message : "An error occurred",
        "error"
      );
    } finally {
      setIsPending(false);
    }
  };

  const handleChange = (e) => {
    setContactInfo({ ...contactInfo, [e.target.name]: e.target.value });
  };

  const isStaffAndAdmin = role === "staff" || role === "admin";
  const LabelComponent = isStaffAndAdmin ? Label : RequiredLabel;
  const availableRoles =
    user?.user_metadata?.role === "admin"
      ? ["staff", "admin", "volunteer"]
      : ["volunteer"];

  return (
    <>
      <div className="mb-8 p-6 border rounded-lg bg-gray-50">
        <h3 className="text-lg font-medium mb-4">
          {selectedUser ? "Update Account" : "Create Account"}
        </h3>
        <form onSubmit={handleUserSubmit} className="space-y-4">
          {role === "staff" && (
            <div className="space-y-2">
              <RequiredLabel htmlFor="role">Role</RequiredLabel>
              <Select
                name="role"
                required
                defaultValue={selectedRole}
                onValueChange={(value) => setSelectedRole(value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select a role" />
                </SelectTrigger>
                <SelectContent>
                  {availableRoles.map((role) => (
                    <SelectItem key={role} value={role}>
                      {role.charAt(0).toUpperCase() + role.slice(1)}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          )}
          <div className="grid grid-cols-3 gap-4">
            <div className="space-y-2">
              <RequiredLabel htmlFor="firstName">First Name</RequiredLabel>
              <Input
                id="firstName"
                name="firstName"
                defaultValue={selectedUser?.first_name}
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="middleInitial">Middle Initial</Label>
              <Input
                id="middleInitial"
                name="middleInitial"
                maxLength={1}
                defaultValue={selectedUser?.middle_initial}
              />
            </div>
            <div className="space-y-2">
              <RequiredLabel htmlFor="lastName">Last Name</RequiredLabel>
              <Input
                id="lastName"
                name="lastName"
                defaultValue={selectedUser?.last_name}
                required
              />
            </div>
          </div>

          {/* Username and Email */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <RequiredLabel htmlFor="username">Username</RequiredLabel>
              <Input
                id="username"
                name="username"
                defaultValue={selectedUser?.username}
                required
              />
            </div>
            <div className="space-y-2">
              <div className="flex items-center space-x-1">
                <div data-tooltip-id="email-tooltip">
                  <Info className="w-4 h-4 cursor-pointer" />
                </div>
                <RequiredLabel htmlFor="email">Email</RequiredLabel>
              </div>

              <Input
                id="email"
                name="email"
                type="email"
                placeholder="<EMAIL>"
                defaultValue={selectedUser?.email}
                required
              />

              <Tooltip id="email-tooltip" place="right">
                You may add a dummy email if user does not have one.
              </Tooltip>
            </div>
          </div>

          {/* Password Fields */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              {selectedUser ? (
                <Label htmlFor="newPassword">New Password</Label>
              ) : (
                <RequiredLabel htmlFor="password">Password</RequiredLabel>
              )}
              <div className="relative">
                <Input
                  id={selectedUser ? "newPassword" : "password"}
                  name={selectedUser ? "newPassword" : "password"}
                  type={showPassword ? "text" : "password"}
                  required={selectedUser ? false : true}
                  className="pr-10"
                  onChange={(e) => {
                    const confirmPassword =
                      e.currentTarget.form?.querySelector<HTMLInputElement>(
                        `[name="${
                          selectedUser
                            ? "confirmNewPassword"
                            : "confirmPassword"
                        }"]`
                      );
                    if (confirmPassword && confirmPassword.value) {
                      setPasswordError(
                        e.target.value === confirmPassword.value
                          ? ""
                          : "Passwords do not match"
                      );
                    }
                  }}
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute right-3 top-1/2 -translate-y-1/2"
                >
                  {showPassword ? (
                    <EyeOff className="h-4 w-4 text-gray-500" />
                  ) : (
                    <Eye className="h-4 w-4 text-gray-500" />
                  )}
                </button>
              </div>
            </div>
            <div className="space-y-2">
              {selectedUser ? (
                <Label htmlFor="confirmNewPassword">Confirm New Password</Label>
              ) : (
                <RequiredLabel htmlFor="confirmPassword">
                  Confirm Password
                </RequiredLabel>
              )}
              <div className="relative">
                <Input
                  id={selectedUser ? "confirmNewPassword" : "confirmPassword"}
                  name={selectedUser ? "confirmNewPassword" : "confirmPassword"}
                  type={showConfirmPassword ? "text" : "password"}
                  required={selectedUser ? false : true}
                  className="pr-10"
                  onChange={(e) => {
                    const password =
                      e.currentTarget.form?.querySelector<HTMLInputElement>(
                        `[name="${selectedUser ? "newPassword" : "password"}"]`
                      );
                    setPasswordError(
                      e.target.value === password?.value
                        ? ""
                        : "Passwords do not match"
                    );
                  }}
                />
                <button
                  type="button"
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  className="absolute right-3 top-1/2 -translate-y-1/2"
                >
                  {showConfirmPassword ? (
                    <EyeOff className="h-4 w-4 text-gray-500" />
                  ) : (
                    <Eye className="h-4 w-4 text-gray-500" />
                  )}
                </button>
              </div>
              {passwordError && (
                <p className="text-sm text-red-500">{passwordError}</p>
              )}
            </div>
          </div>

          {/* <div className="space-y-2">
            <RequiredLabel htmlFor="contact_info">Phone Number</RequiredLabel>
            <Input
              id="contact_info"
              name="contact_info"
              defaultValue={
                selectedUser?.phone_number || selectedUser?.contact_info || ""
              }
              required
            />
          </div> */}

          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              {/* Primary Number */}
              <div>
                <RequiredLabel htmlFor="primary_number">
                  Primary Number
                </RequiredLabel>
                <Input
                  name="primary_number"
                  value={contactInfo.primary_number}
                  onChange={handleChange}
                  required
                />
              </div>
              <div>
                <RequiredLabel htmlFor="primary_phone_type">
                  Primary Phone Type
                </RequiredLabel>
                <Select
                  name="primary_phone_type"
                  onValueChange={(value) =>
                    setContactInfo({
                      ...contactInfo,
                      primary_phone_type: value,
                    })
                  }
                  defaultValue={
                    contactInfo.primary_phone_type?.toLowerCase() ||
                    phoneTypes[0]?.toLowerCase()
                  }
                  required
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select type" />
                  </SelectTrigger>
                  <SelectContent>
                    {phoneTypes.map((type) => (
                      <SelectItem key={type} value={type.toLowerCase()}>
                        {type}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Secondary Number */}
              <div>
                <Label htmlFor="secondary_number">Secondary Number</Label>
                <Input
                  name="secondary_number"
                  value={contactInfo.secondary_number}
                  onChange={handleChange}
                />
              </div>
              <div>
                <Label htmlFor="secondary_phone_type">
                  Secondary Phone Type
                </Label>
                <Select
                  name="secondary_phone_type"
                  onValueChange={(value) =>
                    setContactInfo({
                      ...contactInfo,
                      secondary_phone_type: value,
                    })
                  }
                  defaultValue={contactInfo.secondary_phone_type}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select type" />
                  </SelectTrigger>
                  <SelectContent>
                    {phoneTypes.map((type) => (
                      <SelectItem key={type} value={type.toLowerCase()}>
                        {type}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Alternate Contact Name */}
            <div>
              <Label htmlFor="alternate_contact_name">
                Alternate Phone Contact Name
              </Label>
              <Input
                id="alternate_contact_name"
                name="alternate_contact_name"
              />
            </div>
          </div>

          {/* Address Fields */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <RequiredLabel htmlFor="address">Address</RequiredLabel>
              <Input
                id="address"
                name="address"
                defaultValue={selectedUser?.address}
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="secondaryAddress" className="text-sm font-medium">
                Secondary Address
              </Label>
              <Input
                id="secondaryAddress"
                name="secondaryAddress"
                defaultValue={selectedUser?.secondaryAddress}
              />
            </div>
          </div>

          <div className="grid grid-cols-3 gap-4">
            <div className="space-y-2">
              <LabelComponent htmlFor="city">City</LabelComponent>
              <Input
                id="city"
                name="city"
                defaultValue={selectedUser?.city}
                required={!isStaffAndAdmin}
              />
            </div>
            <div className="space-y-2">
              <LabelComponent htmlFor="state">State</LabelComponent>
              <Input
                id="state"
                name="state"
                defaultValue={selectedUser?.state}
                required={!isStaffAndAdmin}
              />
            </div>
            <div className="space-y-2">
              <LabelComponent htmlFor="zipCode">ZIP Code</LabelComponent>
              <Input
                id="zipCode"
                name="zipCode"
                defaultValue={selectedUser?.zip_code}
                required={!isStaffAndAdmin}
              />
            </div>
          </div>

          {/* {!["admin", "staff"].includes(role) && (
            <div className="space-y-2">
              <RequiredLabel htmlFor="paymentOption">
                Payment Method
              </RequiredLabel>
              <Select
                name="paymentOption"
                required
                defaultValue={selectedUser?.payment_method ?? "cash"}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select payment method" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="cash">Cash</SelectItem>
                  <SelectItem value="credit">Credit Card</SelectItem>
                  <SelectItem value="check">Check</SelectItem>
                </SelectContent>
              </Select>
            </div>
          )} */}

          {!["admin", "staff"].includes(role) && (
            <React.Fragment>
              <div className="space-y-2">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <RequiredLabel htmlFor="preferredLanguage">
                      Preferred Language
                    </RequiredLabel>
                    <Select
                      value={selectedLanguage}
                      onValueChange={handleLanguageChange}
                      name="preferredLanguage"
                      disabled={isPending}
                      required
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select preferred language" />
                      </SelectTrigger>
                      <SelectContent>
                        {languageData?.map((language) => (
                          <SelectItem
                            key={language.id}
                            defaultValue={language.name.toLowerCase()}
                            value={language.name.toLowerCase()}
                          >
                            {language.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <RequiredLabel htmlFor="housingType">
                      Housing Type
                    </RequiredLabel>
                    {isPending ? (
                      <Skeleton className="w-full h-9" />
                    ) : (
                      <Select
                        defaultValue={
                          selectedUser?.additional_info?.housing_type?.toLowerCase() ||
                          housingTypes[0]?.toLowerCase()
                        }
                        name="housingType"
                        disabled={isPending}
                        required
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select housing type" />
                        </SelectTrigger>
                        <SelectContent>
                          {housingTypes.map((type) => (
                            <SelectItem key={type} value={type.toLowerCase()}>
                              {type}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    )}
                  </div>
                  <div>
                    <Label htmlFor="householdSize">Household Size</Label>
                    <Input
                      id="householdSize"
                      name="householdSize"
                      value={memberCount}
                      className="bg-gray-100"
                      readOnly
                    />
                  </div>
                </div>
              </div>
              <div className="space-y-2">
                <PersonalInformation isDisabled={isPending} />
              </div>
            </React.Fragment>
          )}

          {!["admin", "staff"].includes(role) && (
            <div className="space-y-4">
              <Label>Authorized Shoppers</Label>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {[0, 1, 2].map((index) => (
                  <div key={index} className="space-y-2">
                    <Label htmlFor={`authorizedShopper-${index}`}>
                      Shopper #{index + 1}
                    </Label>
                    {fetchingMember ? (
                      <Skeleton className="w-full h-9" />
                    ) : (
                      <Input
                        id={`authorizedShopper-${index}`}
                        name={`authorizedShopper-${index}`}
                        defaultValue={
                          (selectedUser?.authorized_shopper &&
                            JSON?.parse(selectedUser?.authorized_shopper)?.[
                              index
                            ]) ||
                          ""
                        }
                      />
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}

          {!["admin", "staff"].includes(role) && (
            <div className="space-y-2">
              <Label htmlFor="headOfHousehold">Head of Household</Label>
              <RadioGroup
                name="isHeadOfHousehold"
                defaultValue={selectedUser?.is_head_of_household ? "yes" : "no"}
                onValueChange={(value) => {
                  setIsHeadOfHousehold(value === "yes");
                }}
                required
              >
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="yes" id="yes" />
                  <Label htmlFor="yes">Yes</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="no" id="no" />
                  <Label htmlFor="no">No</Label>
                </div>
              </RadioGroup>
            </div>
          )}

          {!["admin", "staff"].includes(role) && (
            <React.Fragment>
              <HouseholdMembers
                isDisabled={isPending}
                headOfHousehold={isHeadOfHousehold}
                selectedUser={selectedUser}
                isUserForm={true}
              />
            </React.Fragment>
          )}

          {/* {memberCount && totalMemberIncome > 0 && (
            <React.Fragment>
              <div className="space-y-2">
                <Label htmlFor="amountPaid">Amount Paid</Label>
                <div className="relative">
                  <span className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-500">
                    $
                  </span>
                  <Input
                    id="amountPaid"
                    name="amountPaid"
                    type="number"
                    required
                    className="pl-7 bg-gray-100"
                    value={amountPaid || ""}
                    disabled
                  />
                </div>
                {amountPaid && (
                  <p className="text-sm text-gray-500">
                    Based on household size of {memberCount} and annual income
                    of ${totalMemberIncome.toLocaleString()}
                  </p>
                )}
              </div>

              {memberCount && totalMemberIncome && (
                <div className="space-y-2">
                  <Label htmlFor="total_points">Total Points</Label>
                  <Input
                    type="number"
                    id="total_points"
                    name="total_points"
                    required
                    min="1"
                    className="w-full bg-gray-100"
                    disabled
                    defaultValue={selectedUser?.total_points}
                    value={totalPoints}
                  />
                </div>
              )}
            </React.Fragment>
          )} */}

          <div className="flex justify-end space-x-2">
            <CustomButton
              type="button"
              variant="outline"
              onClick={() => {
                setShowUserForm(false);
                setSelectedUser(null);
              }}
            >
              Cancel
            </CustomButton>
            <CustomButton type="submit" disabled={isPending || !!passwordError}>
              {isPending
                ? "Saving..."
                : selectedUser
                ? "Update Account"
                : "Create Account"}
            </CustomButton>
          </div>
        </form>
      </div>

      <QualificationModal
        isOpen={showQualificationModal}
        onClose={() => setShowQualificationModal(false)}
      />
    </>
  );
}
