import React from "react";
import { useMemberData } from "context/useMemberData";
import { useAuthCheck } from "hooks/useAuthCheck";
import ClientSearch from "@/components/client-search";
import { DashboardNav } from "@/components/dashboard/dashboard-nav";

export default function CheckinPage() {
  const { fetchingMember } = useMemberData();
  const { isAuthenticated, user, loading } = useAuthCheck();
  const hasAccess = ["admin", "staff", "volunteer"].includes(
    user?.user_metadata?.role
  );

  if (isAuthenticated === null || fetchingMember) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        Loading...
      </div>
    );
  }

  return (
    <React.Fragment>
      <DashboardNav />
      {hasAccess && (
        <div className="flex items-center justify-center my-5">
          <ClientSearch isStaff={true} isModal={false} />
        </div>
      )}

      {!hasAccess && !loading && (
        <div className="text-center text-red-500 h-screen flex items-center justify-center">
          Access Denied: Your role does not have access to this page.
        </div>
      )}
    </React.Fragment>
  );
}
