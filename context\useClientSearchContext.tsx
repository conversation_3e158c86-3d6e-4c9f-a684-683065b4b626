import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  use<PERSON><PERSON>back,
  ReactNode,
} from "react";
import { useAuthCheck } from "hooks/useAuthCheck";
interface ClientSearchContextType {
  searchResults: any[];
  isSearching: boolean;
  searchQuery: {
    query: string;
    role: string;
  };
  isStaff: boolean;
  setSearchQuery: (query: { query: string; role: string }) => void;
  setIsStaff: (isStaff: boolean) => void;
  fetchSearchResults: () => Promise<any[]>;
}

const ClientSearchContext = createContext<ClientSearchContextType | undefined>(
  undefined
);

export const useClientSearch = () => {
  const context = useContext(ClientSearchContext);
  if (!context) {
    throw new Error(
      "useClientSearch must be used within a ClientSearchProvider"
    );
  }
  return context;
};

interface ClientSearchProviderProps {
  children: ReactNode;
}

export const ClientSearchProvider = ({
  children,
}: ClientSearchProviderProps) => {
  const [searchResults, setSearchResults] = useState<any[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [searchQuery, setSearchQuery] = useState<{
    query: string;
    role: string;
  }>({ query: "", role: "" });
  const [debouncedQuery, setDebouncedQuery] = useState<string>("");
  const [isStaff, setIsStaff] = useState<boolean>(false);
  const { token } = useAuthCheck();

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedQuery(searchQuery.query);
    }, 500);

    return () => clearTimeout(handler);
  }, [searchQuery]);

  const fetchSearchResults = useCallback(async (): Promise<any[]> => {
    if (debouncedQuery?.trim().length < 1) {
      setSearchResults([]);
      return [];
    }

    setIsSearching(true);
    try {
      const response = await fetch(
        `/api/members-data/search?query=${encodeURIComponent(
          debouncedQuery
        )}&role=${encodeURIComponent(searchQuery.role)}`,
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
        }
      );
      if (!response.ok) {
        throw new Error("Search failed");
      }

      const data = await response.json();
      const clientsOnly: any[] = [];
      const staffAndAdmins: any[] = [];

      data.forEach((client: { role: string }) => {
        if (client.role === "member") {
          clientsOnly.push(client);
        } else if (client.role === "staff" || client.role === "admin") {
          staffAndAdmins.push(client);
        }
      });

      const filteredResults = isStaff ? staffAndAdmins : clientsOnly;
      setSearchResults(filteredResults);
      return filteredResults;
    } catch (error) {
      console.error("Search error:", error);
      setSearchResults([]);
      return [];
    } finally {
      setIsSearching(false);
    }
  }, [debouncedQuery, isStaff]);

  useEffect(() => {
    fetchSearchResults();
  }, [fetchSearchResults]);

  return (
    <ClientSearchContext.Provider
      value={{
        searchResults,
        isSearching,
        searchQuery,
        isStaff,
        setSearchQuery,
        setIsStaff,
        fetchSearchResults,
      }}
    >
      {children}
    </ClientSearchContext.Provider>
  );
};
