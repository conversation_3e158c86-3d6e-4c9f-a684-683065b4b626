import { endOfDay, formatISO, isSameDay, startOfDay } from "date-fns";
import { NextApiRequest, NextApiResponse } from "next";
import { supabaseClient } from "utils/supabase/client";

interface PaymentSummary {
  totalAmount: number;
  totalTransactions: number;
  byMethod: {
    cash: {
      amount: number;
      count: number;
      breakdown: Record<string, number>;
    };
    credit: {
      amount: number;
      count: number;
      splitAmount: number;
      breakdown: Record<string, number>;
    };
    other: {
      amount: number;
      count: number;
      breakdown: Record<string, number>;
      totalPoints: number;
    };
    split: {
      amount: number;
      count: number;
      cashPayment: number;
      creditPayment: number;
      otherPayment: number;
    };
  };
}

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  const { dateRange } = req.query;

  if (!dateRange) {
    return res.status(400).json({ error: "Date range is required" });
  }

  const { fromDate, toDate } = JSON.parse(dateRange as string);
  const startDate = startOfDay(new Date(fromDate));
  const endDate = endOfDay(new Date(toDate));

  try {
    const [
      memberResponse,
      ordersResponse,
      paymentsResponse,
      membershipsResponse,
      checkinsResponse,
    ] = await Promise.all([
      supabaseClient
        .from("members")
        .select("id, users!inner(role, status), created_at", { count: "exact" })
        .eq("users.role", "member")
        .gte("created_at", startDate.toISOString())
        .lte("created_at", endDate.toISOString()),

      supabaseClient
        .from("orders")
        .select(
          "id, member_id, is_completed, on_hold, created_at, items, total_points_used",
          {
            count: "exact",
          }
        )
        .gte("created_at", startDate.toISOString())
        .lte("created_at", endDate.toISOString()),

      supabaseClient
        .from("payments")
        .select(
          `
          id, 
          amount, 
          method, 
          created_at, 
          split_details,
          membership_id,
          memberships (
            id,
            total_points
          )
        `,
          {
            count: "exact",
          }
        )
        .gte("created_at", startDate.toISOString())
        .lte("created_at", endDate.toISOString()),

      supabaseClient
        .from("memberships")
        .select(
          "id, member_id, status, total_points, available_points, used_points, created_at, start_date, end_date",
          {
            count: "exact",
          }
        )
        .lte("start_date", endDate.toISOString())
        .gte("end_date", startDate.toISOString()),

      supabaseClient
        .from("checkins")
        .select("id, created_at, checkout_date, member_id", { count: "exact" })
        .gte("created_at", startDate.toISOString())
        .lte("created_at", endDate.toISOString()),
    ]);

    // Destructure the responses
    const { data: memberData, count: memberCount } = memberResponse;
    const { data: ordersData, count: ordersCount } = ordersResponse;
    const { data: paymentsData, count: paymentsCount } = paymentsResponse;
    const { data: membershipsData, count: membershipsCount } =
      membershipsResponse;
    const { data: checkinsData, count: checkinsCount } = checkinsResponse;

    // Calculate unduplicated consumer count
    const uniqueMemberIds = new Set();

    membershipsData?.forEach((membership) => {
      uniqueMemberIds.add(membership.member_id);
    });

    // Add members who have checked in
    checkinsData?.forEach((checkin) => {
      if (checkin.member_id) {
        uniqueMemberIds.add(checkin.member_id);
      }
    });

    // Add members who have checked out
    checkinsData?.forEach((checkin) => {
      if (checkin.checkout_date && checkin.member_id) {
        uniqueMemberIds.add(checkin.member_id);
      }
    });

    const unduplicatedConsumerCount = uniqueMemberIds.size;

    const processPayments = (paymentsData) => {
      const paymentSummary: PaymentSummary = {
        totalAmount: 0,
        totalTransactions: 0,
        byMethod: {
          cash: {
            amount: 0,
            count: 0,
            breakdown: {
              "11.45": 0,
              "13.45": 0,
              "15.45": 0,
            },
          },
          credit: {
            amount: 0,
            count: 0,
            splitAmount: 0,
            breakdown: {
              "11.45": 0,
              "13.45": 0,
              "15.45": 0,
            },
          },
          other: {
            amount: 0,
            count: 0,
            breakdown: {},
            totalPoints: 0,
          },
          split: {
            amount: 0,
            count: 0,
            cashPayment: 0,
            creditPayment: 0,
            otherPayment: 0,
          },
        },
      };

      paymentsData?.forEach((payment) => {
        const paymentAmount = Number(payment.amount) || 0;
        paymentSummary.totalAmount += paymentAmount;
        paymentSummary.totalTransactions++;

        if (payment.method === "coupon" && payment.memberships) {
          paymentSummary.byMethod.other.count++;
          paymentSummary.byMethod.other.amount += paymentAmount;

          if (payment.memberships.total_points) {
            paymentSummary.byMethod.other.totalPoints += Number(
              payment.memberships.total_points
            );
          }
        } else if (payment.method === "split" && payment.split_details) {
          try {
            const splitDetails =
              typeof payment.split_details === "string"
                ? JSON.parse(payment.split_details)
                : payment.split_details;

            if (Array.isArray(splitDetails)) {
              paymentSummary.byMethod.split.count++;
              paymentSummary.byMethod.split.amount += paymentAmount;

              splitDetails.forEach((detail) => {
                const splitMethod = detail.method || "other";
                const splitAmount = Number(detail.amount) || 0;
                const amountStr = splitAmount.toFixed(2);

                if (splitMethod === "cash") {
                  paymentSummary.byMethod.split.cashPayment += splitAmount;
                  paymentSummary.byMethod.cash.amount += splitAmount;
                  paymentSummary.byMethod.cash.count++;
                  paymentSummary.byMethod.cash.breakdown[amountStr] =
                    (paymentSummary.byMethod.cash.breakdown[amountStr] || 0) +
                    1;
                } else if (splitMethod === "credit") {
                  paymentSummary.byMethod.split.creditPayment += splitAmount;
                  paymentSummary.byMethod.credit.splitAmount += splitAmount;
                  paymentSummary.byMethod.credit.count++;
                } else if (splitMethod === "coupon" && payment.memberships) {
                  paymentSummary.byMethod.other.amount += splitAmount;
                  paymentSummary.byMethod.other.count++;
                  if (payment.memberships.total_points) {
                    paymentSummary.byMethod.other.totalPoints += Number(
                      payment.memberships.total_points
                    );
                  }
                } else {
                  paymentSummary.byMethod.split.otherPayment += splitAmount;
                  paymentSummary.byMethod.other.amount += splitAmount;
                  paymentSummary.byMethod.other.count++;
                }
              });
            }
          } catch (error) {
            console.error("Error parsing split payment details:", error);
            paymentSummary.byMethod.other.amount += paymentAmount;
            paymentSummary.byMethod.other.count++;
          }
        } else {
          const method = payment.method || "other";
          const amountStr = paymentAmount.toFixed(2);

          if (method === "cash") {
            paymentSummary.byMethod.cash.amount += paymentAmount;
            paymentSummary.byMethod.cash.count++;
            paymentSummary.byMethod.cash.breakdown[amountStr] =
              (paymentSummary.byMethod.cash.breakdown[amountStr] || 0) + 1;
          } else if (method === "credit") {
            paymentSummary.byMethod.credit.amount += paymentAmount;
            paymentSummary.byMethod.credit.count++;
            if (["11.45", "13.45", "15.45"].includes(amountStr)) {
              paymentSummary.byMethod.credit.breakdown[amountStr]++;
            }
          } else {
            paymentSummary.byMethod.other.amount += paymentAmount;
            paymentSummary.byMethod.other.count++;
          }
        }
      });

      return paymentSummary;
    };

    const paymentSummary = processPayments(paymentsData);

    const calculateTrafficPerHour = (checkinsData) => {
      const hourlyTraffic = checkinsData.reduce((acc, checkin) => {
        const hour = new Date(checkin.created_at).getUTCHours();
        acc[hour] = (acc[hour] || 0) + 1;
        return acc;
      }, {});

      const trafficData = Object.keys(hourlyTraffic).map((hour) => ({
        hour: Number(hour),
        checkin_count: hourlyTraffic[hour],
      }));

      return trafficData.sort((a, b) => a.hour - b.hour);
    };

    const calculateAverageDuration = (checkinsData) => {
      const durations = checkinsData
        .filter((checkin) => checkin.checkout_date)
        .map((checkin) => {
          const createdAt = new Date(checkin.created_at).getTime();
          const checkoutAt = new Date(checkin.checkout_date).getTime();
          return (checkoutAt - createdAt) / (1000 * 60);
        });

      const averageDuration =
        durations.reduce((sum, duration) => sum + duration, 0) /
        (durations.length || 1);

      return averageDuration.toFixed(2);
    };

    const pointsBreakdown = membershipsData?.reduce(
      (acc, membership) => {
        const status = membership.status;
        const availablePoints = Number(membership.available_points) || 0;
        const membershipStartDate = new Date(membership.start_date);
        const membershipEndDate = new Date(membership.end_date);

        if (membershipStartDate <= endDate && membershipEndDate >= startDate) {
          if (status === "active") {
            acc.activePoints += availablePoints;
            acc.activeCount++;
          } else if (status === "inactive") {
            acc.inactivePoints += availablePoints;
            acc.inactiveCount++;
          } else if (status === "expired") {
            acc.expiredPoints += availablePoints;
            acc.expiredCount++;
          }
        }

        return acc;
      },
      {
        activePoints: 0,
        inactivePoints: 0,
        expiredPoints: 0,
        activeCount: 0,
        inactiveCount: 0,
        expiredCount: 0,
      }
    ) || {
      activePoints: 0,
      inactivePoints: 0,
      expiredPoints: 0,
      activeCount: 0,
      inactiveCount: 0,
      expiredCount: 0,
    };

    const activitySummary = {
      members: {
        count: memberCount || 0,
        byStatus: memberData?.reduce(
          (statuses: Record<string, number>, member: any) => {
            const status = member.users?.status || "unknown";
            statuses[status] = (statuses[status] || 0) + 1;
            return statuses;
          },
          {}
        ),
        unduplicatedConsumerCount: unduplicatedConsumerCount || 0,
      },
      checkins: {
        today:
          checkinsData?.filter((checkin) => {
            if (!checkin.checkout_date) {
              const checkinDate = new Date(checkin.created_at);
              return isSameDay(checkinDate, new Date());
            }
          }).length || 0,
        trafficPerHour: calculateTrafficPerHour(checkinsData),
        averageShoppingDuration: calculateAverageDuration(checkinsData),
      },
      orders: {
        count: ordersCount || 0,
        completed:
          ordersData?.filter((order) => order.is_completed).length || 0,
        onHold: ordersData?.filter((order) => order.on_hold).length || 0,
        totalPointsUsed:
          ordersData
            ?.filter(
              (order) => order.is_completed === true && order.on_hold === false
            )
            ?.reduce(
              (sum, order) => sum + (Number(order.total_points_used) || 0),
              0
            ) || 0,
        distributed:
          ordersData
            ?.filter(
              (order) => order.is_completed === true && order.on_hold === false
            )
            ?.reduce((sum, order) => {
              return (
                sum +
                order.items.reduce(
                  (itemSum, item) => itemSum + (Number(item.quantity) || 0),
                  0
                )
              );
            }, 0) || 0,
      },
      payments: {
        count: paymentsCount || 0,
        totalAmount: paymentSummary.totalAmount,
        totalTransactions: paymentSummary.totalTransactions,
        byMethod: paymentSummary.byMethod,
      },
      memberships: {
        count: membershipsCount || 0,
        totalPoints:
          membershipsData?.reduce(
            (sum, membership) => sum + (Number(membership.total_points) || 0),
            0
          ) || 0,
        availablePoints:
          (Number(pointsBreakdown.activePoints) || 0) +
          (Number(pointsBreakdown.inactivePoints) || 0),
        usedPoints:
          membershipsData?.reduce(
            (sum, membership) => sum + (Number(membership.used_points) || 0),
            0
          ) || 0,
        activePoints: pointsBreakdown.activePoints,
        inactivePoints: pointsBreakdown.inactivePoints,
        expiredPoints: pointsBreakdown.expiredPoints,
        byStatus: membershipsData?.reduce((statuses, membership) => {
          statuses[membership.status] = (statuses[membership.status] || 0) + 1;
          return statuses;
        }, {}),
      },
    };

    return res.status(200).json(activitySummary);
  } catch (error) {
    console.error("Error fetching activity summary:", error);
    return res.status(500).json({ error: "Failed to fetch activity summary." });
  }
}
