import { NextApiRequest, NextApiResponse } from "next";
import { supabaseClient } from "utils/supabase/client";

export default async function GET(req: NextApiRequest, res: NextApiResponse) {
  try {
    const { role } = req.query;

    if (!["admin", "staff", "volunteer"].includes(role as string)) {
      return res.status(403).json({
        message:
          "Unauthorized. Only staff, admin or volunteer can view return item data.",
      });
    }

    const { data: returnItems, error: returnItemsError } = await supabaseClient
      .from("return_items")
      .select(
        `
        *,
        member:members (id, first_name, last_name),
        products (id, product_name, product_code, unit_price, unit_points, unit_weight, category!category(id, name))
        `
      )
      .order("created_at", { ascending: false });

    if (returnItemsError) {
      return res.status(500).json({
        message: "Error fetching return items from the database",
        error: returnItemsError.message,
      });
    }

    return res.status(200).json({
      message: "Return items fetched successfully!",
      data: returnItems,
    });
  } catch (error) {
    console.error(error);
    return res.status(500).json({
      message: "Failed to process the request.",
    });
  }
}
