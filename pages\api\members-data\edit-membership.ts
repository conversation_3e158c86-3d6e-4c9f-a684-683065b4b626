import { NextApiRequest, NextApiResponse } from "next";
import { addTimeToDate } from "utils";
import { supabaseClient } from "utils/supabase/client";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== "PUT") {
    return res.status(405).json({ message: "Method not allowed" });
  }

  try {
    const {
      start_date,
      end_date,
      payment_method,
      amount,
      total_points,
      created_by,
      membership_id,
      split_payments,
      updated_store_location,
      coupon_code,
      used_points,
      fee_waived,
      credit_card_fee,
    } = req.body;

    console.log("req.body", req.body);

    const { data: userData, error: userError } = await supabaseClient
      .from("users")
      .select("id, role")
      .eq("user_auth_id", created_by)
      .single();

    if (userError) {
      return res.status(404).json({
        message: "User not found",
      });
    }

    if (!userData || !["staff", "admin"].includes(userData.role)) {
      return res.status(403).json({
        message: "Unauthorized. Only staff and admin can create memberships.",
      });
    }

    const { data: memberData, error: memberError } = await supabaseClient
      .from("members")
      .select("id")
      .eq("user_id", userData.id)
      .single();

    if (memberError) {
      return res.status(404).json({
        message: "Member not found",
      });
    }

    let couponData;
    if (payment_method === "coupon") {
      const { data, error } = await supabaseClient
        .from("coupons")
        .select("id, point_value,start_date, end_date, status, coupon_limit")
        .ilike("coupon_code", coupon_code)
        .single();

      if (error || !data) {
        return res.status(404).json({
          message: "Coupon not found or invalid.",
        });
      }

      couponData = data;
      const currentDate = new Date().toISOString().split("T")[0];
      const isCouponValid =
        data.start_date <= currentDate &&
        data.end_date >= currentDate &&
        data.status === "active";

      if (!isCouponValid) {
        return res.status(400).json({
          message: "Coupon is not valid or expired.",
        });
      }

      // Check coupon usage count against coupon limit
      if (data.coupon_limit > 0) {
        const { count, error: countError } = await supabaseClient
          .from("payments")
          .select("id", { count: "exact", head: true })
          .eq("coupon_id", data.id)
          .eq("method", "coupon");

        if (countError) {
          console.error("Error checking coupon usage:", countError);
          return res.status(500).json({
            message: "Error validating coupon",
            error: countError.message,
          });
        }

        if (count && count >= data.coupon_limit) {
          return res.status(400).json({
            message: "Coupon has reached its usage limit.",
          });
        }
      }
    }

    const { data: membershipData, error: membershipError } =
      await supabaseClient
        .from("memberships")
        .update({
          start_date: addTimeToDate(start_date),
          end_date: addTimeToDate(end_date),
          total_points:
            payment_method === "coupon" ? couponData.point_value : total_points,
          available_points:
            used_points > 0
              ? total_points - used_points
              : payment_method === "coupon"
              ? couponData.point_value
              : total_points,
          total_paid: amount,
          status: "active",
          updated_by: memberData?.id,
          updated_store_location: updated_store_location,
        })
        .eq("id", membership_id)
        .select()
        .single();

    if (membershipError) {
      console.error("Error adding membership:", membershipError);
      return res.status(500).json({
        message: "Error adding membership",
        error: membershipError.message,
      });
    }

    const { error: paymentError } = await supabaseClient
      .from("payments")
      .update({
        amount,
        method: split_payments ? "split" : payment_method,
        split_details: split_payments ? split_payments : null,
        date: new Date().toISOString(),
        coupon_id: payment_method === "coupon" ? couponData.id : null,
        fee_waived,
        credit_card_fee,
      })
      .eq("membership_id", membershipData?.id)
      .select()
      .single();

    if (paymentError) {
      console.error("Error adding payment:", paymentError);
      return res.status(500).json({
        message: "Error adding payment",
        error: paymentError.message,
      });
    }

    return res.status(200).json({
      message: "Membership added successfully",
    });
  } catch (error: any) {
    console.error("Error adding membership:", error);
    return res.status(500).json({
      message: error.message || "Error adding membership",
    });
  }
}
