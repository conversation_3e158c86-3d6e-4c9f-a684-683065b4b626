import { CustomButton } from "@/components/ui/custom-button";
import { DateRangeFilter } from "@/components/ui/date-range-filter";
import { Skeleton } from "@/components/ui/skeleton";
import { useReturnItem } from "context/useReturnItem";
import React, { useState } from "react";
import { formatDateWithTimestamp } from "utils";
import { ReturnItemForm } from "./return-item-form";

interface ReturnItem {
  id: number;
  created_at: Date;
  product_id: number;
  quantity: string;
  total_points: string;
  unit_points: string;
  reason: string;
  store_location: string;
  member: {
    id: number;
    first_name: string;
    last_name: string;
  };
  products: {
    id: number;
    unit_price: string;
    unit_points: string;
    unit_weight: string;
    product_code: string;
    product_name: string;
    category: {
      id: number;
      name: string;
    };
  };
}

export default function ReturnItemsContent() {
  const { returnItemData, fetchingReturnItem } = useReturnItem();
  const [showReturnModal, setShowReturnModal] = useState(false);
  const [dateRange, setDateRange] = useState<{
    from: Date | undefined;
    to: Date | undefined;
  }>({
    from: undefined,
    to: undefined,
  });

  const filterReturnItems = (returnItems: ReturnItem[]) => {
    return returnItems.filter((item) => {
      const createdAt = new Date(item.created_at);

      return (
        (!dateRange.from || createdAt >= dateRange.from) &&
        (!dateRange.to || createdAt <= dateRange.to)
      );
    });
  };

  return (
    <>
      <div className="relative flex justify-start md:justify-end mb-4 gap-4">
        <CustomButton
          variant="outline"
          onClick={() => setShowReturnModal(true)}
        >
          Return Item
        </CustomButton>
        {returnItemData.length > 0 && (
          <DateRangeFilter onDateRangeChange={setDateRange} isCustom={false} />
        )}
      </div>
      <div className="relative grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 h-full w-full">
        {fetchingReturnItem &&
          Array.from({ length: 4 }).map((_, index) => (
            <div key={index} className="px-2 py-1.5 flex">
              <Skeleton className="h-40 w-full" />
            </div>
          ))}
        {!fetchingReturnItem &&
          filterReturnItems(returnItemData).length === 0 && (
            <div className="col-span-full text-center py-8">
              <p className="text-gray-500">
                No return items found for the selected date range.
              </p>
            </div>
          )}
        {!fetchingReturnItem &&
          filterReturnItems(returnItemData).map((item: ReturnItem) => (
            <ReturnedItems key={item.id} item={item} />
          ))}
        <ReturnItemForm
          isOpen={showReturnModal}
          onClose={() => setShowReturnModal(false)}
        />
      </div>
    </>
  );
}

function ReturnedItems({ item }: { item: ReturnItem }) {
  return (
    <div
      key={item.id}
      className="relative w-full h-full p-4 border rounded-lg shadow-sm bg-white hover:shadow-md transition-shadow duration-200"
    >
      <div className="mb-2">
        <h2
          className="text-lg font-semibold truncate"
          title={item.products?.product_name || "Unknown Product"}
        >
          {item.products?.product_name || "Unknown Product"}
        </h2>
      </div>
      <div className="space-y-1.5">
        <p className="text-sm text-gray-500 flex justify-between">
          <span>Returned by:</span>
          <span className="font-medium">
            {item.member?.first_name && item.member?.last_name
              ? `${item.member.first_name} ${item.member.last_name}`
              : "Unknown Member"}
          </span>
        </p>
        <p className="text-sm text-gray-500 flex justify-between">
          <span>Product Category:</span>
          <span className="font-medium">
            {item.products?.category?.name || "N/A"}
          </span>
        </p>
        <p className="text-sm text-gray-500 flex justify-between">
          <span>Quantity:</span>
          <span className="font-medium">{item.quantity}</span>
        </p>
        <p className="text-sm flex justify-between">
          <span>Points:</span>
          <span className="font-medium">{item.total_points}</span>
        </p>
        <p className="text-sm flex justify-between">
          <span>Location:</span>
          <span className="font-medium">{item.store_location || "N/A"}</span>
        </p>
        <div className="text-sm pt-1">
          <span className="text-gray-600">Reason:</span>
          <p className="text-gray-800 italic mt-1 text-xs">
            {item.reason || "No reason provided"}
          </p>
        </div>
        <div className="pt-2 mt-1 border-t border-gray-100">
          <p className="text-xs text-gray-500">
            Returned on: {formatDateWithTimestamp(item.created_at)}
          </p>
        </div>
      </div>
    </div>
  );
}
