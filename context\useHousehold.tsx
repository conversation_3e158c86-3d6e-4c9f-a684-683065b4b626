import React, { createContext, useState, useContext, ReactNode } from "react";
import { useSelectClient } from "./useSelectClientContext";

interface HouseholdMember {
  memberName: string;
  memberBirthday: string;
  memberIncome: string;
  memberIncomeNotes: string;
  memberType: string;
}

interface HouseholdContextType {
  householdData: any;
  setHouseholdData: React.Dispatch<React.SetStateAction<any>>;
  birthdays: Record<number, string>;
  setBirthdays: React.Dispatch<React.SetStateAction<Record<number, string>>>;
  getAge: (index: number) => string | number;
  getAgeGroups: () => Record<string, number>;
  handleBirthdayChange: (index: number, value: string) => void;
  memberIncomes: Record<number, number>;
  setMemberIncomes: React.Dispatch<
    React.SetStateAction<Record<number, number>>
  >;
  totalMemberIncome: number;
  getHouseholdMembers: () => HouseholdMember[];
  memberCount: number;
  setMemberCount: React.Dispatch<React.SetStateAction<number>>;
}

const HouseholdContext = createContext<HouseholdContextType | null>(null);

export const useHouseholdData = () => {
  const context = useContext(HouseholdContext);
  if (!context) {
    throw new Error(
      "useHouseholdData must be used within a HouseholdDataProvider"
    );
  }
  return context;
};

interface HouseholdDataProviderProps {
  children: ReactNode;
}

export const HouseholdDataProvider: React.FC<HouseholdDataProviderProps> = ({
  children,
}) => {
  const { selectedClient } = useSelectClient();
  const [householdData, setHouseholdData] = useState<any>([]);
  const [birthdays, setBirthdays] = useState<Record<number, string>>({});
  const [memberIncomes, setMemberIncomes] = useState<Record<number, number>>(
    {}
  );
  const [totalMemberIncome, setTotalMemberIncome] = useState(0);
  const [memberCount, setMemberCount] = React.useState(1);

  React.useEffect(() => {
    const memberIncomeTotal = Object.values(memberIncomes)
      .map((income) => Number(income))
      .filter((income) => !isNaN(income))
      .reduce((sum, income) => sum + income, 0);

    setTotalMemberIncome(memberIncomeTotal);
  }, [memberIncomes]);

  const calculateAge = (birthday: string) => {
    const birthDate = new Date(birthday);
    const today = new Date();
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();

    if (
      monthDiff < 0 ||
      (monthDiff === 0 && today.getDate() < birthDate.getDate())
    ) {
      age--;
    }
    return age;
  };

  const getAge = (index: number) => {
    const birthday = birthdays[index];

    if (
      !birthday &&
      selectedClient?.additional_info?.household_members?.[index]
    ) {
      const memberBirthday =
        selectedClient.additional_info.household_members[index].memberBirthday;
      if (memberBirthday) {
        return calculateAge(memberBirthday);
      }
    }

    return birthday ? calculateAge(birthday) : "-";
  };

  const handleBirthdayChange = (index: number, value: string) => {
    setBirthdays((prev) => ({ ...prev, [index]: value }));
  };

  const getAgeGroups = () => {
    const ageRanges: [string, number, number | null][] = [
      ["Children 0-2", 0, 2],
      ["Children 3-5", 3, 5],
      ["Children 6-8", 6, 8],
      ["Children 9-12", 9, 12],
      ["Children 13-17", 13, 17],
      ["Adults 18-64", 18, 64],
      ["Adults 65+", 65, null],
    ];

    const allBirthdays = { ...birthdays };

    return Object.values(allBirthdays).reduce(
      (groups, birthday) => {
        const age = calculateAge(birthday);
        const group = ageRanges.find(
          ([, min, max]) => age >= min && (max === null || age <= max)
        );
        if (group) groups[group[0]]++;
        return groups;
      },
      Object.fromEntries(ageRanges.map(([label]) => [label, 0]))
    );
  };

  const getHouseholdMembers = (): HouseholdMember[] => {
    const members: HouseholdMember[] = [];

    const memberCount =
      selectedClient?.additional_info?.household_members?.length ||
      Object.keys(birthdays).length ||
      0;

    for (let i = 0; i < memberCount; i++) {
      const clientMember =
        selectedClient?.additional_info?.household_members?.[i] || {};

      members.push({
        memberName: clientMember.memberName || "",
        memberBirthday: birthdays[i] || clientMember.memberBirthday || "",
        memberIncome:
          memberIncomes[i]?.toString() || clientMember.memberIncome || "",
        memberIncomeNotes: clientMember.memberIncomeNotes || "",
        memberType: clientMember.memberType || "adult",
      });
    }

    return members;
  };

  return (
    <HouseholdContext.Provider
      value={{
        householdData,
        setHouseholdData,
        birthdays,
        setBirthdays,
        getAge,
        getAgeGroups,
        handleBirthdayChange,
        memberIncomes,
        setMemberIncomes,
        totalMemberIncome,
        getHouseholdMembers,
        memberCount,
        setMemberCount,
      }}
    >
      {children}
    </HouseholdContext.Provider>
  );
};
