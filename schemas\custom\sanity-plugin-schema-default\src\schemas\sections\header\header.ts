import { rootSchema } from "@webriq-pagebuilder/sanity-plugin-schema-default";
import { headerVariants as baseVariantsList } from "@webriq-pagebuilder/sanity-plugin-schema-default";
import { MdVerticalAlignTop } from "react-icons/md";

import variantAImage from "./images/variant_a.jpg";
import variantGImage from "./images/variant_g.png";
import variantHImage from "./images/variant_h.png";
import variantIImage from "./images/variant_i.png";
import variantJImage from "./images/variant_j.png";
import variantKImage from "./images/variant_k.png";
import variantLImage from "./images/variant_l.png";
import variantMImage from "./images/variant_m.png";
import variantNImage from "./images/variant_n.png";
import variantOImage from "./images/variant_o.png";
import variantPImage from "./images/variant_p.png";
import variantQImage from "./images/variant_q.png";
import variantRImage from "./images/variant_r.png";
import initialValue from "./initialValue";
import { headerSchema } from "./schema";

/**
 * If you want to replace all the existing variants for this component, then define variantsList like this:
 *
 * export const variantsList = [
 *  {
 *     title: "Variant A",
 *    description: "This is a new variant A for header",
 *     value: "variant_a",
 *     image: variantAImage.src,
 *   },
 * ]
 *
 */

export const variantsList = [
  ...baseVariantsList, // adds all the existing variants for header component and insert the new variants as follows
  {
    title: "New Variant",
    description: "A new variant for header component",
    value: "variant_a", // update this with the correct variant letter
    image: variantAImage.src, // update with the correct variant image import
  },
  {
    title: "Variant G",
    description: "A new variant for header component",
    value: "variant_g", // update this with the correct variant letter
    image: variantGImage.src, // update with the correct variant image import
  },
  {
    title: "Variant H",
    description: "A new variant for header component",
    value: "variant_h",
    image: variantHImage.src,
  },
  {
    title: "Variant I",
    description: "A new variant for header component",
    value: "variant_i",
    image: variantIImage.src,
  },
  {
    title: "Variant J",
    description: "A new variant for header component",
    value: "variant_j",
    image: variantJImage.src,
  },
  {
    title: "Variant K",
    description: "A new variant for header component",
    value: "variant_k", 
    image: variantKImage.src,
  },
  {
    title: "Variant L",
    description: "A new variant for header component",
    value: "variant_l", 
    image: variantLImage.src,
  },
  {
    title: "Variant M",
    description: "A new variant for header component",
    value: "variant_m", 
    image: variantMImage.src,
  },
  {
    title: "Variant N",
    description: "A new variant for header component",
    value: "variant_n", 
    image: variantNImage.src,
  },
  {
    title: "Variant O",
    description: "A new variant for header component",
    value: "variant_o", 
    image: variantOImage.src,
  },
  {
    title: "Variant P",
    description: "A new variant for header component",
    value: "variant_p", 
    image: variantPImage.src,
  },
  {
    title: "Variant Q",
    description: "A new variant for header component",
    value: "variant_q", 
    image: variantQImage.src,
  },
  {
    title: "Variant R",
    description: "A new variant for header component",
    value: "variant_r", 
    image: variantRImage.src,
  },
];

export default rootSchema(
  "header",
  "Header",
  MdVerticalAlignTop,
  variantsList,
  headerSchema,
  initialValue
);
