import React, {
  createContext,
  useState,
  useContext,
  useEffect,
  ReactNode,
} from "react";
import axios from "axios";
import { useAuthCheck } from "hooks/useAuthCheck";
import { supabaseClient } from "utils/supabase/client";

interface ReturnItemContextType {
  returnItemData: any;
  fetchingReturnItem: boolean;
  setFetchingReturnItem: React.Dispatch<React.SetStateAction<boolean>>;
  refreshReturnItem: () => void;
}

const ReturnItemContext = createContext<ReturnItemContextType | null>(null);

export const useReturnItem = () => {
  const context = useContext(ReturnItemContext);
  if (!context) {
    throw new Error("useReturnItem must be used within a ReturnItemProvider");
  }
  return context;
};

interface ReturnItemProviderProps {
  children: ReactNode;
}

export const ReturnItemProvider: React.FC<ReturnItemProviderProps> = ({
  children,
}) => {
  const [returnItemData, setReturnItemData] = useState<any[]>([]);
  const [fetchingReturnItem, setFetchingReturnItem] = useState<boolean>(false);
  const { user, token } = useAuthCheck();

  const fetchData = async () => {
    setFetchingReturnItem(true);
    try {
      const response = await axios.get("/api/user-data/return-item/get", {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        params: { role: user?.user_metadata.role },
      });

      setReturnItemData(response.data.data);
    } catch (error) {
      console.error("Error fetching return item data:", error);
    } finally {
      setFetchingReturnItem(false);
    }
  };

  const refreshReturnItem = () => {
    fetchData();
  };

  const handleRealtimeUpdate = async (payload: any) => {
    const { eventType, new: newData, old: oldData } = payload;

    if (eventType === "DELETE" && oldData?.id) {
      setReturnItemData((prevData) =>
        prevData.filter((item) => item.id !== oldData.id)
      );
      return;
    }

    if (eventType === "INSERT") {
      try {
        const { data, error } = await supabaseClient
          .from("return_items")
          .select(
            "*, products:products(product_name), member:members(first_name, last_name)"
          )
          .eq("id", newData.id)
          .single();

        if (error) throw error;

        if (data) {
          setReturnItemData((prevData) => [data, ...prevData]);
        }
      } catch (error) {
        console.error("Error fetching full return item data:", error);
      }
      return;
    }

    if (eventType === "UPDATE") {
      setReturnItemData((prevData) => {
        const existingIndex = prevData.findIndex(
          (item) => item.id === newData.id
        );

        if (existingIndex !== -1) {
          const updatedData = [...prevData];
          updatedData[existingIndex] = newData;
          return updatedData;
        } else {
          return [newData, ...prevData];
        }
      });
    }
  };

  useEffect(() => {
    if (user) {
      fetchData();

      const returnItemSubscription = supabaseClient
        .channel("return_item_realtime")
        .on(
          "postgres_changes",
          {
            event: "*",
            schema: "public",
            table: "return_items",
          },
          async (payload) => {
            await handleRealtimeUpdate(payload);
          }
        )
        .subscribe();

      return () => {
        supabaseClient.removeChannel(returnItemSubscription);
      };
    }
  }, [user]);

  return (
    <ReturnItemContext.Provider
      value={{
        returnItemData,
        fetchingReturnItem,
        setFetchingReturnItem,
        refreshReturnItem,
      }}
    >
      {children}
    </ReturnItemContext.Provider>
  );
};
