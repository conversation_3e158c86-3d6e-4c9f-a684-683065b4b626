import { Button } from "@stackshift-ui/button";
import { Container } from "@stackshift-ui/container";
import { Flex } from "@stackshift-ui/flex";
import { Heading } from "@stackshift-ui/heading";
import { Image } from "@stackshift-ui/image";
import { Section } from "@stackshift-ui/section";
import { Text } from "@stackshift-ui/text";
import React from "react";
import { ButtonProps, HeaderProps } from ".";
import { urlFor } from "lib/sanity";

export default function Header_G({
  mainImage,
  title,
  description,
  bgImage,
}: HeaderProps) {
  return (
    <Section
      className="md:pt-36 md:pb-10 pt-12 pb-0 !px-0"
      style={{
        backgroundImage: `url(${urlFor(bgImage)})`,
        backgroundRepeat: "no-repeat",
        backgroundSize: "108%",
        backgroundPosition: "top",
      }}
    >
      <Container maxWidth={1560} className="!px-5">
        <Flex className="w-full items-center justify-center flex-col-reverse md:flex-row md:pt-0">
          <div className="w-full md:pt-0 pt-16 pb-10">
            <TitleAndDescription title={title} description={description} />
          </div>

          <div className="w-full flex items-center mx-auto">
            <MainImage mainImage={mainImage} />
          </div>
        </Flex>
      </Container>
    </Section>
  );
}

function TitleAndDescription({
  title,
  description,
}: {
  title?: string;
  description?: string;
}) {
  return (
    <div className="md:px-10 px-5">
      {title && (
        <Heading
          type="h1"
          className="mb-3 font-heading tracking-tighter !font-extrabold leading-none !text-cfc-gray md:text-5xl text-2xl"
        >
          {title}
        </Heading>
      )}

      {description && (
        <Text
          muted
          className="mt-6 !md:text-3xl md:!text-3xl text-xl tracking-wider leading-tight !font-extralight"
        >
          {description}
        </Text>
      )}
    </div>
  );
}

interface MainImageProps {
  mainImage?: {
    image?: string | any;
    alt?: string;
  };
}

function MainImage({ mainImage }: MainImageProps) {
  if (!mainImage?.image) return null;

  return (
    <div className="relative w-full max-w-md mx-auto">
      <Image
        className=" object-cover w-full md:w-[90%] lg:w-[500px] relative z-10 md:scale-110"
        src={`${mainImage.image}`}
        sizes="(min-width: 520px) 520px, 90vw"
        width={800}
        height={500}
        alt={mainImage.alt ?? "header-main-image"}
      />
    </div>
  );
}

export { Header_G };
