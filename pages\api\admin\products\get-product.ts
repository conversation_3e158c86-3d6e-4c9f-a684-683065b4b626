import { NextApiRequest, NextApiResponse } from "next";
import { supabaseClient } from "utils/supabase/client";

const handleError = (res: NextApiResponse, message: string, error: any) => {
  console.error(message, error);
  return res.status(500).json({
    message,
    error: error.message,
  });
};

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== "GET") {
    return res.status(405).json({ message: "Method not allowed" });
  }

  try {
    const { userId } = req.query;

    if (!userId || typeof userId !== "string") {
      return res.status(400).json({ message: "Invalid or missing userId" });
    }

    const { data: userData, error: userError } = await supabaseClient
      .from("users")
      .select("role")
      .eq("user_auth_id", userId)
      .single();

    if (userError) {
      return res.status(404).json({
        message: "User not found",
      });
    }

    if (!userData || !["staff", "admin", "volunteer"].includes(userData.role)) {
      return res.status(403).json({
        message: "Unauthorized. Only staff or admin can fetch product data.",
      });
    }

    const { data: productData, error: productError } = await supabaseClient
      .from("products")
      .select(
        `
        *,
        categoryData:category (
          id,
          name
        )
      `
      )
      .order("created_at", { ascending: false });

    if (productError) {
      return res.status(500).json({
        message: "Error getting product data from the database",
        error: productError.message,
      });
    }

    const formattedResults = productData.map((product) => ({
      ...product,
      category: product.categoryData.name,
    }));

    return res.status(200).json(formattedResults || []);
  } catch (error) {
    return handleError(
      res,
      "Failed to process the request due to an unexpected error.",
      error
    );
  }
}
