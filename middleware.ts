import { NextRequest, NextResponse } from "next/server";

const allowedPaths = [
  /^\/membership(\/.*)?$/,
  /^\/account-settings$/,
  /^\/login$/,
  /^\/administrator(\/.*)?$/,
  /^\/api(\/.*)?$/,
];

export function middleware(req: NextRequest) {
  const { pathname } = req.nextUrl;

  const isAllowed = allowedPaths.some((regex) => regex.test(pathname));

  if (!isAllowed) {
    // Redirect everything else to /login or a 404/unauthorized page
    const loginUrl = req.nextUrl.clone();
    loginUrl.pathname = "/login";
    return NextResponse.redirect(loginUrl);
  }

  return NextResponse.next();
}

export const config = {
  matcher: "/:path*",
};
