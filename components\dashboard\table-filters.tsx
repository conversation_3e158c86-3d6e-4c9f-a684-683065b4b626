import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuCheckboxItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { CustomButton } from "@/components/ui/custom-button";
import { Filter } from "lucide-react";

interface FilterOption {
  id: string;
  label: string;
  value: string;
}
interface TableFiltersProps {
  filters: {
    [key: string]: {
      label: string;
      options: FilterOption[];
    };
  };
  selectedFilters: {
    [key: string]: string[];
  };
  onFilterChange: (filterKey: string, value: string) => void;
}

export default function TableFilters({
  filters,
  selectedFilters,
  onFilterChange,
}: TableFiltersProps) {
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <CustomButton variant="outline" className="gap-2">
          <Filter className="h-4 w-4" />
          Filters
        </CustomButton>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="w-56 max-h-64 overflow-y-auto">
        {Object.entries(filters).map(([key, filter], index) => (
          <div key={key}>
            {index > 0 && <DropdownMenuSeparator />}
            <DropdownMenuLabel>{filter.label}</DropdownMenuLabel>
            <div className="max-h-40 overflow-y-auto">
              {filter.options.map((option) => (
                <DropdownMenuCheckboxItem
                  key={option.id}
                  checked={selectedFilters[key]?.includes(option.value)}
                  onCheckedChange={() => onFilterChange(key, option.value)}
                >
                  {option.label}
                </DropdownMenuCheckboxItem>
              ))}
            </div>
          </div>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
