import React, { useEffect } from "react";
import { Popover, PopoverContent, PopoverTrigger } from "../../ui/popover";
import { CustomButton } from "../../ui/custom-button";
import { Check, ChevronsUpDown } from "lucide-react";
import { cn } from "../../lib/utils";
import { Skeleton } from "../../ui/skeleton";
import { Input } from "../../ui/input";
import { CheckoutStatusType } from "types";
import { useCheckedInToday } from "context/useCheckedInToday";
import { useClientSearch } from "context/useClientSearchContext";
import { useAuthCheck } from "hooks/useAuthCheck";
import { getCurrentActiveMembership } from "utils";

export interface Member {
  id: string;
  first_name: string;
  last_name: string;
  memberships: any[];
  check_in: any[];
  checkoutStatus: CheckoutStatusType;
  role: string;
  orders: any[];
}

interface MemberSearchProps {
  onSelectMember: (member: Member) => void;
  selectedMember?: Member;
}

export function MemberSearch({
  onSelectMember,
  selectedMember,
}: MemberSearchProps) {
  const [open, setOpen] = React.useState(false);
  const { user } = useAuthCheck();
  const { checkedInTodayData, fetchingCheckedInToday } = useCheckedInToday();
  const { searchResults, isSearching, searchQuery, setSearchQuery } =
    useClientSearch();

  useEffect(() => {
    if (!open) {
      setSearchQuery({ query: "", role: user?.user_metadata?.role });
    }
  }, [open]);

  const displayCheckedInUsers =
    searchResults.length === 0 && !isSearching && checkedInTodayData.length > 0;

  return (
    <Popover open={open} onOpenChange={setOpen} modal={true}>
      <PopoverTrigger asChild>
        <CustomButton
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className="w-full justify-between"
          disabled={isSearching}
        >
          {selectedMember
            ? `${selectedMember.first_name} ${selectedMember.last_name}`
            : "Search for member..."}
          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </CustomButton>
      </PopoverTrigger>
      <PopoverContent className="w-[300px] p-2">
        <div className="space-y-2">
          <Input
            placeholder="Search members..."
            value={searchQuery.query}
            onChange={(e) =>
              setSearchQuery({
                query: e.target.value,
                role: user?.user_metadata?.role,
              })
            }
            className="w-full"
          />
          <div className="max-h-[300px] overflow-y-auto z-50">
            {(isSearching || fetchingCheckedInToday) && (
              <>
                {Array.from({ length: 3 }).map((_, index) => (
                  <div key={index} className="px-2 py-1.5">
                    <Skeleton className="h-12 w-full" />
                  </div>
                ))}
              </>
            )}
            {/* {!isSearching && searchResults.length === 0 && searchQuery && (
              <div className="px-2 py-4 text-center text-sm text-muted-foreground">
                No members found
              </div>
            )} */}
            {!isSearching &&
              searchResults.map((member) => (
                <SearchMember
                  key={member.id}
                  member={member}
                  onSelectMember={onSelectMember}
                  setOpen={setOpen}
                  setSearchQuery={setSearchQuery}
                  selectedMember={selectedMember}
                  user={user}
                />
              ))}

            {displayCheckedInUsers &&
              checkedInTodayData
                .filter(
                  (member) =>
                    member.checkoutStatus === "has-checked-in" ||
                    member.checkoutStatus === "ready"
                )
                .map((member) => (
                  <CheckedInToday
                    key={member.id}
                    member={member}
                    onSelectMember={onSelectMember}
                    setOpen={setOpen}
                    setSearchQuery={setSearchQuery}
                    selectedMember={selectedMember}
                    user={user}
                  />
                ))}
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );
}

function SearchMember({
  member,
  onSelectMember,
  setOpen,
  setSearchQuery,
  selectedMember,
  user,
}: {
  member: Member;
  onSelectMember: (member: Member) => void;
  setOpen: (open: boolean) => void;
  setSearchQuery: (searchQuery: { query: string; role: string }) => void;
  selectedMember: Member | undefined;
  user: any;
}) {
  return (
    <div
      key={member.id}
      className="flex items-center px-2 py-1.5 cursor-pointer hover:bg-accent hover:text-accent-foreground rounded-md"
      onClick={() => {
        onSelectMember(member);
        setOpen(false);
        setSearchQuery({ query: "", role: user?.user_metadata?.role });
      }}
    >
      <div className="flex items-center flex-1">
        <Check
          className={cn(
            "mr-2 h-4 w-4",
            selectedMember?.id === member.id ? "opacity-100" : "opacity-0"
          )}
        />
        <div className="flex flex-col">
          <span className="text-xs text-muted-foreground">
            Member ID: {member.id}
          </span>
          <span>
            {member.first_name} {member.last_name}
          </span>
          <div className="flex flex-col gap-1">
            <span className="text-xs text-muted-foreground">
              Available Points:{" "}
              {getCurrentActiveMembership(member.memberships)
                ?.available_points || 0}
            </span>
            <StatusBadge checkoutStatus={member.checkoutStatus} />
          </div>
        </div>
      </div>
    </div>
  );
}

function CheckedInToday({
  member,
  onSelectMember,
  setOpen,
  setSearchQuery,
  selectedMember,
  user,
}: {
  member: Member;
  onSelectMember: (member: Member) => void;
  setOpen: (open: boolean) => void;
  setSearchQuery: (searchQuery: { query: string; role: string }) => void;
  selectedMember: Member | undefined;
  user: any;
}) {
  return (
    <div
      key={member.id}
      className="flex items-center px-2 py-1.5 cursor-pointer hover:bg-accent hover:text-accent-foreground rounded-md"
      onClick={() => {
        onSelectMember(member);
        setOpen(false);
        setSearchQuery({ query: "", role: user?.user_metadata?.role });
      }}
    >
      <div className="flex items-center flex-1">
        <Check
          className={cn(
            "mr-2 h-4 w-4",
            selectedMember?.id === member.id ? "opacity-100" : "opacity-0"
          )}
        />
        <div className="flex flex-col">
          <span className="text-xs text-muted-foreground">
            Member ID: {member.id}
          </span>
          <span>
            {member.first_name} {member.last_name}
          </span>
          <div className="flex flex-col gap-1">
            <span className="text-xs text-muted-foreground">
              Available Points:{" "}
              {getCurrentActiveMembership(member.memberships)
                ?.available_points || 0}
            </span>
            <StatusBadge checkoutStatus={member.checkoutStatus} />
          </div>
        </div>
      </div>
    </div>
  );
}

function StatusBadge({ checkoutStatus }) {
  const getStatus = () => {
    switch (checkoutStatus) {
      case "has-checked-in":
      case "ready":
        return {
          label: "Ready to Checkout",
          bgClass: "bg-green-100 text-green-800",
        };
      case "already-checked-out":
        return {
          label: "Already Checked Out",
          bgClass: "bg-yellow-100 text-yellow-800",
        };
      case "not-checked-in":
        return {
          label: "Not Checked In",
          bgClass: "bg-blue-100 text-blue-800",
        };
      case "inactive":
        return {
          label: "Inactive",
          bgClass: "bg-gray-100 text-gray-800",
        };
      case "expired":
        return {
          label: "Expired",
          bgClass: "bg-red-100 text-red-800",
        };
      case "no-membership":
        return {
          label: "No Membership",
          bgClass: "bg-gray-200 text-gray-800",
        };
      default:
        return {
          label: "Unknown Status",
          bgClass: "bg-gray-100 text-gray-800",
        };
    }
  };

  const { label, bgClass } = getStatus();

  return (
    <span className={`text-xs px-2 py-0.5 rounded-full w-fit ${bgClass}`}>
      {label}
    </span>
  );
}
