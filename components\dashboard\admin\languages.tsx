import { CustomButton } from "@/components/ui/custom-button";
import { Input } from "@/components/ui/input";
import { Pencil, Plus, Search, Trash2, X } from "lucide-react";
import React, { useState } from "react";
import { useLanguageData } from "context/useLanguageData";
import { DataTable } from "@/components/data-table";
import { formatDate } from "utils";
import ConfirmationModal from "@/components/dashboard/confirmation-modal";
import { useAlert } from "context/useAlert";
import { useAuthCheck } from "hooks/useAuthCheck";

interface Language {
  id: string;
  name: string;
  created_at: string;
}

function LanguageForm({
  onClose,
  onSubmit,
  initialData = null,
}: {
  onClose: () => void;
  onSubmit: (data: { name: string; id?: string }) => Promise<void>;
  initialData?: Language | null;
}) {
  const [name, setName] = useState(initialData?.name || "");
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);
    try {
      await onSubmit({ name, id: initialData?.id });
      setName("");
      onClose();
    } catch (error) {
      console.error("Error saving language:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-semibold">
            {initialData ? "Edit Language" : "Add New Language"}
          </h3>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700"
          >
            <X className="h-5 w-5" />
          </button>
        </div>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label
              htmlFor="name"
              className="block text-sm font-medium text-gray-700 mb-1"
            >
              Language Name
            </label>
            <Input
              id="name"
              value={name}
              onChange={(e) => setName(e.target.value)}
              placeholder="Enter language name"
              required
            />
          </div>
          <div className="flex justify-end gap-2">
            <CustomButton
              type="button"
              variant="outline"
              onClick={onClose}
              disabled={isSubmitting}
            >
              Cancel
            </CustomButton>
            <CustomButton type="submit" disabled={isSubmitting}>
              {isSubmitting
                ? "Saving..."
                : initialData
                ? "Save Changes"
                : "Add Language"}
            </CustomButton>
          </div>
        </form>
      </div>
    </div>
  );
}

export default function Languages() {
  const [searchTerm, setSearchTerm] = useState("");
  const [showForm, setShowForm] = useState(false);
  const [selectedLanguage, setSelectedLanguage] = useState<Language | null>(
    null
  );
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { languageData, fetchingLanguage, refreshData } = useLanguageData();
  const { showAlert } = useAlert();
  const { token } = useAuthCheck();

  const handleSaveLanguage = async (data: { name: string; id?: string }) => {
    setIsSubmitting(true);
    try {
      const method = data.id ? "PUT" : "POST";
      const response = await fetch("/api/admin/language", {
        method,
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        throw new Error("Failed to save language");
      }

      showAlert(
        `Language ${data.id ? "updated" : "added"} successfully`,
        "success"
      );
      refreshData();
      setShowForm(false);
      setSelectedLanguage(null);
    } catch (error) {
      console.error("Error saving language:", error);
      showAlert(`Error ${data.id ? "updating" : "adding"} language`, "error");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDeleteLanguage = async () => {
    if (!selectedLanguage) return;

    setIsSubmitting(true);
    try {
      const response = await fetch("/api/admin/language", {
        method: "DELETE",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({ id: selectedLanguage.id }),
      });

      if (!response.ok) {
        throw new Error("Failed to delete language");
      }

      showAlert("Language deleted successfully", "success");
      refreshData();
      setShowDeleteModal(false);
      setSelectedLanguage(null);
    } catch (error) {
      console.error("Error deleting language:", error);
      showAlert("Error deleting language", "error");
    } finally {
      setIsSubmitting(false);
    }
  };

  const filterUsers = (language: typeof languageData) => {
    return language.filter((lang) => {
      const matchesSearch = lang.name
        .toLowerCase()
        .includes(searchTerm.toLowerCase());

      return matchesSearch;
    });
  };

  const headers = ["ID", "Created At", "Name", "Actions"];

  const renderRow = (language: Language) => (
    <tr key={language.id} className="border-b">
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
        {language.id}
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
        {formatDate(language.created_at)}
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
        {language.name}
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
        <div className="flex space-x-2 justify-center">
          <CustomButton
            variant="ghost"
            size="icon"
            onClick={() => {
              setSelectedLanguage(language);
              setShowForm(true);
            }}
            disabled={isSubmitting}
          >
            <Pencil className="h-4 w-4" />
          </CustomButton>
          <CustomButton
            variant="ghost"
            size="icon"
            className="text-destructive"
            onClick={() => {
              setSelectedLanguage(language);
              setShowDeleteModal(true);
            }}
            disabled={isSubmitting}
          >
            <Trash2 className="h-4 w-4" />
          </CustomButton>
        </div>
      </td>
    </tr>
  );

  return (
    <div className="p-6 space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold">Languages Management</h2>
        <CustomButton
          variant="outline"
          onClick={() => {
            setSelectedLanguage(null);
            setShowForm(true);
          }}
        >
          <Plus className="h-4 w-4 mr-2" />
          Add New Language
        </CustomButton>
      </div>

      <div className="flex gap-4 items-center">
        <div className="relative flex-1">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
          <Input
            placeholder="Search language..."
            className="pl-8"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
      </div>

      <DataTable
        headers={headers}
        data={filterUsers(languageData).sort((a, b) =>
          a.name.localeCompare(b.name)
        )}
        renderRow={renderRow}
        isLoading={fetchingLanguage}
      />

      {showForm && (
        <LanguageForm
          onClose={() => {
            setShowForm(false);
            setSelectedLanguage(null);
          }}
          onSubmit={handleSaveLanguage}
          initialData={selectedLanguage}
        />
      )}

      <ConfirmationModal
        isOpen={showDeleteModal}
        message={`Are you sure you want to delete "${selectedLanguage?.name}"?`}
        onConfirm={handleDeleteLanguage}
        onCancel={() => {
          setShowDeleteModal(false);
          setSelectedLanguage(null);
        }}
        isLoading={isSubmitting}
        isLoadingText="Deleting..."
      />
    </div>
  );
}
