import React, { useState } from "react";
import { Card } from "../../ui/card";
import { Skeleton } from "../../ui/skeleton";
import { useSelectClient } from "context/useSelectClientContext";
import { formatDate } from "utils";
import { CustomButton } from "@/components/ui/custom-button";
import { Printer } from "lucide-react";
import { useClientSearch } from "context/useClientSearchContext";
import Image from "next/image";

export default function OrderHistoryContent() {
  const [selectedReceipt, setSelectedReceipt] = useState<any>(null);
  const { isSearching } = useClientSearch();
  const { selectedClient } = useSelectClient();

  const handleOrderClick = (order: any) => {
    if (selectedReceipt?.date_of_order === formatDate(order.date_of_order)) {
      setSelectedReceipt(null);
      return;
    }
    const receipt = {
      order_number: order.id,
      date_of_order: formatDate(order.date_of_order),
      client_name: `${selectedClient?.first_name} ${selectedClient?.last_name}`,
      client_id: order.member_id,
      total_points_used: order.total_points_used,
      items: order.items.map((item: any) => ({
        product_name: item.product_name,
        quantity: item.quantity,
        points_per_unit: item.points_per_unit,
        total_points: item.total_points,
      })),
      on_hold: order.on_hold,
      available_points: order.available_points,
      renewal_date: selectedClient?.memberships[0]?.end_date,
      store_location: order.store_location,
    };
    setSelectedReceipt(receipt);
  };

  return (
    <Card className="p-6">
      <style jsx global>{`
        @media print {
          html,
          body {
            height: 100%;
            margin: 0 !important;
            padding: 0 !important;
            overflow: hidden;
          }

          @page {
            size: A4;
            margin: 15mm;
          }

          body * {
            visibility: hidden;
          }

          .no-print {
            display: none !important;
          }

          #printArea,
          #printArea * {
            visibility: visible;
          }

          #printArea {
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            padding: 15px;
            margin: 0;
          }
        }
      `}</style>

      <div className="mb-6">
        <h2 className="text-xl font-semibold">Order History</h2>
      </div>

      <div className="overflow-x-auto">
        {isSearching ? (
          <Skeleton className="w-full h-20" />
        ) : (
          <div className="border rounded-lg overflow-x-auto">
            <table className="min-w-full table-auto border-collapse divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr className="border-b bg-gray-50">
                  <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Date
                  </th>
                  <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Total Points Used
                  </th>
                  <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white text-center divide-y divide-gray-200">
                {selectedClient?.orders?.map((order) => (
                  <React.Fragment key={order.id}>
                    <tr
                      key={order.id}
                      className="odd:bg-white even:bg-gray-50 cursor-pointer hover:bg-gray-100"
                      onClick={() => handleOrderClick(order)}
                    >
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {formatDate(order.date_of_order)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {order.total_points_used}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {order.on_hold ? "On Hold" : "Completed"}
                      </td>
                    </tr>
                    {selectedReceipt?.order_number === order.id && (
                      <Receipt selectedReceipt={selectedReceipt} />
                    )}
                  </React.Fragment>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </Card>
  );
}

function Receipt({ selectedReceipt }: { selectedReceipt: any }) {
  return (
    <tr>
      <td colSpan={3} className="p-0">
        <div id="printArea" className="p-4 bg-gray-50 border-b relative">
          <CustomButton
            onClick={() => window.print()}
            variant="outline"
            size="sm"
            className="no-print absolute top-4 right-4 z-50"
          >
            <Printer className="h-4 w-4" />
          </CustomButton>
          <div className="text-center mb-8">
            <div className="flex flex-col items-center relative gap-4">
              <div>
                <Image
                  width={150}
                  height={150}
                  src="/cfclogo.png"
                  alt="Community Food Club Logo"
                  className="w-full h-full my-6"
                />
              </div>
              <h2 className="font-semibold text-xl w-full text-center mb-1">
                ORDER RECEIPT
              </h2>
            </div>
            <div className="mt-4 text-sm text-muted-foreground text-center">
              <p>
                {new Date().toLocaleDateString("en-US", {
                  month: "short",
                  day: "2-digit",
                  year: "numeric",
                })}
              </p>

              <p>{selectedReceipt.store_location}</p>
            </div>
          </div>

          <div className="space-y-2">
            <div className="flex justify-between">
              <span className="font-medium">Order #:</span>
              <span>{selectedReceipt.order_number}</span>
            </div>
            <div className="flex justify-between">
              <span className="font-medium">Order Date:</span>
              <span>{selectedReceipt.date_of_order}</span>
            </div>
            <div className="flex justify-between">
              <span className="font-medium">Client Name:</span>
              <span>{selectedReceipt.client_name}</span>
            </div>
            <div className="flex justify-between">
              <span className="font-medium">Client ID:</span>
              <span>{selectedReceipt.client_id}</span>
            </div>
            <div className="flex justify-between">
              <span className="font-medium">Status:</span>
              <span>{selectedReceipt.on_hold ? "On Hold" : "Completed"}</span>
            </div>

            <div className="mt-6">
              <h3 className="font-semibold mb-2">Order Items:</h3>
              <table className="w-full border-collapse">
                <thead>
                  <tr className="border-b">
                    <th className="px-2 py-1 text-sm text-left">Item</th>
                    <th className="px-2 py-1 text-sm text-center">Qty</th>
                    <th className="px-2 py-1 text-sm text-center">
                      Points/Unit
                    </th>
                    <th className="px-2 py-1 text-sm text-right">
                      Total Points
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {selectedReceipt?.items?.map((item: any, index: number) => (
                    <tr key={index} className="border-b">
                      <td className="px-2 py-1 text-sm">{item.product_name}</td>
                      <td className="px-2 py-1 text-sm text-center">
                        {item.quantity}
                      </td>
                      <td className="px-2 py-1 text-sm text-center">
                        {item.points_per_unit}
                      </td>
                      <td className="px-2 py-1 text-sm text-right">
                        {item.total_points}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            <div className="mt-6 pt-2 flex justify-between font-semibold">
              <span>Total Points Used:</span>
              <span>{selectedReceipt.total_points_used}</span>
            </div>

            <div className="mt-6 pt-2">
              <p className="font-medium">
                You have {selectedReceipt.available_points} points available for
                shopping.
              </p>
              <p className="font-medium">
                Usted tiene {selectedReceipt.available_points} puntos
                disponibles de compra.
              </p>
              <p className="font-medium ">
                You may renew your membership on{" "}
                {formatDate(selectedReceipt.renewal_date)}.
              </p>
              <p className="font-medium ">
                Puede renovar su membresia en:{" "}
                {formatDate(selectedReceipt.renewal_date)}.
              </p>
            </div>

            <div className="mt-8 text-center text-sm text-muted-foreground">
              <p>Thank you for shopping with us today!</p>
              <p>¡Gracias por comprar con nosotros hoy!</p>
              <p>Questions? Call us at 616.288.5550</p>
              <p>¿Preguntas? Llámenos al 616-288-5550</p>
            </div>
          </div>
        </div>
      </td>
    </tr>
  );
}
