import {
  mainImage,
  plainText,
  subtitle,
  title,
  customText,
} from "../../../common/fields";
import { hideIfVariantIn } from "@webriq-pagebuilder/sanity-plugin-schema-default";
import { BsFillPersonPlusFill } from "react-icons/bs";

export const teamSchema = [
  subtitle(hideIfVariantIn(["variant_b", "variant_e"])),
  title(hideIfVariantIn(["variant_b"])),
  {
    name: "teams",
    type: "array",
    title: "Teams",
    description:
      "Click the 'Add item' button to add a team member. If you want to edit added team member, click this ⋮ icon on its right.",
    of: [
      {
        type: "object",
        icon: BsFillPersonPlusFill,
        fields: [
          mainImage(),
          customText("name", "Full Name", "", "Enter Name Here...", 1),
          customText(
            "jobTitle",
            "Job Title",
            "",
            "Enter Job title Here...",
            1,
            hideIfVariantIn(["variant_d"])
          ),
          customText(
            "email",
            "Email",
            "",
            "<EMAIL>",
            1,
            hideIfVariantIn(["variant_d"])
          ),
          plainText(hideIfVariantIn(["variant_a", "variant_c"])),
          customText(
            "favorite",
            "Favorite",
            "",
            "Favorite Fruit or Vegetable",
            1,
            hideIfVariantIn([
              "variant_a",
              "variant_b",
              "variant_c",
              "variant_d",
            ])
          ),
        ],
      },
    ],
  },
];
