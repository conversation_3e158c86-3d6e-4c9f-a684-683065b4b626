import React, { useState, useEffect } from "react";
import { Card } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { CustomButton } from "@/components/ui/custom-button";
import { useAuthCheck } from "hooks/useAuthCheck";
import { useAlert } from "context/useAlert";
import { Label } from "@/components/ui/label";
import { DashboardNav } from "@/components/dashboard/dashboard-nav";
import { RequiredLabel } from "@/components/ui/required-label";
import { Eye, EyeOff } from "lucide-react";
import { supabaseClient } from "utils/supabase/client";
import { Skeleton } from "@/components/ui/skeleton";
import ContactInformation from "@/components/dashboard/staff/contact-information";

export default function SettingsPage() {
  const { user, loading: userLoading, token } = useAuthCheck();
  const { showAlert } = useAlert();

  const [authenticatedUser, setAuthenticatedUser] = useState<any>(null);
  const [passwordError, setPasswordError] = useState("");
  const [showPassword, setShowPassword] = useState({
    current: false,
    confirm: false,
    new: false,
  });
  const [isPending, setIsPending] = useState(false);
  const [contactInfo, setContactInfo] = useState({
    primary_number: "",
    primary_phone_type: "",
    secondary_number: "",
    secondary_phone_type: "",
    alternate_contact_name: "",
  });

  const role = user?.user_metadata?.role.toLowerCase();
  const isStaffAndAdmin = ["staff", "admin", "volunteer"].includes(role);
  const LabelComponent = isStaffAndAdmin ? Label : RequiredLabel;

  const fetchUserAuthData = async () => {
    const { data: memberData } = await supabaseClient
      .from("members")
      .select(
        "id, users!inner(username, email, id), first_name, last_name, middle_initial, contact_info, address, secondary_address, city, state, zip_code"
      )
      .eq("users.email", user?.email)
      .eq("users.username", user?.user_metadata?.username)
      .single();

    setAuthenticatedUser(memberData);
    setContactInfo(
      memberData?.contact_info ? JSON.parse(memberData?.contact_info) : {}
    );
  };

  useEffect(() => {
    if (userLoading) return;
    setIsPending(true);
    fetchUserAuthData();
    setIsPending(false);
  }, [user]);

  const togglePasswordVisibility = (field: "current" | "confirm" | "new") => {
    setShowPassword((prev) => ({
      ...prev,
      [field]: !prev[field],
    }));
  };

  const handleSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    setIsPending(true);

    const endpoint = isStaffAndAdmin
      ? "/api/user-data/update-staff"
      : "/api/user-data/update-member";

    const formData = new FormData(event.currentTarget);
    const data = Object.fromEntries(formData.entries());

    try {
      const response = await fetch(endpoint, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({
          ...data,
          userId: user?.id,
          userAuthId: user?.id,
          memberId: authenticatedUser?.id,
          currentEmail: authenticatedUser?.users?.email,
          currentUsername: authenticatedUser?.users?.username,
          role,
          contact_info: JSON.stringify(contactInfo),
        }),
      });

      const responseData = await response.json();
      if (!response.ok) {
        showAlert(responseData?.message, "error");
        throw new Error(responseData.message || "An error occurred");
      }

      showAlert(responseData?.message, "success");
      await fetchUserAuthData();
    } catch (error) {
      console.error("Error:", error);
      showAlert(error, "error");
    } finally {
      setIsPending(false);
    }
  };

  return (
    <React.Fragment>
      <DashboardNav />
      <div className="container mx-auto py-8 max-w-2xl">
        <form onSubmit={handleSubmit}>
          <Card className="p-6 space-y-6">
            {/* Personal Information */}
            <div className="space-y-4">
              <h2 className="text-xl font-semibold">Personal Information</h2>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <RequiredLabel htmlFor="firstName">First Name</RequiredLabel>
                  {isPending ? (
                    <Skeleton className="w-full h-9" />
                  ) : (
                    <Input
                      id="firstName"
                      name="firstName"
                      required
                      defaultValue={authenticatedUser?.first_name}
                    />
                  )}
                </div>
                <div className="space-y-2">
                  <Label htmlFor="middleInitial">Middle Initial</Label>
                  {isPending ? (
                    <Skeleton className="w-full h-9" />
                  ) : (
                    <Input
                      id="middleInitial"
                      name="middleInitial"
                      maxLength={1}
                      defaultValue={authenticatedUser?.middle_initial}
                    />
                  )}
                </div>
                <div className="space-y-2">
                  <RequiredLabel htmlFor="lastName">Last Name</RequiredLabel>
                  {isPending ? (
                    <Skeleton className="w-full h-9" />
                  ) : (
                    <Input
                      id="lastName"
                      name="lastName"
                      required
                      defaultValue={authenticatedUser?.last_name}
                    />
                  )}
                </div>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <RequiredLabel htmlFor="username">Username</RequiredLabel>
                  {isPending ? (
                    <Skeleton className="w-full h-9" />
                  ) : (
                    <Input
                      id="username"
                      name="username"
                      required
                      defaultValue={authenticatedUser?.users?.username}
                    />
                  )}
                </div>
                <div className="space-y-2">
                  <RequiredLabel htmlFor="email">Email Address</RequiredLabel>
                  {isPending ? (
                    <Skeleton className="w-full h-9" />
                  ) : (
                    <Input
                      id="email"
                      name="email"
                      type="email"
                      required
                      defaultValue={authenticatedUser?.users?.email}
                    />
                  )}
                </div>
              </div>
            </div>

            {/* Contact Information */}
            <div className="space-y-4">
              <h2 className="text-xl font-semibold">Contact Information</h2>
              <ContactInformation
                isLoading={isPending}
                user={authenticatedUser}
              />
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <RequiredLabel htmlFor="address">Address</RequiredLabel>
                  {isPending ? (
                    <Skeleton className="w-full h-9" />
                  ) : (
                    <Input
                      id="address"
                      name="address"
                      defaultValue={authenticatedUser?.address}
                      required
                    />
                  )}
                </div>
                <div className="space-y-2">
                  <Label htmlFor="secondaryAddress">Secondary Address</Label>
                  {isPending ? (
                    <Skeleton className="w-full h-9" />
                  ) : (
                    <Input
                      id="secondaryAddress"
                      name="secondaryAddress"
                      defaultValue={authenticatedUser?.secondary_address}
                    />
                  )}
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <LabelComponent htmlFor="city">City</LabelComponent>
                  {isPending ? (
                    <Skeleton className="w-full h-9" />
                  ) : (
                    <Input
                      id="city"
                      name="city"
                      defaultValue={authenticatedUser?.city}
                      required={!isStaffAndAdmin}
                    />
                  )}
                </div>
                <div className="space-y-2">
                  <LabelComponent htmlFor="state">State</LabelComponent>
                  {isPending ? (
                    <Skeleton className="w-full h-9" />
                  ) : (
                    <Input
                      id="state"
                      name="state"
                      defaultValue={authenticatedUser?.state}
                      required={!isStaffAndAdmin}
                    />
                  )}
                </div>
                <div className="space-y-2">
                  <LabelComponent htmlFor="zipCode">Zip Code</LabelComponent>
                  {isPending ? (
                    <Skeleton className="w-full h-9" />
                  ) : (
                    <Input
                      id="zipCode"
                      name="zipCode"
                      defaultValue={authenticatedUser?.zip_code}
                      required={!isStaffAndAdmin}
                    />
                  )}
                </div>
              </div>
            </div>
            {/* Password Change */}
            <div className="space-y-4">
              <h2 className="text-xl font-semibold">Change Password</h2>
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="newPassword">New Password</Label>
                    {isPending ? (
                      <Skeleton className="w-full h-9" />
                    ) : (
                      <div className="relative">
                        <Input
                          id="newPassword"
                          name="newPassword"
                          type={showPassword.new ? "text" : "password"}
                          onChange={(e) => {
                            const confirmPassword =
                              e.currentTarget.form?.querySelector<HTMLInputElement>(
                                '[name="confirmPassword"]'
                              );
                            if (confirmPassword?.value) {
                              setPasswordError(
                                e.target.value === confirmPassword.value
                                  ? ""
                                  : "Passwords do not match"
                              );
                            }
                          }}
                        />
                        <button
                          type="button"
                          onClick={() => togglePasswordVisibility("new")}
                          className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-500 hover:text-gray-700"
                          tabIndex={-1}
                        >
                          {showPassword.new ? (
                            <EyeOff className="h-4 w-4" />
                          ) : (
                            <Eye className="h-4 w-4" />
                          )}
                        </button>
                      </div>
                    )}
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="confirmNewPassword">
                      Confirm New Password
                    </Label>
                    {isPending ? (
                      <Skeleton className="w-full h-9" />
                    ) : (
                      <div className="relative">
                        <Input
                          id="confirmNewPassword"
                          name="confirmNewPassword"
                          type={showPassword.confirm ? "text" : "password"}
                          onChange={(e) => {
                            const newPassword =
                              e.currentTarget.form?.querySelector<HTMLInputElement>(
                                '[name="newPassword"]'
                              );
                            if (newPassword?.value) {
                              setPasswordError(
                                e.target.value === newPassword.value
                                  ? ""
                                  : "Passwords do not match"
                              );
                            }
                          }}
                        />
                        <button
                          type="button"
                          onClick={() => togglePasswordVisibility("confirm")}
                          className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-500 hover:text-gray-700"
                          tabIndex={-1}
                        >
                          {showPassword.confirm ? (
                            <EyeOff className="h-4 w-4" />
                          ) : (
                            <Eye className="h-4 w-4" />
                          )}
                        </button>
                      </div>
                    )}
                    {passwordError && (
                      <p className="text-sm text-red-500 col-span-2">
                        {passwordError}
                      </p>
                    )}
                  </div>
                </div>
              </div>
            </div>
            <div className="flex justify-end">
              <CustomButton type="submit" disabled={isPending}>
                Save Changes
              </CustomButton>
            </div>
          </Card>
        </form>
      </div>
    </React.Fragment>
  );
}
