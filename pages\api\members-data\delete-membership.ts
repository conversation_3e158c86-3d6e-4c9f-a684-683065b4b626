import { NextApiRequest, NextApiResponse } from "next";
import { supabaseClient } from "utils/supabase/client";

export default async function DELETE(
  req: NextApiRequest,
  res: NextApiResponse
) {
  try {
    const { membershipId, paymentId, userId } = req.body;

    const { data: userData, error: userError } = await supabaseClient
      .from("users")
      .select("first_name, last_name, role")
      .eq("user_auth_id", userId)
      .single();

    if (userError) {
      return res.status(404).json({
        message: "User not found",
      });
    }

    if (!userData || !["staff", "admin"].includes(userData.role)) {
      return res.status(403).json({
        message: "Unauthorized. Only staff and admin can create memberships.",
      });
    }

    if (!membershipId || !paymentId) {
      return res
        .status(400)
        .json({ message: "Membership id and payment id is required" });
    }

    const { error: paymentError } = await supabaseClient
      .from("payments")
      .delete()
      .eq("id", paymentId);

    if (paymentError) {
      return res.status(500).json({
        message: "Failed to delete the payment",
        error: paymentError.message,
      });
    }

    const { error: membershipError } = await supabaseClient
      .from("memberships")
      .delete()
      .eq("id", membershipId);

    if (membershipError) {
      return res.status(500).json({
        message: "Failed to delete the membership",
        error: membershipError.message,
      });
    }

    return res.status(200).json({ message: "Membership deleted successfully" });
  } catch (error) {
    return res
      .status(500)
      .json({ message: "Internal Server Error", error: error.message });
  }
}
