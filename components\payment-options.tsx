import { Check, CreditCard, BanknoteIcon } from "lucide-react";

import { But<PERSON> } from "@stackshift-ui/button";
import {
  Card,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";

export default function PaymentOptions({ paymentOption, setPaymentOption }) {
  const handlePaymentSelect = (paymentMethod: string) => {
    setPaymentOption(paymentMethod);
  };

  return (
    <section className="py-12 bg-gray-50">
      <div className="w-full px-4 md:px-6">
        <div className="text-center space-y-4 mb-8">
          <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl">
            Select a Payment Option
          </h2>
          <p className="mx-auto max-w-[700px] text-gray-500 md:text-xl/relaxed">
            Choose your preferred payment method to complete your transaction
          </p>
        </div>
        <div className="grid md:grid-cols-3 gap-6 max-w-5xl mx-auto">
          {/* Check Payment Option */}
          <Card className="flex flex-col">
            <CardHeader className="flex-1">
              <div className="flex items-center justify-center h-12 w-12 rounded-lg bg-primary/10 mb-4">
                <Check className="h-6 w-6 text-primary" />
              </div>
              <CardTitle>Check</CardTitle>
              <CardDescription>
                Pay with a personal or business check
              </CardDescription>
            </CardHeader>
            <CardFooter>
              <Button
                className="w-full"
                variant={paymentOption === "check" ? "solid" : "ghost"}
                onClick={() => handlePaymentSelect("check")}
              >
                {paymentOption === "check"
                  ? "Selected Payment Option"
                  : "Pay with Check"}
              </Button>
            </CardFooter>
          </Card>

          <Card className="flex flex-col">
            <CardHeader className="flex-1">
              <div className="flex items-center justify-center h-12 w-12 rounded-lg bg-primary/10 mb-4">
                <BanknoteIcon className="h-6 w-6 text-primary" />
              </div>
              <CardTitle>Cash</CardTitle>
              <CardDescription>Pay with cash in person</CardDescription>
            </CardHeader>
            <CardFooter>
              <Button
                className="w-full"
                variant={paymentOption === "cash" ? "solid" : "ghost"}
                onClick={() => handlePaymentSelect("cash")}
              >
                {paymentOption === "cash"
                  ? "Selected Payment Option"
                  : "Pay with Cash"}
              </Button>
            </CardFooter>
          </Card>

          <Card className="flex flex-col relative overflow-hidden">
            <div className="absolute inset-0 bg-primary/5" />
            <CardHeader className="flex-1 relative">
              <div className="flex items-center justify-center h-12 w-12 rounded-lg bg-primary mb-4">
                <CreditCard className="h-6 w-6 text-primary-foreground" />
              </div>
              <CardTitle>Card (Stripe)</CardTitle>
              <CardDescription>Pay with credit or debit card</CardDescription>
            </CardHeader>
            <CardFooter className="relative">
              <Button
                className="w-full"
                variant={paymentOption === "card" ? "solid" : "ghost"}
                onClick={() => handlePaymentSelect("card")}
              >
                {paymentOption === "card"
                  ? "Selected Payment Option"
                  : "Pay with Card"}
              </Button>
            </CardFooter>
          </Card>
        </div>
      </div>
    </section>
  );
}
