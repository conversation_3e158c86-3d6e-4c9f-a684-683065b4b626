import { Card } from "@stackshift-ui/card";
import { Container } from "@stackshift-ui/container";
import { Flex } from "@stackshift-ui/flex";
import { Heading } from "@stackshift-ui/heading";
import { Image } from "@stackshift-ui/image";
import { Link } from "@stackshift-ui/link";
import { Section } from "@stackshift-ui/section";
import { Text } from "@stackshift-ui/text";
import React from "react";
import { ContactProps } from ".";
import { PortableText } from "@portabletext/react";
import { MyPortableTextComponents } from "types";

// block styling as props to `components` of the PortableText component
export const textComponentBlockStyling: MyPortableTextComponents = {
  block: {
    h1: ({ children }) => (
      <Heading
        fontSize="3xl"
        weight="bold"
        className="mb-8 leading-normal text-black dark:text-white"
      >
        {children}
      </Heading>
    ),
    h2: ({ children }) => (
      <Heading
        type="h2"
        weight="bold"
        fontSize="2xl"
        className="mb-8 text-black dark:text-white"
      >
        {children}
      </Heading>
    ),
    h3: ({ children }) => (
      <Heading
        type="h3"
        fontSize="xl"
        weight="bold"
        className="mb-8 leading-normal text-black dark:text-white"
      >
        {children}
      </Heading>
    ),
    h4: ({ children }) => (
      <Heading
        type="h4"
        weight="bold"
        fontSize="lg"
        className="mb-8 leading-normal text-black dark:text-white"
      >
        {children}
      </Heading>
    ),
    normal: ({ children }) => (
      <p className="leading-relaxed text-white text-base">{children}</p>
    ),
    blockquote: ({ children }) => (
      <blockquote className="mb-6 italic leading-loose text-gray-500 px-14">
        - {children}
      </blockquote>
    ),
  },
  code: ({ value }) => (
    <pre data-language={value.language}>
      <code>{value.code}</code>
    </pre>
  ),
  list: {
    bullet: ({ children }) => (
      <ul className="flex flex-col pl-10 mb-8 space-y-4 leading-relaxed list-disc">
        {children}
      </ul>
    ),
    number: ({ children }) => (
      <ol className="flex flex-col pl-10 mb-8 space-y-4 leading-relaxed list-decimal">
        {children}
      </ol>
    ),
  },
  listItem: {
    bullet: ({ children }) => <li className="leading-relaxed">{children}</li>,
    number: ({ children }) => <li className="leading-relaxed">{children}</li>,
  },
  marks: {
    strong: ({ children }) => <strong>{children}</strong>,
    em: ({ children }) => <em>{children}</em>,
    code: ({ children }) => <code>{children}</code>,
    link: ({ children, value }) => (
      <a
        className="hover:text-primary-foreground text-primary"
        href={value.href}
        target="_blank"
        rel="noopener noreferrer"
      >
        {children}
      </a>
    ),
  },
  types: {
    addImage: ({ value }) => (
      <Image
        className="w-full h-full mb-5"
        width={500}
        height={500}
        sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
        src={urlFor(value?.image)}
        alt={value?.alt ?? value?.image?.asset?._ref}
      />
    ),
  },
};

export default function Contact_C({
  title,
  officeInformation,
  firstColumn,
  contactNumber,
  blockText,
}: ContactProps) {
  return (
    <Section className="pt-4 pb-40 bg-cfc-primary">
      <Container maxWidth={1280}>
        <ContactHeader title={title} />
        <div className="grid items-start md:grid-cols-2 lg:grid-cols-3 gap-4 max-w-5xl mx-auto">
          <div className="w-full px-4 text-center ">
            <Heading
              type="h4"
              fontSize="lg"
              className="mb-4 uppercase text-white"
            >
              Address
            </Heading>

            <div className="text-white">
              <PortableText
                value={firstColumn}
                components={textComponentBlockStyling}
                onMissingComponent={false} // Disabling warnings / handling unknown types
              />
            </div>
          </div>

          <div className="w-full px-4 text-center">
            <Heading
              type="h4"
              fontSize="lg"
              className="mb-4 uppercase text-white"
            >
              Hours
            </Heading>

            {blockText && (
              <div className="w-full text-center">
                <PortableText
                  value={blockText}
                  components={textComponentBlockStyling}
                  onMissingComponent={false} // Disabling warnings / handling unknown types
                />
              </div>
            )}
          </div>

          <div className="w-full px-4 text-center">
            <Heading
              type="h4"
              fontSize="lg"
              className="mb-4 uppercase text-white"
            >
              Phone
            </Heading>
            <Text muted className="text-white">
              {contactNumber ? (
                <Link href={`tel:${contactNumber}`}>{contactNumber}</Link>
              ) : null}
            </Text>
          </div>
        </div>
      </Container>
    </Section>
  );
}

function ContactHeader({ title }: { title?: string }) {
  return (
    <div className="mb-8 text-center">
      {title ? (
        <Heading
          type="h2"
          fontSize="4xl"
          weight="thin"
          className="text-center text-white"
        >
          {title}
        </Heading>
      ) : null}
    </div>
  );
}

export { Contact_C };
