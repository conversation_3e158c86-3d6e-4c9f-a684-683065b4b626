import { rootSchema } from "@webriq-pagebuilder/sanity-plugin-schema-default";
import { MdLineWeight } from "react-icons/md";

// Images
import variantAImage from "./images/variant_a.jpg";
import variantBImage from "./images/variant_b.jpg";
import variantCImage from "./images/variant_c.jpg";
import variantDImage from "./images/variant_d.png";
import variantEImage from "./images/variant_e.png";
import variantFImage from "./images/variant_f.png";
import variantGImage from "./images/variant_g.png";
import variantHImage from "./images/variant_h.png";
import variantIImage from "./images/variant_i.png";
import variantJImage from "./images/variant_j.png";
import variantKImage from "./images/variant_k.png";
import variantLImage from "./images/variant_l.png";
import variantMImage from "./images/variant_m.png";
import variantNImage from "./images/variant_n.png";
import variantOImage from "./images/variant_o.png";

import { textComponentSchema } from "./schema";

export const variantsList = [
  {
    title: "Variant A",
    description: "Add a single-column text component.",
    value: "variant_a",
    image: variantAImage.src,
  },
  {
    title: "Variant B",
    value: "variant_b",
    description: "Add a text component with 2 columns.",
    image: variantBImage.src,
  },
  {
    title: "Variant C",
    value: "variant_c",
    description: "Add a text component with 3 columns.",
    image: variantCImage.src,
  },
  {
    title: "Variant D",
    value: "variant_d",
    description: "Add a text component with 3 columns.",
    image: variantDImage.src,
  },
  {
    title: "Variant E",
    value: "variant_e",
    description: "Add a text component with 3 columns.",
    image: variantEImage.src,
  },
  {
    title: "Variant F",
    value: "variant_f",
    description: "Add a text component with 3 columns.",
    image: variantFImage.src,
  },
  {
    title: "Variant G",
    value: "variant_g",
    description: "Add a text component with 3 columns.",
    image: variantGImage.src,
  },
  {
    title: "Variant H",
    value: "variant_h",
    description: "Add a text component with 3 columns.",
    image: variantHImage.src,
  },
  {
    title: "Variant I",
    value: "variant_i",
    description: "Add a text component with 3 columns.",
    image: variantIImage.src,
  },
  {
    title: "Variant J",
    value: "variant_j",
    description: "Add a text component with 3 columns.",
    image: variantJImage.src,
  },
  {
    title: "Variant K",
    value: "variant_k",
    description: "Add a text component with 3 columns.",
    image: variantKImage.src,

  },
  {
    title: "Variant L",
    value: "variant_l",
    description: "Add a text component with 3 columns.",
    image: variantLImage.src,

  },
  {
    title: "Variant M",
    value: "variant_m",
    description: "Add a text component with 3 columns.",
    image: variantMImage.src,

  },
  {
    title: "Variant N",
    value: "variant_n",
    description: "Add a text component with 3 columns.",
    image: variantNImage.src,

  },
  {
    title: "Variant O",
    value: "variant_o",
    description: "Add a text component with 3 columns.",
    image: variantOImage.src,

  },
];

export default rootSchema(
  "textComponent",
  "Text Component",
  MdLineWeight,
  variantsList,
  textComponentSchema
);
