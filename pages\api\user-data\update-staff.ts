import { checkEmailExist, checkUsernameExist } from "helper/supabase";
import { supabaseAdmin } from "utils/supabase/admin";
import { supabaseClient } from "utils/supabase/client";

export default async function handler(req, res) {
  if (req.method !== "PUT") {
    return res.status(405).json({ message: "Method Not Allowed" });
  }

  try {
    const {
      userId,
      memberId,
      firstName,
      middleInitial,
      lastName,
      username,
      email,
      newPassword,
      confirmNewPassword,
      contactInfo,
      address,
      secondaryAddress,
      city,
      state,
      zipCode,
      currentEmail,
      currentUsername,
      userAuthId,
      role,
      currentRole,
    } = req.body;

    // Validate required fields
    if (
      !userId ||
      !memberId ||
      !firstName ||
      !lastName ||
      !username ||
      !email ||
      !contactInfo ||
      !address ||
      (!["admin", "staff", "volunteer"].includes(role) &&
        (!city || !state || !zipCode)) ||
      !role
    ) {
      return res.status(400).json({ message: "Missing required fields" });
    }

    // Check for email and username conflicts
    if (email !== currentEmail) {
      const emailError = await checkEmailExist(email);
      if (emailError) {
        return res
          .status(400)
          .json({ message: "This email is already in use" });
      }
    }

    if (username !== currentUsername) {
      const usernameError = await checkUsernameExist(username);
      if (usernameError) {
        return res
          .status(400)
          .json({ message: "This username is already taken" });
      }
    }

    // Update password if provided
    if (newPassword && confirmNewPassword) {
      if (newPassword !== confirmNewPassword) {
        return res.status(400).json({ message: "Passwords do not match" });
      }

      const { error: passwordUpdateError } = await supabaseAdmin.updateUserById(
        userAuthId,
        {
          password: newPassword,
        }
      );

      if (passwordUpdateError) {
        throw new Error(
          `Failed to update password: ${passwordUpdateError.message}`
        );
      }
    }

    // Update user metadata (username and email)
    const metadataUpdates: any = {};
    if (username !== currentUsername)
      metadataUpdates.user_metadata = { username };
    if (email !== currentEmail) metadataUpdates.email = email;
    if (role !== currentRole) metadataUpdates.role = role;
    if (Object.keys(metadataUpdates).length > 0) {
      const { error: metadataUpdateError } = await supabaseAdmin.updateUserById(
        userAuthId,
        metadataUpdates
      );
      if (metadataUpdateError) {
        return res
          .status(400)
          .json({ message: "Failed to update user metadata" });
      }
    }

    // Update user information
    const userUpdateData = {
      first_name: firstName,
      last_name: lastName,
      email,
      username,
      role,
    };

    const { error: updateUserError } = await supabaseClient
      .from("users")
      .update(userUpdateData)
      .eq("user_auth_id", userAuthId);

    if (updateUserError) {
      return res.status(400).json({ message: "Failed to update user" });
    }

    // Update member details
    const memberUpdateData = {
      address,
      secondary_address: secondaryAddress,
      contact_info: contactInfo,
      first_name: firstName,
      last_name: lastName,
      middle_initial: middleInitial,
      city,
      state,
      zip_code: zipCode,
      updated_at: new Date().toISOString(),
    };

    const { error: updateMemberError } = await supabaseClient
      .from("members")
      .update(memberUpdateData)
      .eq("id", memberId);

    if (updateMemberError) {
      return res.status(400).json({ message: "Failed to update member" });
    }

    return res.status(200).json({ message: "Staff updated successfully" });
  } catch (error) {
    console.error("Error updating staff:", error);
    return res.status(500).json({
      message: "Internal server error",
      error: error.message || "Unknown error occurred",
    });
  }
}
