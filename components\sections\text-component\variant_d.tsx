import { PortableText } from "@portabletext/react";
import { Container } from "@stackshift-ui/container";
import { Flex } from "@stackshift-ui/flex";

import { Heading } from "@stackshift-ui/heading";
import { Section } from "@stackshift-ui/section";
import { TextComponentProps } from ".";
import { usePathname } from "next/navigation";
import { MyPortableTextComponents } from "types";

export default function TextComponent_D({
  heading,
  firstColumn,
}: TextComponentProps) {
  const pathname = usePathname();

  const textComponentBlockStyling: MyPortableTextComponents = {
    block: {
      h1: ({ children }) => (
        <Heading
          fontSize="3xl"
          weight="bold"
          className="mb-8 leading-normal text-black dark:text-white"
        >
          {children}
        </Heading>
      ),
      h2: ({ children }) => (
        <Heading
          type="h2"
          weight="thin"
          fontSize="3xl"
          className="mb-6 md:text-4xl text-white text-center"
        >
          {children}
        </Heading>
      ),
      h3: ({ children }) => (
        <Heading
          type="h3"
          fontSize="xl"
          weight="bold"
          className="mb-8 leading-normal text-black dark:text-white"
        >
          {children}
        </Heading>
      ),
      h4: ({ children }) => (
        <Heading
          type="h4"
          weight="bold"
          fontSize="lg"
          className="mb-8 leading-normal text-black dark:text-white"
        >
          {children}
        </Heading>
      ),
      normal: ({ children }) => (
        <p
          className={`mb-8 ${
            pathname.includes("volunteer")
              ? "md:!text-3xl text-2xl font-light text-center"
              : pathname.includes("join-today")
              ? "md:!text-2xl text-2xl font-light text-left tracking-wide"
              : "!text-lg text-center"
          }  md:leading-relaxed leading-tight text-white`}
        >
          {children}
        </p>
      ),
      blockquote: ({ children }) => (
        <blockquote className="mb-6 italic leading-loose text-gray-500 px-14">
          - {children}
        </blockquote>
      ),
    },
    code: ({ value }) => (
      <pre data-language={value.language}>
        <code>{value.code}</code>
      </pre>
    ),
    list: {
      bullet: ({ children }) => (
        <ul className="flex flex-col pl-10 mb-8 space-y-4 leading-relaxed list-disc">
          {children}
        </ul>
      ),
      number: ({ children }) => (
        <ol className="flex flex-col pl-10 mb-8 space-y-4 leading-relaxed list-decimal">
          {children}
        </ol>
      ),
    },
    listItem: {
      bullet: ({ children }) => <li className="leading-relaxed">{children}</li>,
      number: ({ children }) => <li className="leading-relaxed">{children}</li>,
    },
    marks: {
      strong: ({ children }) => <strong>{children}</strong>,
      em: ({ children }) => <em>{children}</em>,
      code: ({ children }) => <code>{children}</code>,
      link: ({ children, value }) => (
        <a
          className="hover:text-primary-foreground text-primary"
          href={value.href}
          target="_blank"
          rel="noopener noreferrer"
        >
          {children}
        </a>
      ),
    },
  };

  return (
    <Section className="lg:py-44 py-24 bg-cfc-secondary relative overflow-hidden">
      <svg
        xmlns="http://www.w3.org/2000/svg"
        viewBox="0 0 1000 100"
        preserveAspectRatio="none"
        className="absolute -top-1 w-full left-0 wave-svg h-16"
      >
        <path
          className="elementor-shape-fill"
          d="M1000,4.3V0H0v4.3C0.9,23.1,126.7,99.2,500,100S1000,22.7,1000,4.3z"
        ></path>
      </svg>

      <div className="md:px-7 px-0">
        {heading && (
          <Heading
            type="h2"
            weight="thin"
            className={`${
              pathname.includes("join-today") ? "!text-[40px]" : "!text-4xl"
            } mb-5 text-center text-white leading-none`}
          >
            {heading}
          </Heading>
        )}
        
        <Flex wrap justify="center" className="mx-auto">
          {firstColumn && (
            <div
              className={`mb-2 ${
                pathname.includes("volunteer")
                  ? "!max-w-3xl"
                  : pathname.includes("join-today")
                  ? "!max-w-4xl"
                  : "w-full"
              }`}
            >
              <PortableText
                value={firstColumn}
                components={textComponentBlockStyling}
                onMissingComponent={false} // Disabling warnings / handling unknown types
              />
            </div>
          )}
        </Flex>
      </div>
      <svg
        xmlns="http://www.w3.org/2000/svg"
        viewBox="0 0 1000 100"
        preserveAspectRatio="none"
        className="absolute -bottom-1 w-full left-0 wave-svg-bottom wave-svg h-16"
      >
        <path
          className="elementor-shape-fill"
          d="M1000,4.3V0H0v4.3C0.9,23.1,126.7,99.2,500,100S1000,22.7,1000,4.3z"
        ></path>
      </svg>
    </Section>
  );
}

export { TextComponent_D };
