import { Button } from "@stackshift-ui/button";
import { Container } from "@stackshift-ui/container";
import { Flex } from "@stackshift-ui/flex";
import { Heading } from "@stackshift-ui/heading";
import { Image } from "@stackshift-ui/image";
import { Section } from "@stackshift-ui/section";
import { Text } from "@stackshift-ui/text";
import React from "react";
import { ButtonProps, HeaderProps } from ".";
import { urlFor } from "lib/sanity";

export default function Header_R({
  mainImage,
  title,
  description,
}: HeaderProps) {
  return (
    <Section className="md:py-6 py-6 !px-0">
      <Container maxWidth={1280}>
        <Flex className="w-full flex items-center max-w-6xl mx-auto justify-end flex-col-reverse md:flex-row">
          <div className="w-full basis-3/5 p-10">
            <TitleAndDescription title={title} description={description} />
          </div>

          <div className="relative w-full basis-2/5">
            <div
              className="relative w-full flex !self-end !justify-self-end"
              style={{
                backgroundImage: `url(${mainImage?.image})`,
                backgroundRepeat: "no-repeat",
                backgroundSize: "cover",
                backgroundPosition: "center",
              }}
            >
              <Image
                className="object-cover relative z-10"
                // src={`${mainImage?.image}`}
                src="/assets/elements/holder.png"
                width={600}
                height={300}
                alt={mainImage?.alt ?? "header-main-image"}
              />
            </div>
          </div>
        </Flex>
      </Container>
    </Section>
  );
}

function TitleAndDescription({
  title,
  description,
}: {
  title?: string;
  description?: string;
}) {
  return (
    <div className="flex items-start justify-start  flex-col">
      {title && (
        <Heading
          type="h2"
          weight="thin"
          className="mb-2 text-left !text-cfc-secondary text-6xl"
        >
          {title}
        </Heading>
      )}

      {description && (
        <Text muted className="my-2 !text-2xl md:!text-3xl font-thin">
          {description}
        </Text>
      )}
    </div>
  );
}

interface MainImageProps {
  mainImage?: {
    image?: string | any;
    alt?: string;
  };
}

function MainImage({ mainImage }: MainImageProps) {
  if (!mainImage?.image) return null;

  return (
    <div className="relative w-full max-w-md mx-auto">
      <Image
        className=" object-cover w-full lg:w-[500px] relative z-10 md:scale-110"
        src={`${mainImage.image}`}
        sizes="(min-width: 520px) 520px, 90vw"
        width={800}
        height={500}
        alt={mainImage.alt ?? "header-main-image"}
      />
      {/* <DecorativeImages /> */}
    </div>
  );
}

export { Header_R };
