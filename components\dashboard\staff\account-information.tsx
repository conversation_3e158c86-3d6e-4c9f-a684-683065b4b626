import { Skeleton } from "@/components/ui/skeleton";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import React, { useEffect, useState } from "react";
import { useSelectClient } from "context/useSelectClientContext";
import { useClientSearch } from "context/useClientSearchContext";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { housingTypes, languages } from "utils";
import { useHouseholdData } from "context/useHousehold";
import { Plus } from "lucide-react";
import { CustomButton } from "@/components/ui/custom-button";
import { useLanguageData } from "context/useLanguageData";
import axios from "axios";
import { useAlert } from "context/useAlert";
import { useAuthCheck } from "hooks/useAuthCheck";

export default function AccountInformation({ isLoading }) {
  const { selectedClient } = useSelectClient();
  const { isSearching } = useClientSearch();
  const { totalMemberIncome, memberCount } = useHouseholdData();
  const { languageData, fetchingLanguage } = useLanguageData();
  const { showAlert } = useAlert();
  const { token } = useAuthCheck();
  const [showCustomInput, setShowCustomInput] = useState(false);
  const [customLanguage, setCustomLanguage] = useState("");
  const [selectedLanguage, setSelectedLanguage] = useState(
    selectedClient?.additional_info?.preferred_language?.toLowerCase()
  );
  const [housingType, setHousingType] = useState(
    selectedClient?.additional_info?.housing_type?.toLowerCase()
  );
  const [submittingLanguage, setSubmittingLanguage] = useState(false);

  const isDisabled = isLoading || !selectedClient;

  useEffect(() => {
    if (selectedClient?.additional_info?.preferred_language) {
      setSelectedLanguage(
        selectedClient.additional_info.preferred_language.toLowerCase()
      );
    }

    if (selectedClient?.additional_info?.housing_type) {
      setHousingType(selectedClient.additional_info.housing_type.toLowerCase());
    }
  }, [selectedClient]);

  const handleLanguageChange = (value) => {
    if (value === "custom") {
      setShowCustomInput(true);
    } else {
      setShowCustomInput(false);
      setSelectedLanguage(value);
    }
  };

  const handleCustomLanguageSubmit = async () => {
    setSubmittingLanguage(true);
    try {
      if (customLanguage.trim()) {
        setSelectedLanguage(customLanguage.toLowerCase());
        const response = await axios.post("/api/admin/language", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
          language: customLanguage,
        });

        if (response.status === 200) {
          showAlert("Language added successfully", "success");
        }
      }
    } catch (error) {
      showAlert("Error adding language", "error");
    } finally {
      setSubmittingLanguage(false);
      setShowCustomInput(false);
      setCustomLanguage("");
    }
  };

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-3 gap-4">
        <div className="space-y-2">
          <Label htmlFor="firstName">First Name</Label>
          {isSearching ? (
            <Skeleton className="w-full h-9" />
          ) : (
            <Input
              id="firstName"
              name="firstName"
              disabled={isDisabled}
              defaultValue={selectedClient?.first_name}
            />
          )}
        </div>
        <div className="space-y-2">
          <Label htmlFor="middleInitial">Middle Initial</Label>
          {isSearching ? (
            <Skeleton className="w-full h-9" />
          ) : (
            <Input
              id="middleInitial"
              name="middleInitial"
              disabled={isDisabled}
              defaultValue={selectedClient?.middle_initial}
            />
          )}
        </div>
        <div className="space-y-2">
          <Label htmlFor="lastName">Last Name</Label>
          {isSearching ? (
            <Skeleton className="w-full h-9" />
          ) : (
            <Input
              id="lastName"
              name="lastName"
              disabled={isDisabled}
              defaultValue={selectedClient?.last_name}
            />
          )}
        </div>
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="address">Address 1</Label>
          {isSearching ? (
            <Skeleton className="w-full h-9" />
          ) : (
            <Input
              id="address"
              name="address"
              disabled={isDisabled}
              defaultValue={selectedClient?.address}
            />
          )}
        </div>
        <div className="space-y-2">
          <Label htmlFor="secondaryAddress">Address 2</Label>
          {isSearching ? (
            <Skeleton className="w-full h-9" />
          ) : (
            <Input
              id="secondaryAddress"
              name="secondaryAddress"
              disabled={isDisabled}
              defaultValue={selectedClient?.secondary_address}
            />
          )}
        </div>
      </div>

      <div className="grid grid-cols-4 gap-4">
        <div className="space-y-2">
          <Label htmlFor="city">City</Label>
          {isSearching ? (
            <Skeleton className="w-full h-9" />
          ) : (
            <Input
              id="city"
              name="city"
              disabled={isDisabled}
              defaultValue={selectedClient?.city}
            />
          )}
        </div>
        <div className="space-y-2">
          <Label htmlFor="state">State</Label>
          {isSearching ? (
            <Skeleton className="w-full h-9" />
          ) : (
            <Input
              id="state"
              name="state"
              disabled={isDisabled}
              defaultValue={selectedClient?.state}
            />
          )}
        </div>
        <div className="space-y-2">
          <Label htmlFor="zipCode">ZIP Code</Label>
          {isSearching ? (
            <Skeleton className="w-full h-9" />
          ) : (
            <Input
              id="zipCode"
              name="zipCode"
              disabled={isDisabled}
              defaultValue={selectedClient?.zip_code}
            />
          )}
        </div>
        <div className="space-y-2">
          <Label htmlFor="housingType">Housing Type</Label>
          {isSearching ? (
            <Skeleton className="w-full h-9" />
          ) : (
            <Select
              value={housingType}
              onValueChange={(value) => setHousingType(value)}
              name="housingType"
              disabled={isDisabled}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select housing type" />
              </SelectTrigger>
              <SelectContent>
                {housingTypes.map((type) => (
                  <SelectItem key={type} value={type.toLowerCase()}>
                    {type}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          )}
        </div>
      </div>

      <div className="grid grid-cols-3 gap-4">
        <div className="space-y-2">
          <Label htmlFor="preferredLanguage">Preferred Language</Label>
          {isSearching || fetchingLanguage ? (
            <Skeleton className="w-full h-9" />
          ) : (
            <>
              <Select
                value={selectedLanguage}
                onValueChange={handleLanguageChange}
                name="preferredLanguage"
                disabled={isDisabled || showCustomInput}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select preferred language" />
                </SelectTrigger>
                <SelectContent>
                  {languageData?.map((language) => (
                    <SelectItem
                      key={language.id}
                      defaultValue={language.name.toLowerCase()}
                      value={language.name.toLowerCase()}
                    >
                      {language.name}
                    </SelectItem>
                  ))}
                  <SelectItem value="custom">
                    <div className="flex items-center gap-1 text-blue-500 hover:text-blue-700 text-sm">
                      <Plus className="h-4 w-4" />
                      Custom
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>

              {showCustomInput && (
                <div className="mt-2 flex gap-2">
                  <Input
                    placeholder="Enter custom language"
                    value={customLanguage}
                    onChange={(e) => setCustomLanguage(e.target.value)}
                    className="flex-1"
                    autoFocus
                  />
                  <CustomButton
                    type="button"
                    onClick={handleCustomLanguageSubmit}
                    disabled={!customLanguage.trim()}
                  >
                    {submittingLanguage ? "Adding..." : "Add"}
                  </CustomButton>
                  <CustomButton
                    type="button"
                    variant="outline"
                    onClick={() => setShowCustomInput(false)}
                  >
                    Cancel
                  </CustomButton>
                </div>
              )}
            </>
          )}
        </div>

        <div className="space-y-2">
          <Label htmlFor="householdSize">Household Size</Label>
          {isSearching ? (
            <Skeleton className="w-full h-9" />
          ) : (
            <Input
              id="householdSize"
              name="householdSize"
              type="number"
              min="1"
              step="1"
              placeholder="1"
              required
              disabled={true}
              value={memberCount}
            />
          )}
        </div>
        <div className="space-y-2">
          <Label htmlFor="annualIncome">Household Annual Income</Label>
          <div className="relative">
            {isSearching ? (
              <Skeleton className="w-full h-9" />
            ) : (
              <React.Fragment>
                <span className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-500">
                  $
                </span>
                <Input
                  id="annualIncome"
                  name="annualIncome"
                  type="number"
                  min="0"
                  step="1"
                  placeholder="0.00"
                  className="pl-7"
                  required
                  disabled={true}
                  value={
                    Number(totalMemberIncome) !== 0
                      ? Number(totalMemberIncome)
                      : selectedClient?.annual_income
                  }
                />
              </React.Fragment>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
