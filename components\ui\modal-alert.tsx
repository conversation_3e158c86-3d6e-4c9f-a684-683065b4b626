import React from "react";
import { <PERSON><PERSON><PERSON>riangle, <PERSON>ert<PERSON>ircle, CheckCircle, Info } from "lucide-react";
import { CustomButton } from "./custom-button";
import { Dialog, DialogContent } from "./dialog";
import { cn } from "utils/cn";

export interface ModalAlertProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  message: string;
  type?: "warning" | "error" | "success" | "info";
  confirmText?: string;
  showCancel?: boolean;
  cancelText?: string;
  onConfirm?: () => void;
  onCancel?: () => void;
}

const ModalAlert: React.FC<ModalAlertProps> = ({
  isOpen,
  onClose,
  title,
  message,
  type = "warning",
  confirmText = "I Understand",
  showCancel = false,
  cancelText = "Cancel",
  onConfirm,
  onCancel,
}) => {
  if (!isOpen) return null;

  const getTypeStyles = () => {
    switch (type) {
      case "error":
        return {
          border: "border-red-200",
          icon: Al<PERSON><PERSON>riangle,
          iconColor: "text-red-600",
          titleColor: "text-red-800",
          buttonColor: "bg-red-600 hover:bg-red-700",
        };
      case "success":
        return {
          border: "border-green-200",
          icon: CheckCircle,
          iconColor: "text-green-600",
          titleColor: "text-green-800",
          buttonColor: "bg-green-600 hover:bg-green-700",
        };
      case "info":
        return {
          border: "border-blue-200",
          icon: Info,
          iconColor: "text-blue-600",
          titleColor: "text-blue-800",
          buttonColor: "bg-blue-600 hover:bg-blue-700",
        };
      default: // warning
        return {
          border: "border-yellow-200",
          icon: AlertCircle,
          iconColor: "text-yellow-600",
          titleColor: "text-yellow-800",
          buttonColor: "bg-yellow-600 hover:bg-yellow-700",
        };
    }
  };

  const styles = getTypeStyles();
  const IconComponent = styles.icon;

  const handleConfirm = () => {
    if (onConfirm) {
      onConfirm();
    }
    onClose();
  };

  const handleCancel = () => {
    if (onCancel) {
      onCancel();
    }
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent
        className={cn(
          "max-w-md w-full h-fit border-2 bg-white p-6 rounded-lg shadow-xl",
          styles.border
        )}
      >
        <div className="w-full h-full">
          <div className="flex items-center space-x-3 mb-4">
            <IconComponent
              className={`h-8 w-8 ${styles.iconColor} flex-shrink-0`}
            />
            <h3 className={`text-lg font-bold ${styles.titleColor}`}>
              {title}
            </h3>
          </div>

          <div className="mb-6">
            <p className="text-gray-700 text-sm leading-relaxed">{message}</p>
          </div>

          <div className="flex justify-end space-x-3">
            {showCancel && (
              <CustomButton
                variant="outline"
                onClick={handleCancel}
                className="px-4 py-2"
              >
                {cancelText}
              </CustomButton>
            )}
            <CustomButton
              onClick={handleConfirm}
              className={`${styles.buttonColor} text-white px-6 py-2`}
            >
              {confirmText}
            </CustomButton>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default ModalAlert;
