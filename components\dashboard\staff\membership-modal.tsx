import React, { FC, useState, useEffect } from "react";
import { But<PERSON> } from "@stackshift-ui/button";
import { Input } from "../../ui/input";
import { Label } from "../../ui/label";
import { useAlert } from "context/useAlert";
import { useAuthCheck } from "hooks/useAuthCheck";
import { useSelectClient } from "context/useSelectClientContext";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../../ui/select";
import {
  calculateMembershipPrice,
  calculatePoints,
} from "utils/membership-calculator";
import { useMemberData } from "context/useMemberData";

interface MembershipModalProps {
  isOpen: boolean;
  onClose: () => void;
  setIsModalOpen: React.Dispatch<React.SetStateAction<boolean>>;
}

const MembershipModal: FC<MembershipModalProps> = async ({
  isOpen,
  onClose,
  setIsModalOpen,
}) => {
  if (!isOpen) return null;
  const [isPending, setIsPending] = useState(false);
  const [totalPoints, setTotalPoints] = useState(0);
  const [selectedAmount, setSelectedAmount] = useState<string>("11");
  const { showAlert } = useAlert();
  const { user, token } = useAuthCheck();
  const { selectedClient } = useSelectClient();
  const { refreshData } = useMemberData();

  useEffect(() => {
    if (selectedAmount) {
      const calculatePointsAsync = async () => {
        const points = await calculatePoints(Number(selectedAmount));
        setTotalPoints(points);
      };
      calculatePointsAsync();
    }
  }, [selectedAmount]);

  const handleAddMembership = async (
    event: React.FormEvent<HTMLFormElement>
  ) => {
    event.preventDefault();
    setIsPending(true);
    const formData = new FormData(event.currentTarget);
    const data = Object.fromEntries(formData.entries());

    try {
      const response = await fetch(`/api/members-data/add-membership`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({
          ...data,
          total_points: totalPoints,
          member_id: selectedClient?.id,
          created_by: user?.id,
        }),
      });

      const responseData = await response.json();
      if (!response.ok) {
        showAlert(responseData?.message, "error");
        throw new Error(responseData.message || "An error occurred");
      }
      showAlert("Membership added successfully!", "success");
      setIsModalOpen(false);
    } catch (error) {
      showAlert("Failed to add membership. Please try again.", "error");
    } finally {
      setIsPending(false);
      refreshData();
    }
  };

  const today = new Date().toISOString().split("T")[0];
  const nextMonth = new Date();
  nextMonth.setMonth(nextMonth.getMonth() + 1);
  const oneMonthFromToday = nextMonth.toISOString().split("T")[0];

  const availablePrices = await calculateMembershipPrice(
    selectedClient?.household_size || 1,
    selectedClient?.annual_income || 0
  );

  if (availablePrices.length === 0) {
    return (
      <div className="fixed inset-0 z-10 bg-black bg-opacity-50 flex justify-center items-center">
        <div className="bg-white p-6 rounded shadow-lg w-[500px]">
          <h4 className="text-lg font-semibold mb-4">
            Membership Not Available
          </h4>
          <p className="text-gray-600 mb-4">
            This client does not qualify for membership based on their household
            size and income.
          </p>
          <div className="flex justify-end">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              className="px-4 py-2 rounded border"
            >
              Close
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 z-10 bg-black bg-opacity-50 flex justify-center items-center">
      <div className="bg-white p-6 rounded shadow-lg w-[500px]">
        <h4 className="text-lg font-semibold mb-4">Add Membership</h4>
        <form onSubmit={handleAddMembership}>
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="start_date">Start Date</Label>
                <Input
                  type="date"
                  id="start_date"
                  name="start_date"
                  required
                  className="w-full"
                  defaultValue={today}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="end_date">End Date</Label>
                <Input
                  type="date"
                  id="end_date"
                  name="end_date"
                  required
                  className="w-full"
                  defaultValue={oneMonthFromToday}
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="payment_method">Payment Method</Label>
              <Select name="payment_method" required defaultValue="cash">
                <SelectTrigger>
                  <SelectValue placeholder="Select payment method" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="cash">Cash</SelectItem>
                  <SelectItem value="credit">Credit Card</SelectItem>
                  <SelectItem value="check">Check</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="amount">Amount Paid</Label>
              <Select
                name="amount"
                required
                defaultValue={availablePrices[0].toString()}
                onValueChange={setSelectedAmount}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select amount paid" />
                </SelectTrigger>
                <SelectContent>
                  {availablePrices.map((price) => (
                    <SelectItem key={price} value={price.toString()}>
                      ${price}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="total_points">Total Points</Label>
              <Input
                type="number"
                id="total_points"
                name="total_points"
                required
                min="0"
                className="w-full bg-gray-100"
                disabled
                value={totalPoints}
              />
            </div>
          </div>

          <div className="mt-6 flex justify-end gap-2">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              className="px-4 py-2 rounded border"
            >
              Cancel
            </Button>
            <Button
              type="submit"
              className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
              disabled={isPending || !user}
            >
              {isPending ? "Adding..." : "Add Membership"}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default MembershipModal;
