import React from "react";
import { useMemberData } from "context/useMemberData";
import { useAuthCheck } from "hooks/useAuthCheck";
import ClientSearch from "@/components/client-search";
import { DashboardNav } from "@/components/dashboard/dashboard-nav";

export default function CheckinPage() {
  const { isAuthenticated, user } = useAuthCheck();

  if (isAuthenticated === null) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        Loading...
      </div>
    );
  }

  const isStaff = ["admin", "staff", "volunteer"].includes(user?.role);

  return (
    <React.Fragment>
      <DashboardNav />
      <div className="flex items-center justify-center my-5">
        <ClientSearch isStaff={isStaff} isModal={false} />
      </div>
    </React.Fragment>
  );
}
