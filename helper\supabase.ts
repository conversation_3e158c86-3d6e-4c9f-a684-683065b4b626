import { supabaseClient } from "utils/supabase/client";

export async function checkEmailExist(email: string) {
  const { data: existingUser, error: checkEmailError } = await supabaseClient
    .from("users")
    .select("id, email")
    .eq("email", email);

  if (checkEmailError) {
    console.error("Email check error:", checkEmailError.message);
    throw new Error("Failed to check email existence.");
  }

  if (existingUser.length > 0) {
    console.log("Email is already registered");
    return { error: "Email is already registered." };
  }

  return null;
}

export async function checkUsernameExist(username: string) {
  const { data: existingUsername, error: usernameError } = await supabaseClient
    .from("users")
    .select("id, username")
    .eq("username", username);

  if (usernameError) {
    console.error("Email check error:", usernameError.message);
    throw new Error("Failed to check username existence");
  }

  if (existingUsername.length > 0) {
    console.log("Username is already registered");
    return { error: "Username is already registered" };
  }

  return null;
}

// Function to handle database fetches
export async function fetchDatabaseData({
  table,
  selection,
  identifierField,
  identifierValue,
  multipleRows = false,
}: {
  table: string;
  selection?: string;
  identifierField: string;
  identifierValue: string | number | null;
  multipleRows?: boolean;
}) {
  const query = supabaseClient
    .from(table)
    .select(selection ? selection : "*")
    .eq(identifierField, identifierValue);

  // Apply .single() conditionally
  const { data, error } = multipleRows ? await query : await query.single();

  if (error) {
    console.error(`Failed to fetch ${table} data:`, error.message);
    throw new Error(`Could not fetch ${table} data.`);
  }

  return data;
}

export async function insertData(table: string, data: object) {
  try {
    const { data: insertedData, error } = await supabaseClient
      .from(table)
      .insert(data)
      .select();

    if (error) throw error;
    return insertedData;
  } catch (error) {
    console.error(`Error inserting data into ${table}:`, error);
    throw error;
  }
}
