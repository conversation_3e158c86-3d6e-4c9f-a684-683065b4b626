import { Card } from "@stackshift-ui/card";
import { Container } from "@stackshift-ui/container";
import { Flex } from "@stackshift-ui/flex";
import { Section } from "@stackshift-ui/section";
import { Text } from "@stackshift-ui/text";
import React from "react";
import { StatsProps } from ".";
import { StatItems } from "../../../types";

export default function Statistics_E({ stats }: StatsProps) {
  return (
    <Section className="py-10 bg-white !p-0">
      <Container maxWidth={1280} className="px-2">
        <Flex className="flex flex-row text-center md:w-11/12 mx-auto px-0">
          <StatisticsItems stats={stats} />
        </Flex>
      </Container>
    </Section>
  );
}

function StatisticsItems({ stats }: { stats?: StatItems[] }) {
  if (!stats) return null;

  return (
    <React.Fragment>
      {stats?.map((items, index) => (
        <div
          className="w-full py-4 md:mx-3 mx-1 bg-cfc-primary rounded-2xl"
          key={index}
        >
          <p className="text-5xl text-white md:text-7xl overflow-ellipsis overflow-clip">
            {items?.value}
          </p>

          <Text
            weight="black"
            className="mb-1 text-white text-center !font-light"
          >
            {items?.label}
          </Text>
        </div>
      ))}
    </React.Fragment>
  );
}

export { Statistics_E };
