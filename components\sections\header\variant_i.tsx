import { Button } from "@stackshift-ui/button";
import { Container } from "@stackshift-ui/container";
import { Flex } from "@stackshift-ui/flex";
import { Heading } from "@stackshift-ui/heading";
import { Image } from "@stackshift-ui/image";
import { Section } from "@stackshift-ui/section";
import { Text } from "@stackshift-ui/text";
import React from "react";
import { ButtonProps, HeaderProps } from ".";

export default function Header_I({
  mainImage,
  title,
  description,
}: HeaderProps) {
  return (
    <Section
      className="pt-12 !px-0"
    >
      <Container maxWidth={1260}>
        <Flex className="w-full items-center justify-center flex-col md:flex-row max-w-5xl mx-auto md:px-8 px-4 gap-4">
          <div className="w-full md:w-8/12">
            <TitleAndDescription title={title} description={description} />
          </div>

          <div className="w-full md:w-4/12 flex items-center mx-auto">
            <MainImage mainImage={mainImage} />
          </div>
        </Flex>
      </Container>
    </Section>
  );
}

function TitleAndDescription({
  title,
  description,
}: {
  title?: string;
  description?: string;
}) {
  return (
    <div className="pr-4">
      {title && (
        <Heading
          type="h2"
          className="mb-3 font-light leading-none !text-cfc-secondary md:text-4xl text-3xl"
        >
          {title}
        </Heading>
      )}

      {description && (
        <Text
          muted
          className="my-6 !text-base leading-relaxed font-extralight"
        >
          {description}
        </Text>
      )}
    </div>
  );
}


interface MainImageProps {
  mainImage?: {
    image?: string | any;
    alt?: string;
  };
}

function MainImage({ mainImage }: MainImageProps) {
  if (!mainImage?.image) return null;

  return (
    <div className="relative flex items-start justify-start w-full max-w-md mx-auto pr-8">
      <Image
        className="object-cover rounded md:max-w-full md:w-auto w-full h-auto relative z-10"
        src={`${mainImage.image}`}
        // sizes="(min-width: 520px) 520px, 90vw"
        width={300}
        height={120}
        alt={mainImage.alt ?? "header-main-image"}
      />
    </div>
  );
}


export { Header_I };
