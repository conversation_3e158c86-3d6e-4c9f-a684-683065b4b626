{"name": "@webriq-pagebuilder/site-template-default", "version": "6.0.4", "private": true, "license": "MIT", "author": {"name": "WebriQ", "email": "<EMAIL>"}, "scripts": {"build": "next build", "dev": "next dev", "lint": "next lint", "start": "next start", "update:tag": "echo 'Enter custom tag: 👇 (eg: latest, next, your-own-tag)' && read ctag && yarn add @webriq-pagebuilder/sanity-plugin-input-component-gpt3@$ctag @webriq-pagebuilder/sanity-plugin-input-component-variants@$ctag @webriq-pagebuilder/sanity-plugin-schema-blog@$ctag @webriq-pagebuilder/sanity-plugin-schema-commerce@$ctag @webriq-pagebuilder/sanity-plugin-schema-default@$ctag @webriq-pagebuilder/sanity-plugin-webriq-blog@$ctag @webriq-pagebuilder/sanity-plugin-webriq-components@$ctag @webriq-pagebuilder/sanity-plugin-webriq-forms@$ctag @webriq-pagebuilder/sanity-plugin-webriq-payments@$ctag @webriq-pagebuilder/sanity-plugin-desk-studio-version@$ctag @webriq-pagebuilder/sanity-plugin-inspector-inline-edit@$ctag @webriq-pagebuilder/sanity-plugin-input-component-social-accounts@$ctag", "update-stackshift": "yarn add @stackshift-ui/app-promo@latest @stackshift-ui/blog@latest @stackshift-ui/button@latest @stackshift-ui/call-to-action@latest @stackshift-ui/contact@latest @stackshift-ui/container@latest @stackshift-ui/cookies@latest @stackshift-ui/faqs@latest @stackshift-ui/features@latest @stackshift-ui/flex@latest @stackshift-ui/footer@latest @stackshift-ui/form-field@latest @stackshift-ui/grid@latest @stackshift-ui/header@latest @stackshift-ui/heading@latest @stackshift-ui/how-it-works@latest @stackshift-ui/image@latest @stackshift-ui/input@latest @stackshift-ui/link@latest @stackshift-ui/logo-cloud@latest @stackshift-ui/navigation@latest @stackshift-ui/newsletter@latest @stackshift-ui/portfolio@latest @stackshift-ui/section@latest @stackshift-ui/signin-signup@latest @stackshift-ui/statistics@latest @stackshift-ui/swiper-pagination@latest @stackshift-ui/system@latest @stackshift-ui/team@latest @stackshift-ui/testimonial@latest @stackshift-ui/text@latest @stackshift-ui/text-component@latest @stackshift-ui/webriq-form@latest", "stackshift:custom": "plop", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "build-storybook-docs": "storybook build --docs", "chromatic": "npx chromatic --project-token=chpt_68d919151b3e677 --only-changed", "test": "npx playwright test", "test:ui": "npx playwright test --ui", "test:generate": "npx playwright codegen localhost:3000"}, "dependencies": {"@chakra-ui/react": "2.8.2", "@emotion/styled": "^11.13.5", "@portabletext/react": "^3.0.9", "@radix-ui/react-accordion": "^1.2.1", "@radix-ui/react-dialog": "^1.1.4", "@radix-ui/react-dropdown-menu": "^2.1.4", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-popover": "^1.1.5", "@radix-ui/react-radio-group": "^1.2.2", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.1.4", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-switch": "^1.1.2", "@radix-ui/react-tabs": "^1.1.2", "@sanity/code-input": "^4.1.1", "@sanity/icons": "^3.4.0", "@sanity/image-url": "^1.0.2", "@sanity/types": "^3.17.0", "@sanity/ui": "^2.8.9", "@sanity/vision": "^3.17.0", "@stackshift-ui/app-promo": "^6.0.3", "@stackshift-ui/blog": "^6.0.6", "@stackshift-ui/button": "^6.0.3", "@stackshift-ui/call-to-action": "^6.0.4", "@stackshift-ui/contact": "^6.0.4", "@stackshift-ui/container": "^6.0.3", "@stackshift-ui/cookies": "^6.0.3", "@stackshift-ui/faqs": "^6.0.5", "@stackshift-ui/features": "^6.0.4", "@stackshift-ui/flex": "^6.0.3", "@stackshift-ui/footer": "^6.0.4", "@stackshift-ui/form-field": "^6.0.4", "@stackshift-ui/grid": "^6.0.3", "@stackshift-ui/header": "^6.0.5", "@stackshift-ui/heading": "^6.0.3", "@stackshift-ui/how-it-works": "^6.0.3", "@stackshift-ui/image": "^6.0.3", "@stackshift-ui/input": "^6.0.4", "@stackshift-ui/link": "^6.0.3", "@stackshift-ui/logo-cloud": "^6.0.4", "@stackshift-ui/navigation": "^6.0.4", "@stackshift-ui/newsletter": "^6.0.4", "@stackshift-ui/portfolio": "^6.0.4", "@stackshift-ui/section": "^6.0.3", "@stackshift-ui/signin-signup": "^6.0.4", "@stackshift-ui/statistics": "^6.0.4", "@stackshift-ui/swiper-pagination": "^6.0.3", "@stackshift-ui/system": "^6.0.3", "@stackshift-ui/team": "^6.0.4", "@stackshift-ui/testimonial": "^6.0.3", "@stackshift-ui/text": "^6.0.3", "@stackshift-ui/text-component": "^6.0.3", "@stackshift-ui/webriq-form": "^6.0.3", "@stripe/react-stripe-js": "^2.1.0", "@stripe/stripe-js": "^4.8.0", "@supabase/auth-helpers-nextjs": "^0.10.0", "@supabase/supabase-js": "^2.47.12", "@tiptap/extension-highlight": "^2.11.5", "@tiptap/pm": "^2.11.5", "@tiptap/react": "^2.11.5", "@tiptap/starter-kit": "^2.11.5", "@webriq-pagebuilder/sanity-plugin-desk-studio-version": "^6.0.4", "@webriq-pagebuilder/sanity-plugin-input-component-gpt3": "^6.0.4", "@webriq-pagebuilder/sanity-plugin-input-component-social-accounts": "^6.0.4", "@webriq-pagebuilder/sanity-plugin-input-component-variants": "^6.0.4", "@webriq-pagebuilder/sanity-plugin-inspector-inline-edit": "^6.0.4", "@webriq-pagebuilder/sanity-plugin-schema-blog": "^6.0.4", "@webriq-pagebuilder/sanity-plugin-schema-commerce": "^6.0.4", "@webriq-pagebuilder/sanity-plugin-schema-default": "^6.0.4", "@webriq-pagebuilder/sanity-plugin-webriq-blog": "^6.0.4", "@webriq-pagebuilder/sanity-plugin-webriq-components": "^6.0.4", "@webriq-pagebuilder/sanity-plugin-webriq-forms": "^6.0.4", "@webriq-pagebuilder/sanity-plugin-webriq-payments": "^6.0.4", "axios": "^1.3.6", "braille": "^1.1.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cors": "^2.8.5", "crypto-js": "^4.1.1", "date-fns": "^4.1.0", "deepmerge": "^4.3.1", "history": "^5.3.0", "jsonwebtoken": "^9.0.0", "lodash": "^4.17.21", "lucide-react": "^0.471.0", "magicast": "^0.3.3", "marked": "^14.1.2", "msw-storybook-addon": "2.0.3", "nanoid": "^5.0.7", "next": "^14.2.25", "next-sanity": "4.3.3", "polished": "^4.2.2", "react": "^18.2.0", "react-calendar": "^5.1.0", "react-colorful": "^5.6.1", "react-dom": "^18.2.0", "react-error-boundary": "^4.1.1", "react-is": "^18.2.0", "react-sticky-el": "^2.1.1", "react-toast": "^1.0.3", "react-tooltip": "^5.28.0", "sanity": "^3.17.0", "sanity-plugin-iframe-pane": "2.6.1", "sanity-plugin-media": "^2.2.2", "sass": "^1.62.1", "sharp": "^0.33.5", "split-pane-react": "^0.1.3", "stripe": "^17.2.0", "styled-components": "^6.1.13", "swiper": "^11.1.14", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "ts-dedent": "^2.2.0", "vanilla-cookieconsent": "^3.0.1"}, "devDependencies": {"@netlify/plugin-nextjs": "^5.5.0", "@playwright/test": "1.48.0", "@storybook/addon-designs": "^8.0.0", "@storybook/addon-docs": "^8.0.0", "@storybook/addon-essentials": "^8.0.0", "@storybook/addon-interactions": "^8.0.0", "@storybook/addon-links": "^8.0.0", "@storybook/addon-mdx-gfm": "^8.0.0", "@storybook/addon-onboarding": "^8.0.0", "@storybook/blocks": "^8.0.0", "@storybook/manager-api": "^8.0.0", "@storybook/nextjs": "^8.0.0", "@storybook/react": "^8.0.0", "@storybook/test": "8.3.5", "@storybook/testing-library": "^0.2.2", "@storybook/theming": "^8.0.0", "@types/react": "^18.2.65", "@types/react-dom": "^18.2.21", "@types/vhtml": "^2.2.4", "autoprefixer": "^10.4.20", "chromatic": "^11.0.8", "eslint": "^9.12.0", "eslint-config-next": "^14.2.25", "eslint-plugin-storybook": "^0.9.0", "msw": "2.4.9", "plop": "^4.0.1", "postcss": "^8.4.47", "prettier": "^3.0.3", "sitemap": "^8.0.0", "storybook": "^8.0.0", "tailwindcss": "^3.4.12", "typescript": "5.6.3"}, "packageManager": "yarn@1.22.21+sha512.ca75da26c00327d26267ce33536e5790f18ebd53266796fbb664d2a4a5116308042dd8ee7003b276a20eace7d3c5561c3577bdd71bcb67071187af124779620a"}