import React, { FC, useState, useEffect } from "react";
import { Input } from "../../ui/input";
import { Label } from "../../ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../../ui/select";
import { useAlert } from "context/useAlert";
import { useAuthCheck } from "hooks/useAuthCheck";
import { useSelectClient } from "context/useSelectClientContext";
import {
  calculateMembershipPrice,
  calculatePoints,
} from "utils/membership-calculator";
import { useClientSearch } from "context/useClientSearchContext";
import { useLocation } from "hooks/useLocation";
import { CustomButton } from "@/components/ui/custom-button";
import { AlertTriangle, CreditCard } from "lucide-react";

interface MembershipFormModalProps {
  isOpen: boolean;
  onClose: () => void;
  mode: "add" | "edit";
  initialData?: {
    start_date?: string;
    end_date?: string;
    payment_method?: string;
    amount?: string;
    total_points?: number;
    membership_id?: string;
    method: string;
    used_points?: number;
    split_details: { method: string; amount: number }[];
    fee_waived?: boolean;
    credit_card_fee?: number;
  };
}

interface SplitPayment {
  method: string;
  amount: number;
  applyCreditFee?: boolean;
}

const MembershipFormModal: FC<MembershipFormModalProps> = ({
  isOpen,
  onClose,
  mode,
  initialData,
}) => {
  if (!isOpen) return null;

  const { selectedClient, refreshSelectedClient } = useSelectClient();
  const today = new Date().toISOString().split("T")[0];
  const nextMonth = new Date();
  nextMonth.setMonth(nextMonth.getMonth() + 1);
  const oneMonthFromToday = nextMonth.toISOString().split("T")[0];

  const CREDIT_CARD_FEE = 0.45;

  const getInitialDates = () => {
    if (mode === "edit" && initialData) {
      return {
        startDate: initialData.start_date || today,
        endDate: initialData.end_date || oneMonthFromToday,
      };
    }

    // Get the latest membership by end_date
    const latestMembership = selectedClient?.memberships?.reduce(
      (latest, current) => {
        if (!latest) return current;
        return new Date(current.end_date) > new Date(latest.end_date)
          ? current
          : latest;
      },
      null
    );

    if (latestMembership) {
      // Set start date to day after latest membership ends
      const latestEndDate = new Date(latestMembership.end_date);
      const nextStartDate = new Date(latestEndDate);
      nextStartDate.setDate(nextStartDate.getDate() + 1);

      // Set end date to one month after new start date
      const nextEndDate = new Date(nextStartDate);
      nextEndDate.setMonth(nextEndDate.getMonth() + 1);

      return {
        startDate: nextStartDate.toISOString().split("T")[0],
        endDate: nextEndDate.toISOString().split("T")[0],
      };
    } else {
      // No memberships, start from today
      const nextMonth = new Date(today);
      nextMonth.setMonth(nextMonth.getMonth() + 1);

      return {
        startDate: today,
        endDate: nextMonth.toISOString().split("T")[0],
      };
    }
  };

  const initialDates = getInitialDates();
  const [startDate, setStartDate] = useState(initialDates.startDate);
  const [endDate, setEndDate] = useState(initialDates.endDate);
  const [isPending, setIsPending] = useState(false);
  const [totalPoints, setTotalPoints] = useState(0);
  const [baseAmount, setBaseAmount] = useState<number | null>(null);
  const [finalAmount, setFinalAmount] = useState<number | null>(null);

  // Credit card fee states
  const [showCreditFeePrompt, setShowCreditFeePrompt] = useState(false);
  const [applyCreditFee, setApplyCreditFee] = useState(() => {
    if (initialData?.fee_waived === false) return true;
    if (initialData?.credit_card_fee && initialData.credit_card_fee > 0)
      return true;
    return false;
  });
  const [pendingSplitIndex, setPendingSplitIndex] = useState<number | null>(
    null
  );

  const { showAlert } = useAlert();
  const { user, token } = useAuthCheck();
  const { fetchSearchResults } = useClientSearch();
  const { city, region, country, error } = useLocation();

  const [isSplitPayment, setIsSplitPayment] = useState(
    initialData?.payment_method === "split" ? true : false
  );
  const [splitPayments, setSplitPayments] = useState<SplitPayment[]>(
    initialData?.split_details?.map((detail) => ({
      ...detail,
      applyCreditFee:
        detail.method === "credit"
          ? initialData?.fee_waived === false ||
            (initialData?.credit_card_fee ?? 0) > 0
          : false,
    })) || [
      { method: "cash", amount: 0, applyCreditFee: false },
      { method: "credit", amount: 0, applyCreditFee: true },
    ]
  );
  const [paymentMethod, setPaymentMethod] = useState(
    initialData?.payment_method || "cash"
  );

  useEffect(() => {
    if (isSplitPayment && !initialData?.split_details) {
      setSplitPayments([
        {
          method: "cash",
          amount: baseAmount ? baseAmount / 2 : 0,
          applyCreditFee: false,
        },
        {
          method: "credit",
          amount: baseAmount ? baseAmount / 2 : 0,
          applyCreditFee: true,
        },
      ]);
    }
  }, [isSplitPayment, baseAmount]);

  useEffect(() => {
    if (selectedClient?.household_size) {
      const calculatePointsAsync = async () => {
        const points = await calculatePoints(selectedClient.household_size);
        setTotalPoints(points);
      };
      calculatePointsAsync();
    }
    if (selectedClient?.annual_income) {
      const calculatePriceAsync = async () => {
        const prices = await calculateMembershipPrice(
          selectedClient.household_size || 0,
          selectedClient.annual_income || 0
        );
        const base = prices[0] || null;
        setBaseAmount(base);
        setFinalAmount(base);
      };
      calculatePriceAsync();
    }
  }, [selectedClient]);

  useEffect(() => {
    if (startDate) {
      const start = new Date(startDate);
      const end = new Date(start);
      end.setMonth(end.getMonth() + 1);
      setEndDate(end.toISOString().split("T")[0]);
    }
  }, [startDate]);

  // Calculate final amounts when payment method or fee settings change
  useEffect(() => {
    if (baseAmount) {
      if (isSplitPayment) {
        const total = splitPayments.reduce((sum, payment) => {
          let amount = payment.amount;
          if (payment.method === "credit" && payment.applyCreditFee) {
            amount += CREDIT_CARD_FEE;
          }
          return sum + amount;
        }, 0);
        setFinalAmount(total);
      } else {
        let final = baseAmount;
        if (paymentMethod === "credit" && applyCreditFee) {
          final += CREDIT_CARD_FEE;
        } else if (paymentMethod === "coupon") {
          final = 13;
        }
        setFinalAmount(final);
      }
    }
  }, [
    baseAmount,
    paymentMethod,
    applyCreditFee,
    isSplitPayment,
    splitPayments,
  ]);

  const handlePaymentMethodChange = (value: string) => {
    if (value === "credit") {
      setShowCreditFeePrompt(true);
    } else {
      setPaymentMethod(value);
      setApplyCreditFee(false);
    }
  };

  const handleSplitPaymentMethodChange = (index: number, value: string) => {
    if (value === "credit") {
      setPendingSplitIndex(index);
      setShowCreditFeePrompt(true);
    } else {
      const updatedPayments = [...splitPayments];
      updatedPayments[index].method = value;
      updatedPayments[index].applyCreditFee = false;

      // Prevent duplicate payment methods
      if (index === 0) {
        updatedPayments[1].method = value === "cash" ? "credit" : "cash";
      } else {
        updatedPayments[0].method = value === "cash" ? "credit" : "cash";
      }

      setSplitPayments(updatedPayments);
    }
  };

  const handleCreditFeeDecision = (applyFee: boolean) => {
    if (pendingSplitIndex !== null) {
      // Handle split payment
      const updatedPayments = [...splitPayments];
      updatedPayments[pendingSplitIndex].method = "credit";
      updatedPayments[pendingSplitIndex].applyCreditFee = applyFee;

      // Prevent duplicate payment methods
      if (pendingSplitIndex === 0) {
        updatedPayments[1].method = "cash";
      } else {
        updatedPayments[0].method = "cash";
      }

      setSplitPayments(updatedPayments);
      setPendingSplitIndex(null);
    } else {
      // Handle single payment
      setPaymentMethod("credit");
      setApplyCreditFee(applyFee);
    }

    setShowCreditFeePrompt(false);
  };

  const handleSplitChange = (index: number, field: string, value: string) => {
    const updatedPayments = [...splitPayments];

    if (field === "amount") {
      const newAmount = parseFloat(value) || 0;
      updatedPayments[index].amount = newAmount;

      // Auto-calculate the remaining amount for the other payment method
      if (baseAmount && updatedPayments.length === 2) {
        const otherIndex = index === 0 ? 1 : 0;
        const remainingAmount = baseAmount - newAmount;
        updatedPayments[otherIndex].amount = Math.max(0, remainingAmount);
      }
    }

    setSplitPayments(updatedPayments);
  };

  const validateSplitPayments = () => {
    const totalSplitBase = splitPayments.reduce(
      (sum, payment) => sum + payment.amount,
      0
    );
    return Math.abs(totalSplitBase - (baseAmount || 0)) < 0.01; // Allow for small rounding differences
  };

  const calculateSplitTotal = () => {
    return splitPayments.reduce((sum, payment) => {
      let amount = payment.amount;
      if (payment.method === "credit" && payment.applyCreditFee) {
        amount += CREDIT_CARD_FEE;
      }
      return sum + amount;
    }, 0);
  };

  const couponRedeemed =
    selectedClient?.memberships
      ?.flatMap((membership) => membership.payments || [])
      ?.find((payment) => payment?.coupons?.coupon_code) || null;

  const handleSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    setIsPending(true);

    // Split payment validation
    if (isSplitPayment && !validateSplitPayments()) {
      showAlert("Split amounts do not match base membership amount.", "error");
      setIsPending(false);
      return;
    }

    const formData = new FormData(event.currentTarget);
    const data = Object.fromEntries(formData.entries());

    // Calculate credit card fees
    let totalCreditFee = 0;
    let feeWaived = false;

    if (isSplitPayment) {
      totalCreditFee = splitPayments.reduce((sum, payment) => {
        return (
          sum +
          (payment.method === "credit" && payment.applyCreditFee
            ? CREDIT_CARD_FEE
            : 0)
        );
      }, 0);
      feeWaived = splitPayments.some(
        (payment) => payment.method === "credit" && !payment.applyCreditFee
      );
    } else if (paymentMethod === "credit") {
      totalCreditFee = applyCreditFee ? CREDIT_CARD_FEE : 0;
      feeWaived = !applyCreditFee;
    }

    try {
      const endpoint =
        mode === "add"
          ? "/api/members-data/add-membership"
          : "/api/members-data/edit-membership";

      const response = await fetch(endpoint, {
        method: mode === "add" ? "POST" : "PUT",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({
          ...data,
          total_points: totalPoints,
          member_id: selectedClient?.id,
          amount: finalAmount,
          base_amount: paymentMethod === "coupon" ? 13 : baseAmount,
          credit_card_fee: totalCreditFee,
          fee_waived: feeWaived,
          used_points: Number(initialData?.used_points || 0),
          role: user?.user_metadata?.role,
          userId: user?.id,
          created_by: user?.id,
          added_store_location: !error
            ? `${city}, ${region}, ${country}`
            : error,
          split_payments: isSplitPayment ? JSON.stringify(splitPayments) : null,
          ...(mode === "edit" && {
            membership_id: initialData?.membership_id,
            updated_store_location: !error
              ? `${city}, ${region}, ${country}`
              : error,
          }),
        }),
      });

      const responseData = await response.json();
      if (!response.ok) {
        showAlert(responseData?.message, "error");
        return;
      }

      showAlert(
        `Membership ${mode === "add" ? "added" : "updated"} successfully!`,
        "success"
      );
      onClose();
    } catch (error) {
      showAlert(`Failed to ${mode} membership. Please try again.`, "error");
    } finally {
      setIsPending(false);
      await fetchSearchResults();
      await refreshSelectedClient();
    }
  };

  return (
    <div className="fixed inset-0 z-10 bg-black bg-opacity-50 flex justify-center items-center">
      <div className="bg-white p-6 rounded shadow-lg w-[500px] max-h-[90vh] overflow-y-auto">
        {/* Credit Card Fee Prompt Modal */}
        {showCreditFeePrompt && (
          <div className="fixed inset-0 z-20 bg-black bg-opacity-50 flex justify-center items-center">
            <div className="bg-white p-6 rounded shadow-xl w-[400px] border-2 border-blue-200">
              <div className="flex items-center space-x-3 mb-4">
                <CreditCard className="h-6 w-6 text-blue-600" />
                <h5 className="text-lg font-semibold text-gray-800">
                  Credit Card Processing Fee
                </h5>
              </div>

              <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-md">
                <div className="flex items-start space-x-2">
                  <AlertTriangle className="h-4 w-4 text-blue-600 mt-0.5 flex-shrink-0" />
                  <div className="text-sm text-blue-800">
                    <p className="font-medium">
                      Credit card transactions include a $0.45 processing fee.
                    </p>
                    <p className="mt-1">
                      Would you like to add this fee to the total amount?
                    </p>
                  </div>
                </div>
              </div>

              <div className="space-y-3 mb-6">
                <div className="flex justify-between items-center p-2 bg-gray-50 rounded">
                  <span className="text-sm text-gray-600">
                    {pendingSplitIndex !== null
                      ? "Split Amount:"
                      : "Base Amount:"}
                  </span>
                  <span className="font-medium">
                    $
                    {pendingSplitIndex !== null
                      ? splitPayments[pendingSplitIndex]?.amount.toFixed(2)
                      : (baseAmount || 0).toFixed(2)}
                  </span>
                </div>
                <div className="flex justify-between items-center p-2 bg-gray-50 rounded">
                  <span className="text-sm text-gray-600">Processing Fee:</span>
                  <span className="font-medium">
                    + ${CREDIT_CARD_FEE.toFixed(2)}
                  </span>
                </div>
                <div className="flex justify-between items-center p-2 bg-blue-50 border border-blue-200 rounded">
                  <span className="text-sm font-medium text-blue-800">
                    Total with Fee:
                  </span>
                  <span className="font-bold text-blue-800">
                    $
                    {(pendingSplitIndex !== null
                      ? (splitPayments[pendingSplitIndex]?.amount || 0) +
                        CREDIT_CARD_FEE
                      : (baseAmount || 0) + CREDIT_CARD_FEE
                    ).toFixed(2)}
                  </span>
                </div>
              </div>

              <div className="flex space-x-3">
                <CustomButton
                  type="button"
                  variant="outline"
                  onClick={() => handleCreditFeeDecision(false)}
                  className="flex-1"
                >
                  No Fee
                </CustomButton>
                <CustomButton
                  type="button"
                  onClick={() => handleCreditFeeDecision(true)}
                  className="flex-1"
                >
                  Add Fee
                </CustomButton>
              </div>
            </div>
          </div>
        )}

        <h4 className="text-lg font-semibold mb-4">
          {mode === "add" ? "Add" : "Edit"} Membership
        </h4>

        <form onSubmit={handleSubmit}>
          <div className="space-y-4">
            {/* Start & End Date */}
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="start_date">Start Date</Label>
                <Input
                  type="date"
                  id="start_date"
                  name="start_date"
                  required
                  value={startDate}
                  onChange={(e) => setStartDate(e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="end_date">End Date</Label>
                <Input
                  type="date"
                  id="end_date"
                  name="end_date"
                  required
                  value={endDate}
                  onChange={(e) => setEndDate(e.target.value)}
                />
              </div>
            </div>

            <div className="space-y-2">
              <label className="flex items-center space-x-2 cursor-pointer">
                <input
                  type="checkbox"
                  checked={isSplitPayment}
                  onChange={() => setIsSplitPayment(!isSplitPayment)}
                />
                <span className="text-sm">Split Payment</span>
              </label>
            </div>

            {!isSplitPayment && (
              <div className="space-y-2">
                <Label htmlFor="payment_method">Payment Method</Label>
                <Select
                  name="payment_method"
                  required
                  value={paymentMethod}
                  onValueChange={handlePaymentMethodChange}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select payment method" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="cash">Cash</SelectItem>
                    <SelectItem value="credit">Credit Card</SelectItem>
                    <SelectItem value="coupon" disabled={!!couponRedeemed}>
                      Coupon
                    </SelectItem>
                  </SelectContent>
                </Select>

                {/* Credit Card Fee Display for Single Payment */}
                {paymentMethod === "credit" && (
                  <div className="mt-3 p-3 bg-gray-50 border border-gray-200 rounded-md space-y-2">
                    <Label className="text-sm font-medium">
                      Credit Card Fee Details
                    </Label>
                    <div className="space-y-2">
                      <div className="flex justify-between items-center text-sm">
                        <span className="text-gray-600">Base Amount:</span>
                        <span className="font-medium">
                          ${(baseAmount || 0).toFixed(2)}
                        </span>
                      </div>
                      <div className="flex justify-between items-center text-sm">
                        <span className="text-gray-600">Processing Fee:</span>
                        <span
                          className={`font-medium ${
                            applyCreditFee ? "text-green-600" : "text-red-600"
                          }`}
                        >
                          {applyCreditFee
                            ? `+ $${CREDIT_CARD_FEE.toFixed(2)}`
                            : "Waived"}
                        </span>
                      </div>
                      <hr className="border-gray-300" />
                      <div className="flex justify-between items-center text-sm font-bold">
                        <span>Total Amount:</span>
                        <span className="text-blue-600">
                          ${(finalAmount || 0).toFixed(2)}
                        </span>
                      </div>

                      {!applyCreditFee && (
                        <div className="flex items-center space-x-2 mt-2 p-2 bg-yellow-50 border border-yellow-200 rounded">
                          <AlertTriangle className="h-4 w-4 text-yellow-600" />
                          <span className="text-xs text-yellow-800">
                            Fee waived by staff
                          </span>
                        </div>
                      )}

                      <button
                        type="button"
                        onClick={() => setShowCreditFeePrompt(true)}
                        className="text-xs text-blue-600 hover:text-blue-800 underline mt-2"
                      >
                        Change fee settings
                      </button>
                    </div>
                  </div>
                )}
              </div>
            )}

            {!isSplitPayment && paymentMethod === "coupon" && (
              <div className="space-y-2">
                <Label htmlFor="coupon_code">Coupon Code</Label>
                <Input
                  defaultValue={couponRedeemed?.coupons?.coupon_code}
                  type="text"
                  id="coupon_code"
                  name="coupon_code"
                  required
                  placeholder="Enter coupon code"
                />
              </div>
            )}

            {/* Split Payment Section */}
            {isSplitPayment && (
              <div className="space-y-4">
                <Label>Split Payment Details</Label>
                {splitPayments.map((payment, index) => (
                  <div
                    key={index}
                    className="p-3 border border-gray-200 rounded-md space-y-3"
                  >
                    <div className="grid grid-cols-2 gap-4">
                      <Select
                        name={`split_method_${index}`}
                        value={payment.method}
                        onValueChange={(value) =>
                          handleSplitPaymentMethodChange(index, value)
                        }
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select method" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="cash">Cash</SelectItem>
                          <SelectItem value="credit">Credit Card</SelectItem>
                        </SelectContent>
                      </Select>
                      <Input
                        type="number"
                        name={`split_amount_${index}`}
                        value={payment.amount}
                        onChange={(e) =>
                          handleSplitChange(index, "amount", e.target.value)
                        }
                        placeholder="Enter base amount"
                        step="0.01"
                      />
                    </div>

                    {/* Credit Card Fee Display for Split Payment */}
                    {payment.method === "credit" && (
                      <div className="p-2 bg-gray-50 border border-gray-200 rounded space-y-1">
                        <div className="flex justify-between items-center text-xs">
                          <span className="text-gray-600">Base Amount:</span>
                          <span className="font-medium">
                            ${payment.amount.toFixed(2)}
                          </span>
                        </div>
                        <div className="flex justify-between items-center text-xs">
                          <span className="text-gray-600">Processing Fee:</span>
                          <span
                            className={`font-medium ${
                              payment.applyCreditFee
                                ? "text-green-600"
                                : "text-red-600"
                            }`}
                          >
                            {payment.applyCreditFee
                              ? `+ $${CREDIT_CARD_FEE.toFixed(2)}`
                              : "Waived"}
                          </span>
                        </div>
                        <hr className="border-gray-300" />
                        <div className="flex justify-between items-center text-xs font-bold">
                          <span>Subtotal:</span>
                          <span className="text-blue-600">
                            $
                            {(
                              payment.amount +
                              (payment.applyCreditFee ? CREDIT_CARD_FEE : 0)
                            ).toFixed(2)}
                          </span>
                        </div>

                        <button
                          type="button"
                          onClick={() => {
                            setPendingSplitIndex(index);
                            setShowCreditFeePrompt(true);
                          }}
                          className="text-xs text-blue-600 hover:text-blue-800 underline mt-2"
                        >
                          Change fee settings
                        </button>
                      </div>
                    )}
                  </div>
                ))}

                {/* Split Payment Summary */}
                <div className="p-3 bg-blue-50 border border-blue-200 rounded-md">
                  <div className="space-y-2">
                    <div className="flex justify-between items-center text-sm">
                      <span className="text-gray-600">Base Total:</span>
                      <span className="font-medium">
                        $
                        {splitPayments
                          .reduce((sum, p) => sum + p.amount, 0)
                          .toFixed(2)}
                      </span>
                    </div>
                    <div className="flex justify-between items-center text-sm">
                      <span className="text-gray-600">Credit Card Fees:</span>
                      <span className="font-medium">
                        $
                        {splitPayments
                          .reduce(
                            (sum, p) =>
                              sum +
                              (p.method === "credit" && p.applyCreditFee
                                ? CREDIT_CARD_FEE
                                : 0),
                            0
                          )
                          .toFixed(2)}
                      </span>
                    </div>
                    <hr className="border-blue-300" />
                    <div className="flex justify-between items-center text-sm font-bold">
                      <span className="text-blue-800">Grand Total:</span>
                      <span className="text-blue-800">
                        ${calculateSplitTotal().toFixed(2)}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            )}

            <div className="space-y-2">
              <Label htmlFor="amount">Amount Paid</Label>
              <Input
                type="number"
                id="amount"
                name="amount"
                required
                className="w-full"
                value={(finalAmount || 0).toFixed(2)}
                disabled
              />
              {paymentMethod !== "coupon" && baseAmount && (
                <p className="text-sm text-gray-500">
                  Based on household size of {selectedClient?.household_size}{" "}
                  and annual income of $
                  {selectedClient?.annual_income?.toLocaleString()}
                </p>
              )}
            </div>

            {paymentMethod !== "coupon" && (
              <div className="space-y-2">
                <Label htmlFor="total_points">Total Points</Label>
                <Input
                  type="number"
                  id="total_points"
                  name="total_points"
                  required
                  className="w-full bg-gray-100"
                  disabled
                  value={totalPoints}
                />
                <p className="text-xs text-gray-500">
                  Points are calculated based on the base membership amount
                  (excluding fees)
                </p>
              </div>
            )}
          </div>

          <div className="mt-6 flex justify-end gap-2">
            <CustomButton type="button" variant="outline" onClick={onClose}>
              Cancel
            </CustomButton>
            <CustomButton type="submit" disabled={isPending || !user}>
              {isPending
                ? `${mode === "add" ? "Adding" : "Updating"}...`
                : `${mode === "add" ? "Add" : "Update"}`}
            </CustomButton>
          </div>
        </form>
      </div>
    </div>
  );
};

export default MembershipFormModal;
