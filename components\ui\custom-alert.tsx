import { AlertCircle, CheckCircle2, X } from "lucide-react";
import { cva, type VariantProps } from "class-variance-authority";
import { cn } from "@/components/lib/utils";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { useEffect, useState } from "react";

const alertVariants = cva(
  "relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",
  {
    variants: {
      variant: {
        success:
          "bg-emerald-50 text-emerald-900 dark:bg-emerald-950 dark:text-emerald-100 border-emerald-200 dark:border-emerald-800 [&>svg]:text-emerald-600",
        error:
          "bg-destructive/10 text-destructive dark:bg-destructive/20 border-destructive/50 dark:border-destructive/50 [&>svg]:text-destructive",
      },
    },
    defaultVariants: {
      variant: "success",
    },
  }
);

export interface CustomAlertProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof alertVariants> {
  message: string;
  onDismiss?: () => void;
  duration?: number;
}

export function CustomAlert({
  className,
  variant,
  message,
  onDismiss,
  duration = 5000,
  ...props
}: CustomAlertProps) {
  const [isVisible, setIsVisible] = useState(true);
  const [progress, setProgress] = useState(100);

  useEffect(() => {
    // Start the progress countdown
    const startTime = Date.now();
    const endTime = startTime + duration;

    const progressInterval = setInterval(() => {
      const now = Date.now();
      const remaining = endTime - now;
      const progressPercent = (remaining / duration) * 100;

      if (remaining <= 0) {
        clearInterval(progressInterval);
        setIsVisible(false);
        onDismiss?.();
      } else {
        setProgress(progressPercent);
      }
    }, 10);

    // Auto-dismiss after duration
    const dismissTimeout = setTimeout(() => {
      setIsVisible(false);
      onDismiss?.();
    }, duration);

    return () => {
      clearInterval(progressInterval);
      clearTimeout(dismissTimeout);
    };
  }, [duration, onDismiss]);

  if (!isVisible) return null;

  const handleDismiss = () => {
    setIsVisible(false);
    onDismiss?.();
  };

  const progressBarColor =
    variant === "success"
      ? "bg-emerald-500 dark:bg-emerald-400"
      : "bg-destructive";

  return (
    <Alert
      className={cn(
        alertVariants({ variant }),
        "transition-all duration-300 shadow-lg",
        className
      )}
      {...props}
    >
      {variant === "success" ? (
        <CheckCircle2 className="h-4 w-4" />
      ) : (
        <AlertCircle className="h-4 w-4" />
      )}
      <AlertDescription>{message}</AlertDescription>
      {/* <button
        onClick={handleDismiss}
        className="absolute right-4 top-4 rounded-sm ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
        aria-label="Close alert"
      >
        <X className="h-4 w-4" />
      </button> */}
      <div className="absolute bottom-0 left-0 h-1 w-full rounded-b-lg overflow-hidden">
        <div
          className={cn(
            "h-full transition-all duration-100 ease-linear",
            progressBarColor
          )}
          style={{ width: `${progress}%` }}
        />
      </div>
    </Alert>
  );
}
