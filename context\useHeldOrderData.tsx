import React, {
  createContext,
  useState,
  useContext,
  useEffect,
  ReactNode,
} from "react";
import axios from "axios";
import { useAuthCheck } from "hooks/useAuthCheck";
import { supabaseClient } from "utils/supabase/client";

interface HeldOrderDataContextType {
  heldOrderData: any[];
  fetchingHeldOrderData: boolean;
  setFetchingHeldOrderData: React.Dispatch<React.SetStateAction<boolean>>;
  refreshData: () => void;
}

const HeldOrderDataContext = createContext<HeldOrderDataContextType | null>(
  null
);

export const useHeldOrderData = () => {
  const context = useContext(HeldOrderDataContext);
  if (!context) {
    throw new Error(
      "useHeldOrderData must be used within a HeldOrderDataProvider"
    );
  }
  return context;
};

interface HeldOrderDataProviderProps {
  children: ReactNode;
}

export const HeldOrderDataProvider: React.FC<HeldOrderDataProviderProps> = ({
  children,
}) => {
  const [heldOrderData, setHeldOrderData] = useState<any[]>([]);
  const [fetchingHeldOrderData, setFetchingHeldOrderData] =
    useState<boolean>(true);
  const { token } = useAuthCheck();

  const fetchData = async () => {
    setFetchingHeldOrderData(true);
    try {
      const response = await axios.get(
        "/api/user-data/checkout/get-held-order",
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
          params: {
            on_hold: true,
            is_completed: false,
          },
        }
      );
      setHeldOrderData(response.data);
    } catch (error) {
      console.error("Error fetching held order data:", error);
    } finally {
      setFetchingHeldOrderData(false);
    }
  };

  const refreshData = () => {
    fetchData();
  };

  const fetchDataForOrder = async (orderId: number) => {
    try {
      const response = await axios.get(
        "/api/user-data/checkout/get-held-order",
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
          params: { orderId },
        }
      );
      return response.data[0] || null;
    } catch (error) {
      console.error(`Error fetching order data for ID ${orderId}:`, error);
      return null;
    }
  };

  const handleRealtimeUpdate = async (payload: any) => {
    const { eventType, new: newData, old: oldData } = payload;

    if (eventType === "DELETE" && oldData?.id) {
      setHeldOrderData((prevData) =>
        prevData.filter((order) => order.id !== oldData.id)
      );
      return;
    }

    if (eventType === "UPDATE" && newData?.on_hold === false) {
      setHeldOrderData((prevData) =>
        prevData.filter((order) => order.id !== newData.id)
      );
      return;
    }

    if (
      (eventType === "INSERT" || eventType === "UPDATE") &&
      newData?.on_hold === true &&
      newData?.is_completed === false
    ) {
      const updatedOrderData = await fetchDataForOrder(newData.id);
      if (updatedOrderData) {
        setHeldOrderData((prevData) => {
          const existingIndex = prevData.findIndex(
            (order) => order.id === updatedOrderData.id
          );

          if (existingIndex !== -1) {
            const updatedData = [...prevData];
            updatedData[existingIndex] = updatedOrderData;
            return updatedData;
          } else {
            return [updatedOrderData, ...prevData];
          }
        });
      }
    }

    // console.log("Skipping irrelevant order change...");
  };

  useEffect(() => {
    fetchData();

    const heldOrderSubscription = supabaseClient
      .channel("held_orders_realtime")
      .on(
        "postgres_changes",
        {
          event: "*",
          schema: "public",
          table: "orders",
        },
        async (payload) => {
          await handleRealtimeUpdate(payload);
        }
      )
      .subscribe();

    return () => {
      supabaseClient.removeChannel(heldOrderSubscription);
    };
  }, []);

  return (
    <HeldOrderDataContext.Provider
      value={{
        heldOrderData,
        fetchingHeldOrderData,
        setFetchingHeldOrderData,
        refreshData,
      }}
    >
      {children}
    </HeldOrderDataContext.Provider>
  );
};
