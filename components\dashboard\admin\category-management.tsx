import { useState } from "react";
import { Plus, Search, Pencil, Trash2 } from "lucide-react";
import { CustomButton } from "@/components/ui/custom-button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import TableFilters from "../table-filters";
import { useFilters } from "hooks/useFilters";
import { Label } from "@/components/ui/label";
import { useAlert } from "context/useAlert";
import { useAuthCheck } from "hooks/useAuthCheck";
import { useCategoryData } from "context/useCategoryData";
import { formatDate } from "utils";
import ConfirmationModal from "../confirmation-modal";
import { DataTable } from "@/components/data-table";
import Image from "next/image";
import { RequiredLabel } from "@/components/ui/required-label";

export default function CategoryManagement() {
  const { user, token } = useAuthCheck();
  const { showAlert } = useAlert();
  const { categoryData, refreshCategory, fetchingCategory } = useCategoryData();
  const [showForm, setShowForm] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [editingCategory, setEditingCategory] = useState<any>(null);
  const { selectedFilters, handleFilterChange } = useFilters({
    productCount: [],
  });
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [categoryToDelete, setCategoryToDelete] = useState<any>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Filter configurations
  const categoryFilters = {
    productCount: {
      label: "Product Count",
      options: [
        { id: "1", label: "Low (< 10)", value: "low" },
        { id: "2", label: "Medium (10-20)", value: "medium" },
        { id: "3", label: "High (> 20)", value: "high" },
      ],
    },
  };

  const filterCategories = (categories: typeof categoryData) => {
    return categories.filter((category) => {
      const matchesSearch =
        category.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        category.description.toLowerCase().includes(searchTerm.toLowerCase());

      const matchesProductCount =
        selectedFilters.productCount.length === 0 ||
        selectedFilters.productCount.some((range) => {
          if (range === "low") return category.productCount < 10;
          if (range === "medium")
            return category.productCount >= 10 && category.productCount <= 20;
          if (range === "high") return category.productCount > 20;
          return false;
        });

      return matchesSearch && matchesProductCount;
    });
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setIsSubmitting(true);
    const formData = new FormData(e.currentTarget);
    const imageFile = (
      document.getElementById("productImage") as HTMLInputElement
    )?.files?.[0];

    try {
      let imageUrl = editingCategory?.image_url;

      if (imageFile) {
        const base64Promise = new Promise((resolve, reject) => {
          const reader = new FileReader();
          reader.onload = () => resolve(reader.result);
          reader.onerror = (error) => reject(error);
          reader.readAsDataURL(imageFile);
        });

        const base64Data = await base64Promise;

        imageUrl = {
          fileName: imageFile.name,
          fileType: imageFile.type,
          base64Data: base64Data,
        };
      }

      const endpoint = editingCategory
        ? `/api/admin/category/update-category`
        : "/api/admin/category/create-category";

      const response = await fetch(endpoint, {
        method: editingCategory ? "PUT" : "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({
          ...Object.fromEntries(formData.entries()),
          image_url: imageUrl,
          userId: user?.id,
          ...(editingCategory && {
            id: editingCategory.id,
          }),
        }),
      });

      const responseData = await response.json();
      if (!response.ok) {
        showAlert(responseData?.message, "error");
        throw new Error(responseData?.message || "An error occurred");
      }
      refreshCategory();
      showAlert(
        `Category ${editingCategory ? "updated" : "created"} successfully!`,
        "success"
      );
      setShowForm(false);
      setEditingCategory(null);
    } catch (error) {
      showAlert(error, "error");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleEdit = (category: any) => {
    setEditingCategory({
      ...category,
      icon: category.icon ? JSON.parse(category.icon) : null,
    });
    setShowForm(true);
  };

  const handleDeleteClick = (category: any) => {
    setCategoryToDelete(category);
    setShowDeleteModal(true);
  };

  const handleDeleteConfirm = async () => {
    setIsSubmitting(true);
    try {
      const response = await fetch("/api/admin/category/delete-category", {
        method: "DELETE",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({
          id: categoryToDelete.id,
          userId: user?.id,
        }),
      });

      const responseData = await response.json();
      if (!response.ok) {
        showAlert(responseData?.message, "error");
        throw new Error(responseData?.message || "An error occurred");
      }

      refreshCategory();
      showAlert(responseData?.message, "success");
    } catch (error) {
      console.log(error);
      showAlert(error, "error");
    } finally {
      setShowDeleteModal(false);
      setCategoryToDelete(null);
      setShowForm(false);
      setIsSubmitting(false);
    }
  };

  const handleDeleteCancel = () => {
    setShowDeleteModal(false);
    setCategoryToDelete(null);
  };

  const headers = [
    "Name",
    "Description",
    "Products",
    "Last Updated",
    "Actions",
  ];
  const renderRow = (category: any) => (
    <tr key={category.id} className="border-b">
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
        {category.name}
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
        {category.description}
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
        {category.products?.length}
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
        {formatDate(category.updated_at ?? category.created_at)}
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
        <div className="flex space-x-2 justify-center">
          <CustomButton
            variant="ghost"
            size="icon"
            onClick={() => handleEdit(category)}
          >
            <Pencil className="h-4 w-4" />
          </CustomButton>
          <CustomButton
            variant="ghost"
            size="icon"
            className="text-destructive"
            onClick={() => handleDeleteClick(category)}
          >
            <Trash2 className="h-4 w-4" />
          </CustomButton>
        </div>
      </td>
    </tr>
  );

  return (
    <div className="p-6 space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold">Category Management</h2>
        <CustomButton variant="outline" onClick={() => setShowForm(!showForm)}>
          <Plus className="h-4 w-4 mr-2" />
          Add New Category
        </CustomButton>
      </div>

      <div className="flex gap-4 items-center">
        <div className="relative flex-1">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
          <Input
            placeholder="Search categories..."
            className="pl-8"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        <TableFilters
          filters={categoryFilters}
          selectedFilters={selectedFilters}
          onFilterChange={handleFilterChange}
        />
      </div>

      {showForm && (
        <div className="mb-6 p-6 border rounded-lg bg-gray-50">
          <h3 className="text-lg font-medium mb-4">
            {editingCategory ? "Edit Category" : "Create New Category"}
          </h3>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="space-y-2">
              <RequiredLabel htmlFor="name">Category Name</RequiredLabel>
              <Input
                id="name"
                name="name"
                required
                defaultValue={editingCategory?.name || ""}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="description" className="text-sm font-medium">
                Description
              </Label>
              <Textarea
                id="description"
                name="description"
                rows={3}
                defaultValue={editingCategory?.description || ""}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="productImage" className="text-sm font-medium">
                Product Image
              </Label>
              <div className="flex flex-col space-y-2">
                {/* Current image preview if editing */}
                {editingCategory?.icon && (
                  <div className="w-32 h-32 border rounded-lg overflow-hidden">
                    <Image
                      src={editingCategory.icon?.base64Data}
                      alt={editingCategory.icon?.fileName}
                      className="w-full h-full object-cover"
                      width={500}
                      height={500}
                    />
                  </div>
                )}

                {/* File input and filename display */}
                <div className="flex items-center space-x-2">
                  <CustomButton
                    type="button"
                    variant="outline"
                    onClick={() =>
                      document.getElementById("productImage")?.click()
                    }
                    className="w-32"
                  >
                    Choose File
                  </CustomButton>
                  <input
                    id="productImage"
                    name="productImage"
                    type="file"
                    accept="image/*"
                    className="hidden"
                    onChange={(e) => {
                      const fileName = e.target.files?.[0]?.name;
                      if (fileName) {
                        const fileDisplay =
                          document.getElementById("fileDisplay");
                        if (fileDisplay) {
                          fileDisplay.textContent = fileName;
                        }
                      }
                    }}
                  />
                  <span id="fileDisplay" className="text-sm text-gray-600">
                    {editingCategory?.icon
                      ? editingCategory.icon?.fileName
                      : "No file chosen"}
                  </span>
                </div>
                <p className="text-xs text-gray-500">
                  Supported formats: PNG, JPG, JPEG
                </p>
              </div>
            </div>

            <div className="flex justify-end space-x-2">
              <CustomButton
                type="button"
                variant="outline"
                onClick={() => {
                  setShowForm(false);
                  setEditingCategory(null);
                }}
              >
                Cancel
              </CustomButton>
              <CustomButton type="submit" disabled={isSubmitting}>
                {editingCategory
                  ? isSubmitting
                    ? "Updating..."
                    : "Update Category"
                  : !editingCategory && isSubmitting
                  ? "Creating..."
                  : "Create Category"}
              </CustomButton>
            </div>
          </form>
        </div>
      )}

      <DataTable
        headers={headers}
        data={[...filterCategories(categoryData)].sort((a, b) =>
          a.name.localeCompare(b.name)
        )}
        renderRow={renderRow}
        isLoading={fetchingCategory}
      />

      <ConfirmationModal
        isOpen={showDeleteModal}
        message={`Are you sure you want to delete "${categoryToDelete?.name}" category?`}
        onConfirm={handleDeleteConfirm}
        onCancel={handleDeleteCancel}
        isLoading={isSubmitting}
      />
    </div>
  );
}
