import { NextApiRequest, NextApiResponse } from "next";
import { calculateCheckoutStatus, determineMembershipStatus } from "utils";
import { createSupabaseClient, supabaseClient } from "utils/supabase/client";
import { supabaseAdmin } from "utils/supabase/admin";

const updateMembershipStatus = async (membership: any) => {
  const newStatus = determineMembershipStatus(membership);

  const { error } = await supabaseClient
    .from("memberships")
    .update({ status: newStatus })
    .eq("id", membership.id);

  if (error) console.error("Error updating membership status:", error);
  return { ...membership, status: newStatus };
};

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  try {
    const { query, role } = req.query;

    // TODO: Make this much simpler
    const headers = req.headers;
    const authorization = headers.authorization;
    if (!authorization) {
      return res.status(401).json({ message: "Unauthorized" });
    }

    const values = authorization.split(" ");
    const token = values[1];

    if (!query || typeof query !== "string") {
      return res.status(400).json({ message: "Search query is required" });
    }

    const supabaseClient = createSupabaseClient(token);

    const {
      data: { users: authUsers },
      error: authError,
    } = await supabaseAdmin.listUsers();
    if (authError) {
      console.error("Error fetching auth users:", authError.message);
      return res.status(500).json({ message: "Failed to fetch auth users" });
    }
    const typedAuthUsers = authUsers as any[];
    const authUserMap = {};
    typedAuthUsers.forEach((user) => {
      authUserMap[user.id] = user.last_sign_in_at;
    });

    const { data: searchResult, error: searchError } = await supabaseClient
      .from("members")
      .select(
        `
        *,
        users (id, role, status, email, username, first_name, last_name, user_auth_id),
        memberships!memberships_member_id_fkey (
          id, member_id, status, start_date, end_date, available_points, total_points, used_points, added_store_location, updated_store_location,
          added_by:members!memberships_added_by_fkey (first_name, last_name),
          updated_by:members!memberships_updated_by_fkey (first_name, last_name),
          payments (
            id, membership_id, amount, date, method, split_details, fee_waived, credit_card_fee, added_store_location,
            coupons (id, coupon_code, referred_by)
          )
        ),
        additional_info (*),
        notes (id, type, subject, description, created_at, modified_by),
        checkins!checkins_member_id_fkey (
          id, member_id, created_at, checkin_date, checkout_date, is_forced_checked_in, added_store_location, updated_store_location,
          checked_in_by:members!checkins_checked_in_by_fkey (first_name, last_name),
          checked_out_by:members!checkins_checked_out_by_fkey (first_name, last_name)
        ),
        orders (*),
        return_items!return_items_member_id_fkey (*, store_location, products (id, product_name))
      `
      )
      .order("start_date", { referencedTable: "memberships", ascending: false })
      .order("created_at", { referencedTable: "checkins", ascending: false })
      .order("created_at", { referencedTable: "orders", ascending: false })
      .order("created_at", { referencedTable: "notes", ascending: false })
      .or(
        `address.ilike.%${query}%,first_name.ilike.%${query}%,last_name.ilike.%${query}%`
      );

    if (searchError) {
      console.error("searchError", searchError);
      return res.status(500).json({
        message: "Failed to perform search",
        error: searchError.message,
      });
    }

    const membersWithPayments = await Promise.all(
      searchResult.map(async ({ users, checkins, notes, ...member }) => {
        const updatedMemberships = await Promise.all(
          (member.memberships || []).map(async (membership) => {
            const updatedMembership = await updateMembershipStatus(membership); // Wait for the updated status
            return {
              ...membership,
              status: updatedMembership.status,
              added_by: `${membership.added_by?.first_name} ${membership.added_by?.last_name}`,
              updated_by: membership.updated_by
                ? `${membership.updated_by.first_name} ${membership.updated_by.last_name}`
                : null,
            };
          })
        );

        return {
          ...member,
          role: users?.role,
          status: users?.status,
          email: users?.email,
          name: `${users?.first_name} ${users?.last_name}`,
          address: users?.address,
          id: users?.id,
          username: users?.username,
          last_login: users?.user_auth_id
            ? authUserMap[users.user_auth_id] || null
            : null,
          contact_info: JSON.parse(member.contact_info),
          checkoutStatus: calculateCheckoutStatus(checkins, updatedMemberships),
          memberships: updatedMemberships,
          additional_info: member.additional_info?.[0]
            ? {
                ...member.additional_info[0],
                household_members: JSON.parse(
                  member.additional_info[0].household_members
                ),
                personal_info: JSON.parse(
                  member.additional_info[0].personal_info || "{}"
                ),
                age_groups: JSON.parse(
                  member.additional_info[0].age_groups || "{}"
                ),
              }
            : {},
          check_in: (checkins || []).map((checkin) => ({
            ...checkin,
            checked_in_by: `${checkin.checked_in_by?.first_name} ${checkin.checked_in_by?.last_name}`,
            checked_out_by: `${checkin.checked_out_by?.first_name} ${checkin.checked_out_by?.last_name}`,
          })),
          notes: notes?.filter((n) => {
            if (["admin", "staff"].includes(role as string)) {
              return ["staff", "volunteer"].includes(n.type);
            } else if (role === "volunteer") {
              return n.type === "volunteer";
            }
            return false;
          }),
        };
      })
    );

    return res.status(200).json(membersWithPayments);
  } catch (error) {
    console.error("Search error:", error);
    return res.status(500).json({
      message: "Failed to perform search",
      error: error.message,
    });
  }
}
