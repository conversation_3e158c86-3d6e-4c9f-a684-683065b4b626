import { PortableTextBlock } from "@portabletext/types";
import { lazy } from "react";
import { LabeledRoute, SectionsProps } from "../../../types";
import React from "react";
import dynamic from "next/dynamic";
import * as TextComponentVariants from "@stackshift-ui/text-component";

const Variants = {
  variant_a: TextComponentVariants.TextComponent_A,
  variant_b: TextComponentVariants.TextComponent_B,
  variant_c: TextComponentVariants.TextComponent_C,
  variant_d: dynamic(() => import("./variant_d")),
  variant_e: dynamic(() => import("./variant_e")),
  variant_f: dynamic(() => import("./variant_f")),
  variant_g: dynamic(() => import("./variant_g")),
  variant_h: dynamic(() => import("./variant_h")),
  variant_i: dynamic(() => import("./variant_i")),
  variant_j: dynamic(() => import("./variant_j")),
  variant_k: dynamic(() => import("./variant_k")),
  variant_l: dynamic(() => import("./variant_l")),
  variant_m: dynamic(() => import("./variant_m")),
  variant_n: dynamic(() => import("./variant_n")),
  variant_o: dynamic(() => import("./variant_o")),
};

export interface ButtonProps {
  as?: string;
  label?: string;
  link?: {
    target?: string;
    route?: string;
  };
  ariaLabel?: string;
  variant?: string;
}

export interface TextComponentProps {
  heading?: string;
  firstColumn?: PortableTextBlock[];
  secondColumn?: PortableTextBlock[];
  thirdColumn?: PortableTextBlock[];
  fourthColumn?: PortableTextBlock[];
  primaryButton?: ButtonProps;
  secondaryButton?: ButtonProps;
}

const displayName = "TextComponent";

export const TextComponent: React.FC<SectionsProps> = ({ data }) => {
  const variant = data?.variant;
  const Variant = variant && Variants[variant as keyof typeof Variants];

  const props = {
    heading: data?.variants?.title ?? undefined,
    firstColumn: data?.variants?.firstColumn ?? undefined,
    secondColumn: data?.variants?.secondColumn ?? undefined,
    thirdColumn: data?.variants?.thirdColumn ?? undefined,
    fourthColumn: data?.variants?.fourthColumn ?? undefined,
    primaryButton: data?.variants?.primaryButton ?? undefined,
    secondaryButton: data?.variants?.secondaryButton ?? undefined,
  };

  return Variant ? <Variant {...props} /> : null;
};

TextComponent.displayName = displayName;
