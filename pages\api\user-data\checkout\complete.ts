import { NextApiRequest, NextApiResponse } from "next";
import { supabaseClient } from "utils/supabase/client";

interface OrderData {
  member_id: string;
  on_hold: boolean;
  date_of_order: Date;
  total_points_used: number;
  items: any[];
  is_completed: boolean;
  available_points: number;
  store_location?: string;
}

async function processOrder(orderId: string | null, orderData: OrderData) {
  try {
    if (!orderData.member_id) {
      throw new Error("member_id is required");
    }

    const baseOrderData = {
      ...orderData,
      is_completed: true,
      store_location: orderData.store_location,
    };

    if (orderId) {
      return await updateExistingOrder(orderId, baseOrderData);
    }

    return await createNewOrder(baseOrderData);
  } catch (error) {
    console.error("Error processing order:", error);
    throw error;
  }
}

async function updateExistingOrder(orderId: string, orderData: OrderData) {
  const { data, error } = await supabaseClient
    .from("orders")
    .update(orderData)
    .eq("id", orderId)
    .eq("member_id", orderData.member_id)
    .select()
    .single();

  if (error) {
    console.error("Error updating order:", error);
    throw new Error(`Failed to update order: ${error.message}`);
  }

  return data;
}

async function createNewOrder(orderData: OrderData) {
  const { data, error } = await supabaseClient
    .from("orders")
    .insert(orderData)
    .select()
    .single();

  if (error) {
    console.error("Error creating order:", error);
    throw new Error(`Failed to create order: ${error.message}`);
  }

  return data;
}

async function updateMembershipPoints(
  member_id: string,
  total_points_used: number,
  membership_id: string
) {
  try {
    const { data: existingMembership, error: fetchError } = await supabaseClient
      .from("memberships")
      .select("id, member_id, available_points, used_points")
      .eq("id", membership_id)
      .eq("member_id", member_id)
      .single();

    if (fetchError || !existingMembership) {
      console.error("Error fetching membership:", fetchError);
      throw new Error("Membership not found or not active");
    }

    const updatedAvailablePoints =
      existingMembership.available_points - total_points_used;
    const updatedUsedPoints =
      +existingMembership.used_points + +total_points_used;

    const { data: updatedMembership, error: updateError } = await supabaseClient
      .from("memberships")
      .update({
        available_points: updatedAvailablePoints,
        used_points: updatedUsedPoints,
      })
      .eq("id", membership_id)
      .eq("member_id", member_id)
      .select()
      .single();

    if (updateError) {
      console.error("Error updating membership points:", updateError);
      throw new Error(
        `Failed to update membership points: ${updateError.message}`
      );
    }

    return updatedMembership;
  } catch (error) {
    console.error("Error in updateMembershipPoints:", error);
    throw error;
  }
}

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  const {
    checkout_by,
    orderId,
    member_id,
    items,
    total_points_used,
    on_hold,
    role,
    membership_id,
    store_location,
  } = req.body;

  if (!role || !["staff", "admin"].includes(role)) {
    return res.status(403).json({
      message: "Unauthorized. Only staff and admin can create on hold orders.",
    });
  }

  try {
    const currentDate = new Date();
    const startOfDay = new Date();
    startOfDay.setHours(0, 0, 0, 0);
    const endOfDay = new Date();
    endOfDay.setHours(23, 59, 59, 999);

    const { data: userData, error: userError } = await supabaseClient
      .from("users")
      .select("id")
      .eq("user_auth_id", checkout_by)
      .single();

    if (userError) {
      console.error("Error fetching user data:", userError.message);
      return res.status(500).json({
        message: "Error fetching user data to the database",
        error: userError.message,
      });
    }

    const { data: memberData, error: memberError } = await supabaseClient
      .from("members")
      .select("id")
      .eq("user_id", userData?.id)
      .single();

    if (memberError) {
      console.error("Error fetching member data:", memberError?.message);
      return res.status(500).json({
        message: "Error fetching member data to the database",
        error: memberError?.message,
      });
    }

    const updatedMembership = await updateMembershipPoints(
      member_id,
      total_points_used,
      membership_id
    );
    if (!updatedMembership) {
      return res.status(500).json({
        message: "Failed to update membership points",
      });
    }

    // Update check-in record
    const { error: checkInError } = await supabaseClient
      .from("checkins")
      .update({
        checkout_date: currentDate,
        checked_out_by: memberData?.id,
        updated_store_location: store_location,
      })
      .eq("member_id", member_id)
      .gte("checkin_date", startOfDay.toISOString())
      .lte("checkin_date", endOfDay.toISOString());

    if (checkInError) {
      console.log("checkInError", checkInError);
      return res.status(500).json({
        message: "Error updating check-in record",
        error: checkInError.message,
      });
    }

    // Format items for order
    const formattedItems = items.map((item) => ({
      product_id: item.id,
      product_name: item.product_name,
      quantity: item.quantity,
      points_per_unit: parseInt(item.points),
      total_points: parseInt(item.points) * item.quantity,
      order_max_quantity: parseInt(item.order_max_quantity),
      monthly_max_quantity: parseInt(item.monthly_max_quantity),
    }));

    const orderData: OrderData = {
      member_id,
      on_hold,
      date_of_order: currentDate,
      total_points_used,
      items: formattedItems,
      is_completed: true,
      available_points: updatedMembership.available_points,
      store_location,
    };

    const orderResult = await processOrder(orderId || null, orderData);

    return res.status(200).json({
      message: "Completed order transaction successfully",
      data: orderResult,
    });
  } catch (error) {
    console.error("Error in order processing:", error);
    return res.status(500).json({
      message:
        error instanceof Error ? error.message : "Error processing order",
    });
  }
}
