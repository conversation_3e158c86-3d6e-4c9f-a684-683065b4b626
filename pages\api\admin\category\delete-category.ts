import { NextApiRequest, NextApiResponse } from "next";
import { supabaseClient } from "utils/supabase/client";

export default async function DELETE(
  req: NextApiRequest,
  res: NextApiResponse
) {
  try {
    const { id, userId } = req.body;

    const { data: userData, error: userError } = await supabaseClient
      .from("users")
      .select("role")
      .eq("user_auth_id", userId)
      .single();

    if (userError) {
      return res.status(404).json({
        message: "User not found",
      });
    }

    if (!userData || userData?.role !== "admin") {
      return res.status(403).json({
        message: "Unauthorized. Only admin can delete category data.",
      });
    }

    const { error: categoryError } = await supabaseClient
      .from("category")
      .delete()
      .eq("id", id);

    if (categoryError) {
      return res.status(404).json({
        message: "Category not found",
      });
    }

    return res.status(200).json({ message: "Category deleted successfully" });
  } catch (error) {
    return res
      .status(500)
      .json({ message: "Internal Server Error", error: error.message });
  }
}
