import { PortableText } from "@portabletext/react";
import { Container } from "@stackshift-ui/container";
import { Flex } from "@stackshift-ui/flex";
import { Heading } from "@stackshift-ui/heading";
import { Section } from "@stackshift-ui/section";
import { TextComponentProps } from ".";
import { MyPortableTextComponents } from "types";

export  const textComponentBlockStyling: MyPortableTextComponents = {
    block: {
      h1: ({ children }) => (
        <Heading
          fontSize="3xl"
          weight="bold"
          className="mb-8 leading-normal text-black dark:text-white"
        >
          {children}
        </Heading>
      ),
      h2: ({ children }) => (
        <Heading
          type="h2"
          weight="light"
          fontSize="4xl"
          className="mb-2 !text-cfc-secondary dark:text-white"
        >
          {children}
        </Heading>
      ),
      h3: ({ children }) => (
        <Heading
          type="h3"
          fontSize="base"
          className="mb-4 leading-normal font-semibold !text-cfc-secondary/65 tracking-wide dark:text-white"
        >
          {children}
        </Heading>
      ),
      h4: ({ children }) => (
        <Heading
          type="h4"
          weight="bold"
          fontSize="lg"
          className="mb-8 leading-normal text-black dark:text-white"
        >
          {children}
        </Heading>
      ),
      normal: ({ children }) => (
        <p className="mb-2 leading-relaxed text-cfc-gray text-base">
          {children}
        </p>
      ),
      blockquote: ({ children }) => (
        <blockquote className="mb-6 italic leading-loose text-gray-500 px-14">
          - {children}
        </blockquote>
      ),
    },
    code: ({ value }) => (
      <pre data-language={value.language}>
        <code>{value.code}</code>
      </pre>
    ),
    list: {
      bullet: ({ children }) => (
        <ul className="flex flex-col pl-10 mb-8 space-y-4 leading-relaxed list-disc">
          {children}
        </ul>
      ),
      number: ({ children }) => (
        <ol className="flex flex-col pl-10 mb-8 space-y-4 leading-relaxed list-decimal">
          {children}
        </ol>
      ),
    },
    listItem: {
      bullet: ({ children }) => <li className="leading-relaxed">{children}</li>,
      number: ({ children }) => <li className="leading-relaxed">{children}</li>,
    },
    marks: {
      strong: ({ children }) => <strong>{children}</strong>,
      em: ({ children }) => <em>{children}</em>,
      code: ({ children }) => <code>{children}</code>,
      link: ({ children, value }) => (
        <a
          className="hover:text-primary-foreground text-primary"
          href={value.href}
          target="_blank"
          rel="noopener noreferrer"
        >
          {children}
        </a>
      ),
    },

    types: {
 
      contentTable: ({ value }) => {
        return value?.cells?.map((cell, idx) => (
          <div key={idx} className="flex flex-row items-center max-w-[200px]">
            {cell.rowArray?.map((row, index) => (
              <span
                key={index}
                // className={`p-2 ${index === 0 ? "flex-[2]" : "flex-1"}`}
                className={`py-1 w-full text-base`}
              >
                {row}
              </span>
            ))}
          </div>
        ));
      },
    },
  };

export default function TextComponent_O({
  heading,
  firstColumn,
  secondColumn,
}: TextComponentProps) {
  return (
    <Section className="md:py-4 py-0 pb-10 relative w-full">
      <Container maxWidth={1280} className="container lg:!px-4 !px-0 mx-auto">
        <Flex className="w-full md:gap-8 gap-0 flex items-start justify-center md:flex-row flex-col mx-auto lg:max-w-6xl max-w-2xl">
          {heading && (
            <div className="lg:w-1/4 md:1/3">
              <Heading
                type="h2"
                weight="normal"
                className="mb-2 text-left md:text-3xl text-2xl !text-cfc-secondary/65 !leading-tight dark:text-white md:px-6 px-0"
              >
                {heading}
              </Heading>
            </div>
          )}

          <Flex justify="center" className="lg:w-3/4 md:2/3 w-full">
            {firstColumn && (
              <div className="w-full text-xs leading-relaxed text-justify text-gray-500 lg:text-base lg:px-12">
                <PortableText
                  value={firstColumn}
                  components={textComponentBlockStyling}
                  onMissingComponent={false}
                />
              </div>
            )}
          </Flex>
        </Flex>
      </Container>
    </Section>
  );
}

export { TextComponent_O };
