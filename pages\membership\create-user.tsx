import React from "react";
import { useAuthCheck } from "hooks/useAuthCheck";
import SignupForm from "@/components/signup-form";
import { DashboardNav } from "@/components/dashboard/dashboard-nav";

export default function CreateUser() {
  const { isAuthenticated, user, loading } = useAuthCheck();

  if (loading) {
    return <div>Loading...</div>;
  }

  if (!isAuthenticated) {
    return null;
  }

  const hasAccess =
    user?.user_metadata?.role === "admin" ||
    user?.user_metadata?.role === "staff";

  return hasAccess ? (
    <div className="min-h-screen bg-background">
      <DashboardNav />
      <SignupForm />
    </div>
  ) : (
    <div className="flex items-center justify-center min-h-screen text-red-500">
      <p>
        Access denied. You do not have the required permissions to view this
        page.
      </p>
    </div>
  );
}
