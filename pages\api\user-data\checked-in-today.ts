import { NextApiRequest, NextApiResponse } from "next";
import { calculateCheckoutStatus, determineMembershipStatus } from "utils";
import { supabaseAdmin } from "utils/supabase/admin";
import { supabaseClient } from "utils/supabase/client";

const handleError = (res: NextApiResponse, message: string, error: any) => {
  console.error(message, error);
  return res.status(500).json({
    message,
    error: error.message,
  });
};

interface AuthUser {
  id: string;
  last_sign_in_at: string;
}

interface AuthUserMap {
  [key: string]: {
    last_sign_in_at: string;
  };
}

interface Membership {
  id: string;
  member_id: string;
  start_date: string;
  end_date: string;
  status: string;
  available_points: number;
  used_points: number;
}

const fetchAuthUsers = async () => {
  const {
    data: { users },
    error,
  } = await supabaseAdmin.listUsers();

  if (error) {
    throw new Error(`Error fetching auth users: ${error.message}`);
  }

  return (users as AuthUser[]).reduce<AuthUserMap>(
    (acc, user) => ({
      ...acc,
      [user.id]: {
        last_sign_in_at: user.last_sign_in_at,
      },
    }),
    {}
  );
};

const fetchDataFromTable = async (
  table: string,
  select: string,
  range: { start: number; end: number },
  order?: string
) => {
  const { data, error } = await supabaseClient
    .from(table)
    .select(select)
    .range(range.start, range.end)
    .order(order ?? "created_at", { ascending: false });

  if (error) {
    throw new Error(`Error fetching data from ${table}: ${error.message}`);
  }

  return data;
};

const mapCheckinToMember = (
  member: any,
  checkins: any[],
  allMembers: any[]
) => {
  const checkinData = checkins.filter((c) => c.member_id === member.id);

  const checkinWithMemberNames = checkinData.map((checkin) => {
    const checkedInByMember = allMembers.find(
      (m) => m.id === checkin.checked_in_by
    );

    // Fallback if `checked_in_by` is null
    if (!checkedInByMember && checkin.checked_in_by) {
      console.warn(
        `Member with ID ${checkin.checked_in_by} not found in allMembers array.`
      );
    }

    const checked_in_by = checkedInByMember
      ? `${checkedInByMember.first_name ?? ""} ${
          checkedInByMember.last_name ?? ""
        }`.trim() || "Unknown"
      : "Unknown";

    return {
      ...checkin,
      checked_in_by,
    };
  });

  return {
    ...member,
    check_in: checkinWithMemberNames,
  };
};

const updateMembershipStatus = async (membership: any) => {
  const newStatus = determineMembershipStatus(membership);

  const { error } = await supabaseClient
    .from("memberships")
    .update({ status: newStatus })
    .eq("id", membership.id);

  if (error) console.error("Error updating membership status:", error);
  return { ...membership, status: newStatus };
};

const mapMembersWithMemberships = async (
  members: any[],
  membershipsWithPayments: any[],
  checkins: any[]
) => {
  const processedMembers = await Promise.all(
    members.map(async (member) => {
      const memberWithCheckins = mapCheckinToMember(member, checkins, members);

      const memberMemberships = await Promise.all(
        membershipsWithPayments
          .filter((membership) => membership.member_id === member.id)
          .map(async (m) => await updateMembershipStatus(m))
      );

      const sortedMemberships = memberMemberships.sort((a, b) => {
        const dateA = new Date(a.start_date || "");
        const dateB = new Date(b.start_date || "");
        return dateB.getTime() - dateA.getTime();
      });

      const checkoutStatus = calculateCheckoutStatus(
        memberWithCheckins.check_in,
        sortedMemberships
      );

      return {
        ...memberWithCheckins,
        memberships: sortedMemberships,
        checkoutStatus,
      };
    })
  );

  return processedMembers;
};

const addRolesToMembers = (
  members: any[],
  users: any[],
  authUsers: any,
  orders: any[]
) => {
  return members.map((member) => {
    const user = users.find((u) => u.id === member.user_id);
    return {
      ...member,
      role: user?.role,
      email: user?.email,
      last_login: authUsers[user.user_auth_id]?.last_sign_in_at || null,
      username: user?.username,
      user_auth_id: user?.user_auth_id,
      orders: orders.filter((o) => o.member_id === member.id),
    };
  });
};

const fetchTodayCheckins = async () => {
  const today = new Date();
  today.setHours(0, 0, 0, 0);

  const { data, error } = await supabaseClient
    .from("checkins")
    .select("*")
    .gte("checkin_date", today.toISOString())
    .is("checkout_date", null);

  if (error) {
    throw new Error(`Error fetching today's checkins: ${error.message}`);
  }

  return data;
};

const fetchMembersByIds = async (memberIds: string[]) => {
  if (!memberIds.length) return [];

  const { data, error } = await supabaseClient
    .from("members")
    .select("id, first_name, last_name, user_id")
    .in("id", memberIds);

  if (error) {
    throw new Error(`Error fetching members: ${error.message}`);
  }

  return data;
};

export default async function GET(req: NextApiRequest, res: NextApiResponse) {
  try {
    // Fetch today's check-ins first
    const todayCheckins = await fetchTodayCheckins();

    // If no check-ins today, return empty array
    if (!todayCheckins.length) {
      return res.status(200).json([]);
    }

    // Get unique member IDs from check-ins
    const memberIds = [
      ...new Set(todayCheckins.map((checkin) => checkin.member_id)),
    ];

    // Fetch only the necessary data for checked-in members
    const [members, memberships, users, authUsers, orders] = await Promise.all([
      fetchMembersByIds(memberIds),
      fetchDataFromTable(
        "memberships",
        "id, member_id, available_points, used_points, status, start_date, end_date",
        { start: 0, end: 999 },
        "start_date"
      ),
      fetchDataFromTable(
        "users",
        "id, first_name, last_name, role, user_auth_id",
        { start: 0, end: 999 }
      ),
      fetchAuthUsers(),
      fetchDataFromTable("orders", "*", { start: 0, end: 999 }),
    ]);

    // Filter memberships for checked-in members only
    const relevantMemberships = (memberships as unknown as Membership[]).filter(
      (membership) => memberIds.includes(membership.member_id)
    );

    const membershipsWithPayments = relevantMemberships;

    const membersWithMemberships = await mapMembersWithMemberships(
      members,
      membershipsWithPayments,
      todayCheckins
    );

    const combinedData = addRolesToMembers(
      membersWithMemberships,
      users,
      authUsers,
      orders
    );

    // Filter to only include members who are currently checked in
    const checkedInMembers = combinedData.filter((member) => {
      const latestCheckin = member.check_in?.[0];
      return latestCheckin && !latestCheckin.checkout_date;
    });

    return res.status(200).json(checkedInMembers || []);
  } catch (error) {
    return handleError(
      res,
      "Failed to process the request due to an unexpected error.",
      error
    );
  }
}
