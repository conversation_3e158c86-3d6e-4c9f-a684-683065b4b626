import React from "react";
import { CustomButton } from "../../ui/custom-button";
import { Card } from "../../ui/card";
import { Tag, Package } from "lucide-react";
import { useProductData } from "context/useProductCategoryData";
import { useCategoryData } from "context/useCategoryData";
import { Skeleton } from "@/components/ui/skeleton";
import { playSound } from "utils";
import Image from "next/image";

export interface Product {
  id: string;
  product_name: string;
  category: string;
  unit_points: number;
  order_max_quantity: number;
  monthly_max_quantity: number;
  is_active: boolean;
  icon?: {
    base64Data: string;
    fileName: string;
    fileType: string;
  };
}

interface ProductListProps {
  products: Product[];
  onSelectProduct: (product: Product) => void;
}

export function ProductList({ products, onSelectProduct }: ProductListProps) {
  const { fetchingCategory, categoryData } = useCategoryData();
  const { fetchingProduct } = useProductData();
  const activeProducts = products?.filter((product) => product.is_active);
  const [selectedCategory, setSelectedCategory] = React.useState<string | null>(
    null
  );

  // Filter products by selected category
  const filteredProducts = React.useMemo(() => {
    if (!selectedCategory) {
      return activeProducts.map((product) => ({
        ...product,
        icon:
          typeof product.icon === "string"
            ? JSON.parse(product.icon)
            : product.icon ?? null,
      }));
    }

    return activeProducts
      .filter(
        (product) =>
          product.category?.trim().toLowerCase() ===
          selectedCategory.trim().toLowerCase()
      )
      .map((product) => ({
        ...product,
        icon:
          typeof product.icon === "string"
            ? JSON.parse(product.icon)
            : product.icon ?? null,
      }));
  }, [activeProducts, selectedCategory]);

  return (
    <div className="space-y-3">
      {/* Category Filter */}
      <div className="flex gap-2 pb-2 border-b overflow-x-auto">
        {fetchingCategory && (
          <>
            {Array.from({ length: 3 }).map((_, index) => (
              <Skeleton key={index} className="h-8 w-20" />
            ))}
          </>
        )}
        {!fetchingCategory && categoryData?.length > 0 && (
          <React.Fragment>
            <CustomButton
              variant={selectedCategory === null ? "default" : "outline"}
              size="sm"
              onClick={() => setSelectedCategory(null)}
              className="rounded-full"
            >
              All
            </CustomButton>
            {categoryData.map((category) => (
              <CustomButton
                key={category.id}
                variant={
                  selectedCategory === category.name ? "default" : "outline"
                }
                size="sm"
                onClick={() => setSelectedCategory(category.name)}
                className="rounded-full"
              >
                {category.name}
              </CustomButton>
            ))}
          </React.Fragment>
        )}
      </div>

      <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-3">
        {fetchingProduct && (
          <>
            {Array.from({ length: 12 }).map((_, index) => (
              <Skeleton key={index} className="aspect-square" />
            ))}
          </>
        )}
        {!fetchingProduct &&
          filteredProducts?.map((product) => (
            <Card
              key={product.id}
              className="relative overflow-hidden hover:shadow-lg transition-shadow cursor-pointer group"
              onClick={() => {
                onSelectProduct(product);
                playSound();
              }}
            >
              {/* Product Image with Name Overlay */}
              <div className="aspect-square bg-primary/5 relative">
                {/* Product Name Overlay */}
                <div className="absolute top-0 left-0 w-full z-10">
                  <h3 className="font-bold text-center text-base leading-tight truncate bg-white/80 p-2">
                    {product.product_name}
                  </h3>
                </div>
                {product.icon ? (
                  <Image
                    src={product.icon.base64Data}
                    alt={product.icon.fileName}
                    className="w-full h-full object-cover"
                    width={500}
                    height={500}
                  />
                ) : (
                  <Package className="h-12 w-12 absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 text-muted-foreground/50" />
                )}
              </div>

              {/* Product Info */}
              <div className="p-2 space-y-1">
                <div className="text-xs text-muted-foreground">
                  <div className="flex flex-col justify-between">
                    <span>Daily: {product.order_max_quantity}</span>
                    <span>Monthly Max: {product.monthly_max_quantity}</span>
                  </div>
                </div>
              </div>

              {/* Hover Overlay */}
              <div className="absolute inset-0 bg-primary/5 opacity-0 group-hover:opacity-100 transition-opacity flex flex-col items-center justify-center">
                <span className="bg-background/95 px-3 py-1 mt-1 rounded-full text-sm font-medium shadow-sm">
                  {product.unit_points} Points
                </span>
              </div>
            </Card>
          ))}
      </div>
    </div>
  );
}
