import { FaRoute } from "react-icons/fa";

const defaultFields = [
  {
    title: "Label",
    name: "label",
    type: "string",
  },
  {
    title: "Route type",
    description:
      "Select menu item type to add a single route or a dropdown menu",
    name: "routeType",
    type: "string",
    options: {
      list: [
        {
          title: "Single route",
          description: "Reference a single document or external link",
          value: "singleRoute",
        },
        {
          title: "Multiple Routes",
          description:
            "Define multiple sublinks as a dropdown for this menu item",
          value: "multipleRoute",
        },
      ],
      layout: "radio", // <-- leave out to make it a dropdown menu
    },
  },
  {
    title: "Link Type",
    name: "type",
    type: "string",
    hidden: ({ parent }) => {
      return parent.routeType !== "singleRoute";
    },
    options: {
      list: [
        {
          title: "Internal, inside this website",
          description: "Reference inside documents",
          value: "linkInternal",
        },
        {
          title: "External, outside this website",
          value: "linkExternal",
          description: "Takes you outside the world",
        },
      ],
      layout: "radio", // <-- leave out to make it a dropdown menu
    },
  },
  {
    title: "Page Reference",
    name: "linkInternal",
    type: "reference",
    hidden: ({ parent }) => {
      return parent.type !== "linkInternal";
    },
    to: [{ type: "page" }, { type: "mainCollection" }],
  },
  {
    title: "URL",
    name: "linkExternal",
    type: "url",
    hidden: ({ parent }) => {
      return parent.type !== "linkExternal";
    },
    validation: false,
  },
  {
    name: "linkTarget",
    title: "Link Target",
    type: "string",
    hidden: ({ parent }) => {
      return parent.routeType !== "singleRoute";
    },
    initialValue: "_self",
    options: {
      list: [
        {
          title:
            "Blank - open on a new tab (usually), but users can configure browsers to open a new window instead.",
          description: "Clicking the added link will open it in a new tab.",
          value: "_blank",
        },
        {
          title: "Self (default) - open in the same browsing context",
          description: "Clicking the link will open it on the same tab.",
          value: "_self",
        },
      ],
      layout: "radio", // <-- leave out to make it a dropdown menu
    },
  },
];

export default {
  title: "Link",
  name: "multipleLinks",
  type: "object",
  icon: FaRoute,
  fields: [
    ...defaultFields,
    {
      title: "Multiple routes",
      description: "Add a dropdown menu link",
      name: "multipleRoutes",
      type: "array",
      hidden: ({ parent }) => {
        return parent.routeType !== "multipleRoute";
      },
      of: [
        {
          type: "object",
          fields: [
            ...defaultFields,
            {
              title: "Multiple routes",
              description: "Add a dropdown menu link",
              name: "multipleInnerRoutes",
              type: "array",
              hidden: ({ parent }) => {
                return parent.routeType !== "multipleRoute";
              },
              of: [
                {
                  type: "object",
                  fields: defaultFields,
                },
              ],
            },
          ],
        },
      ],
    },
  ],
  preview: {
    select: {
      label: "label",
      routeType: "routeType",
      internalLink: "linkInternal",
      externalLink: "linkExternal",
    },
    prepare({ label, routeType, internalLink, externalLink }) {
      return {
        title: label,
        subtitle:
          routeType === "multipleRoute"
            ? "Route: multiple routes"
            : routeType === "singleRoute"
            ? externalLink === undefined
              ? internalLink === undefined
                ? "Route: Internal Link Not Set"
                : "Route: Reference to Page"
              : externalLink
            : "Route: External Link Not Set",
      };
    },
  },
};
