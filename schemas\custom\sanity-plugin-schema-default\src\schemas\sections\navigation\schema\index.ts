import {
  logo,
  primaryButton,
  routes,
  secondaryButton,
  socialLinks,
} from "../../../common/fields";
import { hideIfVariantIn } from "@webriq-pagebuilder/sanity-plugin-schema-default";

export const navigationSchema = [
  logo(),
  // routes(),
  {
    title: "Routes",
    description: "This will be your navigation menu",
    name: "routes",
    type: "array",
    of: [{ type: "multipleLinks" }],
  },
  socialLinks(
    hideIfVariantIn([
      "variant_a",
      "variant_b",
      "variant_c",
      "variant_d",
      "variant_e",
      "variant_f",
    ])
  ),
  primaryButton(),
  secondaryButton(),
];
