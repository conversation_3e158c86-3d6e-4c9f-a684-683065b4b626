"use client";
import { useEffect, useState } from "react";

export function useLocation() {
  const [location, setLocation] = useState({
    city: "",
    region: "",
    country: "",
    error: "",
    loading: true,
  });

  useEffect(() => {
    if ("geolocation" in navigator) {
      navigator.geolocation.getCurrentPosition(
        async (position) => {
          const { latitude, longitude } = position.coords;

          try {
            const response = await fetch(
              `https://nominatim.openstreetmap.org/reverse?lat=${latitude}&lon=${longitude}&format=json`
            );
            const data = await response.json();

            setLocation({
              city: data.address.city || data.address.town || "",
              region: data.address.state || "",
              country: data.address.country || "",
              error: "",
              loading: false,
            });
          } catch (err) {
            setLocation({
              ...location,
              error: "Failed to fetch location",
              loading: false,
            });
          }
        },
        (error) => {
          setLocation({
            ...location,
            error: "Location access denied",
            loading: false,
          });
        }
      );
    } else {
      setLocation({
        ...location,
        error: "Geolocation is not supported",
        loading: false,
      });
    }
  }, []);

  return location;
}
