import { NextApiRequest, NextApiResponse } from "next";
import { supabaseClient } from "utils/supabase/client";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  const API_DISABLED = true;

  if (API_DISABLED) {
    return res.status(503).json({ message: "API temporarily disabled." });
  }

  if (req.method !== "PUT") {
    return res.status(405).json({ message: "Method not allowed" });
  }

  try {
    const {
      userRole,
      id,
      name,
      code,
      description,
      startDate,
      endDate,
      status,
      pointValue,
      couponLimit,
      referredBy,
    } = req.body;

    if (!["staff", "admin"].includes(userRole as string)) {
      return res.status(403).json({ message: "Unauthorized" });
    }

    const { error } = await supabaseClient
      .from("coupons")
      .update({
        coupon_name: name,
        coupon_code: code,
        coupon_description: description,
        start_date: startDate,
        end_date: endDate,
        status,
        point_value: Number(pointValue),
        coupon_limit: couponLimit,
        referred_by: referredBy,
      })
      .eq("id", id);

    if (error) {
      return res.status(500).json({
        message: "Error updating coupon data to the database",
        error: error.message,
      });
    }

    return res.status(200).json({ message: "Coupon updated successfully" });
  } catch (error) {
    return res.status(500).json({
      message: "Failed to process the request.",
    });
  }
}
