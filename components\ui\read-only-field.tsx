import { Label } from "./label";
import { Input } from "./input";
import { Skeleton } from "./skeleton";

type ReadOnlyFieldProps = {
  label: string;
  value: string | number | undefined;
  isSmall?: boolean;
  isLoading?: boolean;
  disabled?: boolean;
};

const ReadOnlyField: React.FC<ReadOnlyFieldProps> = ({
  label,
  value,
  isSmall = false,
  isLoading = false,
  disabled = false,
}) => (
  <div className="flex flex-col gap-1">
    <Label className="text-sm font-medium">{label}</Label>
    {isLoading ? (
      <Skeleton className="w-full h-9" />
    ) : (
      <Input
        type="text"
        value={value ?? ""}
        readOnly
        className={`bg-gray-50 border rounded px-2 py-1 ${
          isSmall ? "w-20 text-center" : "w-full"
        } text-sm`}
        disabled={disabled}
      />
    )}
  </div>
);

export default ReadOnlyField;
