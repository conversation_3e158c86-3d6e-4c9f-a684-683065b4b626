import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import ProductCategoryContent from "./product-category-content";
import CategoryManagement from "./category-management";
import {
  UserPlus,
  Package,
  FolderTree,
  BookOpenCheck,
  FileText,
} from "lucide-react";
import UserManagementContent from "./user-management-content";
import { useAuthCheck } from "hooks/useAuthCheck";
import { useSearchParams } from "next/navigation";
import { useRouter } from "next/router";
import { HouseholdDataProvider } from "context/useHousehold";
import MembershipFeeGuidelines from "./membership-fee-guidelines";
import Languages from "./languages";
import { LanguageDataProvider } from "context/useLanguageData";
import MemberManagement from "./member-management";
import { Fragment } from "react";

export default function AdminDashboard() {
  const { user } = useAuthCheck();
  const searchParams = useSearchParams();
  const router = useRouter();
  const tabParam = searchParams.get("tab");

  const isAdmin = user?.user_metadata?.role === "admin";

  const handleTabChange = (value: string) => {
    router.push(
      {
        pathname: router.pathname,
        query: { ...router.query, tab: value },
      },
      undefined,
      { shallow: true }
    );
  };

  return (
    <div className="p-4 space-y-4">
      <Tabs
        defaultValue={tabParam || (isAdmin ? "products" : "categories")}
        className="w-full"
        onValueChange={handleTabChange}
      >
        <TabsList className={`grid w-full grid-cols-${isAdmin ? "5" : "1"}`}>
          {isAdmin && (
            <Fragment>
              <TabsTrigger value="products" className="flex items-center gap-2">
                <Package className="h-4 w-4" />
                Products
              </TabsTrigger>
              <TabsTrigger
                value="categories"
                className="flex items-center gap-2"
              >
                <FolderTree className="h-4 w-4" />
                Categories
              </TabsTrigger>
              <TabsTrigger
                value="membership-fee"
                className="flex items-center gap-2"
              >
                <FileText className="h-4 w-4" />
                Membership Fee Guidelines
              </TabsTrigger>
              <TabsTrigger
                value="languages"
                className="flex items-center gap-2"
              >
                <BookOpenCheck className="h-4 w-4" />
                Languages
              </TabsTrigger>
            </Fragment>
          )}
          <TabsTrigger value="staff" className="flex items-center gap-2">
            <UserPlus className="h-4 w-4" />
            Account Management
          </TabsTrigger>
          {/* <TabsTrigger value="members" className="flex items-center gap-2">
            <UserPlus className="h-4 w-4" />
            Account Management
          </TabsTrigger> */}
          {/* <TabsTrigger value="members" className="flex items-center gap-2">
            <Users className="h-4 w-4" />
            Member Management
          </TabsTrigger>
          <TabsTrigger value="coupons" className="flex items-center gap-2">
            <TicketPercent className="h-4 w-4" />
            Coupon Management
          </TabsTrigger> */}
        </TabsList>

        {isAdmin && (
          <Fragment>
            <TabsContent value="products">
              <ProductCategoryContent />
            </TabsContent>

            <TabsContent value="categories">
              <CategoryManagement />
            </TabsContent>

            <TabsContent value="membership-fee">
              <MembershipFeeGuidelines />
            </TabsContent>

            <TabsContent value="languages">
              <LanguageDataProvider>
                <Languages />
              </LanguageDataProvider>
            </TabsContent>
          </Fragment>
        )}

        <TabsContent value="staff">
          <HouseholdDataProvider>
            <UserManagementContent />
          </HouseholdDataProvider>
        </TabsContent>

        {/* <TabsContent value="members">
          <HouseholdDataProvider>
            <MemberManagement />
          </HouseholdDataProvider>
        </TabsContent> */}

        {/*<TabsContent value="coupons">
          <CouponsDataProvider>
            <CouponManagement />
          </CouponsDataProvider>
        </TabsContent> */}
      </Tabs>
    </div>
  );
}
