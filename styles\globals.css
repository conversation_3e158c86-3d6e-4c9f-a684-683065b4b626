@import url("https://fonts.googleapis.com/css2?family=Open+Sans:ital,wght@0,300..800;1,300..800&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Lora:ital,wght@0,400..700;1,400..700&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Roboto+Mono:ital,wght@0,100..700;1,100..700&display=swap");

@tailwind base;
@tailwind components;
@tailwind utilities;

/**
  IMPORTANT: ONLY ADD STYLES HERE IF THEY WILL BE USED GLOBALLY (IN ALL PAGES). 
  IF NOT, THEN ADD THEM BY CSS MODULES (e.g. Products.module.css - contains all styling for product pages only).
**/

html {
  -webkit-text-size-adjust: 100%;
}

/* ECWID COMPONENT CUSTOM STYLING */
/* TODO: MIGRATE THIS TO CSS MODULE */
/* .cart-icon {
  @apply relative before:content-[attr(data-count)] before:absolute before:flex before:items-center before:justify-center before:bg-primary before:rounded-full before:h-6 before:w-6 before:-top-3 before:-right-3 before:text-white;
} */
html#ecwid_html body#ecwid_body .cart-icon {
  position: relative;
}
html#ecwid_html body#ecwid_body .cart-icon .cart-link {
  display: block;
  position: absolute;
  height: 100%;
  width: 100%;
  top: 0;
  z-index: 1;
}
html#ecwid_html body#ecwid_body .cart-icon .ec-minicart__icon svg {
  width: 32px;
  height: 32px;
}
html#ecwid_html body#ecwid_body .ec-minicart__icon .icon-default path[stroke] {
  stroke-width: 1;
}
html#ecwid_html body#ecwid_body .ec-minicart--m .ec-minicart__counter::after {
  background-color: #007aff !important;
}
html#ecwid_html body#ecwid_body .ec-size .ec-store .ec-breadcrumbs {
  display: none !important;
}

/*
   THEME SETTINGS 
   
   Default dark mode background and text color 
   To override this, add background and text color classes along with a 'dark:' prefix to target components

   Example: 
   <button className="bg-blue-500 text-white dark:bg-blue-50 text-blue-500">
     Save
   </button>
*/
:is(.dark) {
  @apply bg-background text-white;
}
/* END OF THEME SETTINGS */

/* hide navigation sidebar when screen is less than screen size */
@media (min-width: 1024px) {
  .mobile-nav {
    display: none;
  }
}

@media (min-width: 1560px) {
  .main-nav {
    left: 50%;
    align-items: center;
    width: auto;
  }
}
@media (max-width: 1559px) {
  .main-nav {
    left: 40%;
    align-items: center;
  }
}

/* PRODUCT INFO SWIPER CUSTOM STYLE */
/* TODO: MIGRATE THIS TO CSS MODULE */
.swiper-slide-thumb-active img {
  border: 1px solid #0045d8 !important;
}

.product-images-thumbs-swiper.swiper-container .swiper-slide {
  height: 0 !important;
  padding-bottom: 25%;
}

.product-images-thumbs-swiper.swiper-container .swiper-slide > div {
  height: 0 !important;
  padding-bottom: 100%;
}

.swiper-button-disabled {
  opacity: 0.5 !important;
}

.product-images-swiper.swiper-container {
  height: calc(auto + 1px);
  /* position: relative; */
}

.product-images-swiper.swiper-container {
  padding-top: 10px;
}

.product-images-swiper.swiper-container .swiper-wrapper {
  position: relative;
  height: 0 !important;
  padding-bottom: 100%;
}

.product-images-swiper.swiper-container .swiper-slide {
  height: 0 !important;
  padding-bottom: 100%;
}

.product-images-swiper.swiper-container .swiper-slide > div {
  height: 0 !important;
  padding-bottom: 100%;
}

.toastContainer {
  z-index: 1;
}

/** Sanity Studio embed **/

/* Cosmetic: Remove border-right of tools menu */
div[data-ui="Navbar"] > div > div:first-child > div:last-child {
  border-right: 0 !important;
}

/* Hide every thing in the right navbar */
div[data-ui="Navbar"] > div > div:last-child {
  display: none !important;
}

/*  Fallback: Hide manage project from menu */
a[data-ui="MenuItem"][href^="https://sanity.io/manage/project"],
a[data-ui="MenuItem"][href^="https://sanity.io/manage/project"] + hr
{
  display: none !important;
}

/* Fallback: Hide user and logout popover from menu */
button#presence-menu,
button#login-status-menu {
  display: none !important;
}

/* Desktop: when using field groups, we hide the first tab */
[data-ui="TabList"][data-testid="field-group-tabs"] > div:first-child {
  display: none;
}

/* Mobile: when using field groups, hide the first option */
select[data-testid="field-group-select"] > option:first-child {
  display: none;
}

/* Duplicate page settings modal */
.showBtn:hover .hide {
  display: block;
}

/** Help Guide **/
ul[aria-label="List of Content"] {
  height: 100% !important;
  display: flex !important;
  flex-direction: column;
}

ul[aria-label="List of Content"] > li {
  transform: none !important;
  flex: none !important;
  position: relative !important;
  left: initial !important;
  top: initial !important;
}

ul[aria-label="List of Content"] > li:last-child {
  margin-top: auto;
  padding-top: 10px;
}

ul[aria-label="List of Content"] > li:last-child:before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  border-top: 1px solid var(--card-border-color);
}

/**** STUDIO > COMPONENTS > component filter ****/
#component_filter .optionContainer li:hover,
#component_filter .optionContainer .highlight {
  background: #f2f3f5 !important;
}

/* hide default right icon */
#component_filter .icon_down_dir {
  display: none;
}
/* custom right icon */
#component_filter:after {
  content: "";
  position: absolute;
  right: 0;
  top: 0;
  height: 100%;
  width: 35px;
  border-left: 1px solid #ced2d9;
  background: url("data:image/svg+xml,%3Csvg data-sanity-icon='chevron-down' width='1em' height='1em' viewBox='0 0 25 25' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M17 10.5L12.5 15L8 10.5' stroke='currentColor' stroke-width='1.2' stroke-linejoin='round'%3E%3C/path%3E%3C/svg%3E");
  background-size: 25px;
  background-position: center;
  background-repeat: no-repeat;
}
/* add outline when dropdown is active/focused */
#component_filter:has(.optionListContainer.displayBlock) .search-wrapper {
  outline: 2px solid #2275fb;
  outline-offset: 1px;
}
#component_filter .select-component-close-btn:hover {
  fill: red;
}

#webriq_components_filter .select-component-close-btn:hover {
  fill: red;
}
/**** end of STUDIO > COMPONENTS > component filter ****/
/* End */

/* theme settings color picker */
.color-picker {
  margin-bottom: 10px;
}

.custom-layout .react-colorful__saturation {
  margin: 15px 0;
  border-radius: 5px;
  border-bottom: none;
}

.custom-layout .react-colorful__hue {
  order: 0;
}

.custom-layout .react-colorful__pointer {
  position: absolute;
  z-index: 1;
  box-sizing: border-box;
  width: 20px;
  height: 20px;
  transform: translate(-50%, -50%);
  background-color: #fff;
  border: 2px solid #fff;
  border-radius: 50%;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.custom-layout .react-colorful__hue,
.custom-layout .react-colorful__alpha {
  height: 10px;
  border-radius: 5px;
}

.custom-layout .react-colorful__hue-pointer,
.custom-layout .react-colorful__alpha-pointer {
  width: 20px;
  height: 20px;
}

/* END OF theme settings color picker */

/* Custom Css */

.wave-svg .elementor-shape-fill {
  fill: #fff;
  transform-origin: center;
  transform: rotateY(0deg);
}

.wave-svg-bottom {
  transform: rotate(180deg);
}

.textstyle > p:first-child {
  background-color: #f0e04930;
}

.textstyle > p:nth-child(2) {
  background-color: #f0e0495e;
}

.textstyle > p:nth-child(3) {
  background-color: #f0e0498c;
}

.textstyle > p:nth-child(4) {
  background-color: #f0e049c7;
}

/* End Custom Css */

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 240 10% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;
    --primary: 240 5.9% 10%;
    --primary-foreground: 0 0% 98%;
    --secondary: 240 4.8% 95.9%;
    --secondary-foreground: 240 5.9% 10%;
    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;
    --accent: 240 4.8% 95.9%;
    --accent-foreground: 240 5.9% 10%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;
    --ring: 240 10% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
  }
  .dark {
    --background: 240 10% 3.9%;
    --foreground: 0 0% 98%;
    --card: 240 10% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 240 10% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 240 5.9% 10%;
    --secondary: 240 3.7% 15.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 240 3.7% 15.9%;
    --muted-foreground: 240 5% 64.9%;
    --accent: 240 3.7% 15.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
    --ring: 240 4.9% 83.9%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

.ProseMirror {
  padding: 1rem;
  min-height: 150px;
  outline: none;
}

.ProseMirror p {
  margin: 0;
}
