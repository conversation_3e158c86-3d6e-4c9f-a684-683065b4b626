import { rootSchema } from "@webriq-pagebuilder/sanity-plugin-schema-default";
import { MdNavigation } from "react-icons/md"
 
import { navigationVariants as baseVariantsList } from "@webriq-pagebuilder/sanity-plugin-schema-default";
import { MdVerticalAlignTop } from "react-icons/md";


import variantFImage from "./images/variant_f.png"
 
import { navigationSchema } from "./schema"

export const variantLists = [
  ...baseVariantsList,
  {
    title: "Variant F",
    description: "A new variant for navigation component",
    value: "variant_f", // update this with the correct variant letter
    image: variantFImage.src, // update with the correct variant image import
  },
]

export default rootSchema(
  "navigation",
  "Navigation",
  MdNavigation,
  variantLists,
  navigationSchema,
)
