import { NextApiRequest, NextApiResponse } from "next";
import { Coupon } from "types";
import { supabaseClient } from "utils/supabase/client";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== "GET") {
    return res.status(405).json({ message: "Method not allowed" });
  }

  try {
    const { role } = req.query;

    if (!role || !["staff", "admin", "volunteer"].includes(role as string)) {
      return res.status(403).json({
        message:
          "Unauthorized. Only staff, admin, or volunteer can fetch coupon data.",
      });
    }

    const currentDate = new Date().toISOString();

    const [{ data: expiredCoupons, error: fetchError }, { data, error }] =
      await Promise.all([
        supabaseClient
          .from("coupons")
          .select("id")
          .neq("status", "expired")
          .lt("end_date", currentDate),
        supabaseClient
          .from("coupons")
          .select("*")
          .order("created_at", { ascending: false }),
      ]);

    if (fetchError || error) {
      console.error("Error fetching coupons:", fetchError || error);
      return res.status(500).json({ message: "Failed to fetch coupon data" });
    }

    if (expiredCoupons?.length) {
      const idsToExpire = expiredCoupons.map((coupon) => coupon.id);
      const { error: updateError } = await supabaseClient
        .from("coupons")
        .update({ status: "expired" })
        .in("id", idsToExpire);

      if (updateError) {
        console.error("Error updating expired coupons:", updateError);
        return res
          .status(500)
          .json({ message: "Failed to update expired coupons" });
      }
    }

    const { data: paymentData, error: paymentError } = await supabaseClient
      .from("payments")
      .select("coupon_id")
      .not("coupon_id", "is", null);

    if (paymentError) {
      console.error("Error fetching payment data:", paymentError);
      return res.status(500).json({ message: "Failed to fetch payment data" });
    }

    const couponUsageCounts = paymentData?.reduce(
      (acc: Record<string, number>, payment) => {
        if (payment.coupon_id) {
          acc[payment.coupon_id] = (acc[payment.coupon_id] || 0) + 1;
        }
        return acc;
      },
      {}
    );

    const couponsWithUsage = (data as Coupon[]).map((coupon) => ({
      ...coupon,
      usage_count:
        couponUsageCounts && coupon.id ? couponUsageCounts[coupon.id] || 0 : 0,
    }));

    return res.status(200).json(couponsWithUsage || []);
  } catch (error) {
    console.error("Unexpected error:", error);
    return res.status(500).json({
      message: "An unexpected error occurred",
      error: process.env.NODE_ENV === "development" ? String(error) : undefined,
    });
  }
}
