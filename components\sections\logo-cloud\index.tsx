import React, { lazy } from "react";
import { SectionsProps, Images, LabeledRoute } from "../../../types";
import dynamic from "next/dynamic";
import * as LogoCloudVariants from "@stackshift-ui/logo-cloud";

import { PortableTextBlock } from "@portabletext/types";


const Variants = {
  variant_a: LogoCloudVariants.LogoCloud_A,
  variant_b: LogoCloudVariants.LogoCloud_B,
  variant_c: LogoCloudVariants.LogoCloud_C,
  variant_d: LogoCloudVariants.LogoCloud_D,
  variant_e: dynamic(() => import("./variant_e")),
  variant_f: dynamic(() => import("./variant_f")),
  variant_g: dynamic(() => import("./variant_g")),
};

export interface LogoCloudProps {
  title?: string;
  images?: Images[];
  text?: string;
  button?: LabeledRoute;
  firstColumn?: PortableTextBlock[];

}

const displayName = "LogoCloud";

export const LogoCloud: React.FC<SectionsProps> = ({ data }) => {
  const variant = data?.variant;
  const Variant = variant && Variants[variant as keyof typeof Variants];

  const props = {
    title: data?.variants?.title ?? undefined,
    images: data?.variants?.images ?? undefined,
    text: data?.variants?.plainText ?? undefined,
    button: data?.variants?.primaryButton ?? undefined,
    firstColumn: data?.variants?.firstColumn ?? undefined,

  };
  return Variant ? <Variant {...props} /> : null;
};

LogoCloud.displayName = displayName;
