import React from "react";
import dynamic from "next/dynamic";
import {
  LabeledRoute,
  LabeledRouteWithKey,
  Logo,
  SectionsProps,
} from "../../../types";
import * as NavigationVariants from "@stackshift-ui/navigation";

const Variants = {
  variant_a: NavigationVariants.Navigation_A,
  variant_b: NavigationVariants.Navigation_B,
  variant_c: NavigationVariants.Navigation_C,
  variant_d: NavigationVariants.Navigation_D,
  variant_e: dynamic(() => import("./variant_e")),
  variant_f: dynamic(() => import("./variant_f")),
};

export interface ResponsiveNavLinksProps {
  menu: boolean;
  showMenu: () => void;
  links?: LabeledRouteWithKey[];
  primaryButton?: LabeledRoute;
  secondaryButton?: LabeledRoute;
  multipleRoutes?: any;
  socialMedia?: SocialLink[];
}

export interface NavigationProps {
  template?: any;
  logo?: Logo;
  links?: LabeledRouteWithKey[];
  primaryButton?: LabeledRoute;
  secondaryButton?: LabeledRoute;
  banner?: any;
  multipleRoutes?: any;
  socialMedia?: SocialLink[];
}

const displayName = "Navigation";

export const Navigation: React.FC<SectionsProps> = ({ data }) => {
  const variant = data?.variant;
  const Variant = variant && Variants?.[variant as keyof typeof Variants];

  const props = {
    logo: data?.variants?.logo,
    links: data?.variants?.routes,
    primaryButton: data?.variants?.primaryButton,
    secondaryButton: data?.variants?.secondaryButton,
    banner: data?.variants?.banner,
    multipleRoutes: data?.variants?.multipleRoutes,
    socialMedia: data?.variants?.socialLinks,
  };

  return Variant ? <Variant {...props} /> : null;
};

Navigation.displayName = displayName;
