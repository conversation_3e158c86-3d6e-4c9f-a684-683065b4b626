import { Card } from "@stackshift-ui/card";
import { Container } from "@stackshift-ui/container";
import { Flex } from "@stackshift-ui/flex";
import { Section } from "@stackshift-ui/section";
import { Text } from "@stackshift-ui/text";
import React from "react";
import { StatsProps } from ".";
import { StatItems } from "../../../types";

export default function Statistics_D({ stats }: StatsProps) {
  return (
    <Section className="pb-10 bg-white">
      <Container maxWidth={1280}>
        <Flex
          align="center"
          justify="start"
          className="text-center max-w-5xl mx-auto flex md:flex-row flex-col"
        >
          <StatisticsItems stats={stats} />
        </Flex>
      </Container>
    </Section>
  );
}

function StatisticsItems({ stats }: { stats?: StatItems[] }) {
  if (!stats) return null;

  return (
    <React.Fragment>
      {stats.map((items, index) => (
        <div className="w-full px-4 my-8" key={index}>
          <p className="text-7xl text-cfc-primary font-black md:text-7xl">
            {items?.value}
          </p>

          <Text
            weight="black"
            className="mb-1 text-cfc-primary uppercase tracking-tight text-sm md:text-left text-center px-4"
          >
            {items?.label}
          </Text>
        </div>
      ))}
    </React.Fragment>
  );
}

export { Statistics_D };
