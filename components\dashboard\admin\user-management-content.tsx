import { Input } from "@/components/ui/input";
import { CustomButton } from "@/components/ui/custom-button";
import {
  Pencil,
  Search,
  ShieldCheck,
  ShieldMinus,
  UserPlus,
} from "lucide-react";
import React, { useState, useMemo } from "react";
import TableFilters from "../table-filters";

import { useFilters } from "hooks/useFilters";
import { useMemberData } from "context/useMemberData";
import { formatDate } from "utils";
import { DataTable } from "@/components/data-table";
import AddUserForm from "./add-user-form";
import ConfirmationModal from "../confirmation-modal";
import { useAlert } from "context/useAlert";
import { useAuthCheck } from "hooks/useAuthCheck";
import { useClientSearch } from "context/useClientSearchContext";

export default function UserManagementContent() {
  const [showUserForm, setShowUserForm] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [showSuspendModal, setShowSuspendModal] = useState(false);
  const [selectedUser, setSelectedUser] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);
  const { user, token } = useAuthCheck();
  const { staffAndAdminData, staffPagination, fetchStaffPage, refreshData } =
    useMemberData();
  const { showAlert } = useAlert();
  const { selectedFilters, handleFilterChange } = useFilters({
    role: [],
    status: [],
    paymentMethod: [],
    distributionCategory: [],
    distributionStatus: [],
  });
  const { searchResults, setSearchQuery, isSearching, setIsStaff } =
    useClientSearch();
  console.log("🚀 ~ searchResults", searchResults);
  const userFilters = {
    role: {
      label: "Role",
      options: [
        { id: "1", label: "Admin", value: "admin" },
        { id: "2", label: "Staff", value: "staff" },
        { id: "3", label: "Volunteer", value: "volunteer" },
      ],
    },
    status: {
      label: "Status",
      options: [
        { id: "1", label: "Active", value: "active" },
        { id: "2", label: "Suspended", value: "suspended" },
      ],
    },
  };

  const staffUsers = useMemo(() => {
    if (!staffAndAdminData) return [];

    return staffAndAdminData.map((member) => ({
      id: member.id,
      name: `${member.first_name} ${member.last_name}`,
      email: member.email,
      role: member.role,
      status: member.status || "Active",
      username: member.username,
      lastLogin: member.last_login ? formatDate(member.last_login) : "Never",
      first_name: member.first_name,
      last_name: member.last_name,
      middle_initial: member.middle_initial,
      phone_number: member.contact_info,
      address: member.address,
      secondaryAddress: member.secondary_address,
      city: member.city,
      state: member.state,
      zip_code: member.zip_code,
      user_id: member.user_id,
      user_auth_id: member.user_auth_id,
    }));
  }, [staffAndAdminData]);

  const filterUsers = (users: typeof staffUsers) => {
    return users.filter((user) => {
      const matchesSearch =
        user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        user.email.toLowerCase().includes(searchTerm.toLowerCase());

      const matchesRole =
        selectedFilters.role.length === 0 ||
        selectedFilters.role.includes(user.role);

      const matchesStatus =
        selectedFilters.status.length === 0 ||
        selectedFilters.status.includes(user.status);

      return matchesSearch && matchesRole && matchesStatus;
    });
  };

  const isDisabled = user?.user_metadata?.role !== "admin";

  const canEditUser = (targetUser: any) => {
    if (user?.user_metadata?.role === "admin") return true;
    if (
      user?.user_metadata?.role === "staff" &&
      targetUser.role === "volunteer"
    )
      return true;
    return false;
  };

  const handleSelectedUser = (user: any) => {
    if (!canEditUser(user)) {
      showAlert("You can only edit volunteer accounts", "error");
      return;
    }
    setSelectedUser(user);
    setShowUserForm(true);
  };

  const handleDeleteUser = (user: any) => {
    if (!canEditUser(user)) {
      showAlert("You can only delete volunteer accounts", "error");
      return;
    }
    setSelectedUser(user);
    setShowDeleteModal(true);
  };

  const handleSuspendUser = (user: any) => {
    if (!canEditUser(user)) {
      showAlert("You can only suspend volunteer accounts", "error");
      return;
    }
    setSelectedUser(user);
    setShowSuspendModal(true);
  };

  const handleSuspendConfirm = async () => {
    setIsLoading(true);
    const endpoint =
      selectedUser?.status === "active"
        ? "/api/admin/suspend-user"
        : "/api/admin/unsuspend-user";

    try {
      const response = await fetch(endpoint, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({
          role: user?.user_metadata?.role,
          user_auth_id: selectedUser.user_auth_id,
          ...(selectedUser?.status === "active" && {
            ban_duration: "876600h",
          }),
        }),
      });

      const responseData = await response.json();
      if (!response.ok) {
        showAlert(responseData?.message, "error");
        throw new Error(responseData?.message || "An error occurred");
      }

      showAlert(responseData?.message, "success");
      refreshData();
    } catch (error) {
      showAlert(error.message, "error");
    } finally {
      setShowSuspendModal(false);
      setSelectedUser(null);
      setShowUserForm(false);
      setIsLoading(false);
    }
  };

  const handleSuspendCancel = () => {
    setShowSuspendModal(false);
  };

  const handleDeleteConfirm = async () => {
    setIsLoading(true);
    try {
      const response = await fetch("/api/user-data/delete-staff", {
        method: "DELETE",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({
          id: selectedUser.id,
          userId: selectedUser.user_id,
          role: user?.user_metadata?.role,
          userAuthId: selectedUser.user_auth_id,
        }),
      });

      const responseData = await response.json();
      if (!response.ok) {
        showAlert(responseData?.message, "error");
        throw new Error(responseData?.message || "An error occurred");
      }

      refreshData();
      showAlert(responseData?.message, "success");
    } catch (error) {
      console.log(error);
      showAlert(error, "error");
    } finally {
      setShowDeleteModal(false);
      setSelectedUser(null);
      setShowUserForm(false);
      setIsLoading(false);
    }
  };

  const handleDeleteCancel = () => {
    setShowDeleteModal(false);
  };

  const pageSize = staffPagination.pageSize;
  const [searchPage, setSearchPage] = useState(1);
  const searchMode = searchTerm.trim().length > 0;

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
    setIsStaff(true);
    setSearchQuery({ query: e.target.value, role: "staff" });
    setSearchPage(1);
  };

  const displayedUsers = searchMode ? searchResults : filterUsers(staffUsers);
  const total = searchMode ? searchResults.length : staffPagination.total;
  const totalPages = searchMode
    ? Math.max(1, Math.ceil(searchResults.length / pageSize))
    : staffPagination.totalPages;
  const currentPage = searchMode ? searchPage : staffPagination.page;
  const paginatedUsers = searchMode
    ? searchResults.slice((searchPage - 1) * pageSize, searchPage * pageSize)
    : displayedUsers;

  const handlePageChange = (newPage: number) => {
    if (searchMode) {
      setSearchPage(newPage);
    } else {
      fetchStaffPage(newPage);
    }
  };

  const headers = [
    "Name",
    "Username",
    "Email",
    "Role",
    "Status",
    "Last Login",
    "Actions",
  ];

  const renderRow = (user: any) => (
    <tr key={user.id} className="border-b">
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
        {user.name}
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
        {user.username}
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
        {user.email}
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
        <span
          className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
            user.role === "Admin"
              ? "bg-purple-100 text-purple-800"
              : "bg-blue-100 text-blue-800"
          }`}
        >
          {user.role.charAt(0).toUpperCase() + user.role.slice(1)}
        </span>
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
        <span
          className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
            user.status === "active"
              ? "bg-green-100 text-green-800"
              : user.status === "suspended"
              ? "bg-red-100 text-red-800"
              : "bg-gray-100 text-gray-800"
          }`}
        >
          {user.status.charAt(0).toUpperCase() + user.status.slice(1)}
        </span>
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
        {user.lastLogin ?? formatDate(user.last_login) ?? "Never"}
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
        <div className="flex justify-center space-x-2">
          <CustomButton
            variant="ghost"
            size="icon"
            onClick={() => handleSelectedUser(user)}
            disabled={!canEditUser(user)}
          >
            <Pencil className="h-4 w-4" />
          </CustomButton>
          <CustomButton
            variant="ghost"
            size="icon"
            className={`text-destructive ${
              user.status === "active" ? "text-red-800" : "text-gray-800"
            }`}
            onClick={() => handleSuspendUser(user)}
            disabled={!canEditUser(user)}
          >
            {user.status === "active" ? (
              <ShieldMinus className="h-4 w-4" />
            ) : (
              <ShieldCheck className="h-4 w-4" />
            )}
          </CustomButton>
        </div>
      </td>
    </tr>
  );

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-semibold">Account Management</h2>
        <CustomButton
          variant="outline"
          onClick={() => {
            setShowUserForm(!showUserForm);
            setSelectedUser(null);
          }}
          disabled={
            user?.user_metadata?.role !== "admin" &&
            user?.user_metadata?.role !== "staff"
          }
        >
          <UserPlus className="h-4 w-4 mr-2" />
          Add New Account
        </CustomButton>
      </div>

      <div className="flex gap-4 items-center mb-6">
        <div className="relative flex-1">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
          <Input
            placeholder="Search staff..."
            className="pl-8"
            value={searchTerm}
            onChange={handleSearchChange}
          />
        </div>
        <TableFilters
          filters={userFilters}
          selectedFilters={selectedFilters}
          onFilterChange={handleFilterChange}
        />
      </div>

      {showUserForm && (
        <AddUserForm
          setShowUserForm={setShowUserForm}
          selectedUser={selectedUser}
          setSelectedUser={setSelectedUser}
        />
      )}

      <DataTable
        headers={headers}
        data={paginatedUsers}
        renderRow={renderRow}
        currentPage={currentPage}
        totalPages={totalPages}
        pageSize={pageSize}
        total={total}
        onPageChange={handlePageChange}
      />

      {isSearching && searchTerm.trim().length > 0 && (
        <div className="mb-4 text-gray-500 text-center">Searching...</div>
      )}

      <ConfirmationModal
        isOpen={showDeleteModal}
        message={`Are you sure you want to delete "${selectedUser?.name}" Staff data?`}
        onConfirm={handleDeleteConfirm}
        onCancel={handleDeleteCancel}
        isLoading={isLoading}
      />

      <ConfirmationModal
        isOpen={showSuspendModal}
        message={`Are you sure you want to ${
          selectedUser?.status === "active" ? "suspend" : "unsuspend"
        } "${selectedUser?.name}" staff data?`}
        onConfirm={handleSuspendConfirm}
        onCancel={handleSuspendCancel}
        isLoading={isLoading}
        isLoadingText={
          selectedUser?.status === "active"
            ? "Suspending..."
            : "Unsuspending..."
        }
      />
    </div>
  );
}
