import React, { useEffect, useRef, useState } from "react";
import Link from "next/link";
import { urlFor } from "lib/sanity";
import { ConditionalLink, logoLink } from "helper";
import * as Accordion from "@radix-ui/react-accordion";
import styles from "/styles/components/navigation-f.module.css";
import { IoCall } from "react-icons/io5";
import { RxMagnifyingGlass } from "react-icons/rx";
import { FaPhoneAlt } from "react-icons/fa";
import { Button } from "components/ui";
// import { Flex } from "components/layout/index";
import Image from "next/image";
import { useRouter } from "next/router";
import { IoMenu } from "react-icons/io5";

function VariantF({
  links,
  primaryButton,
  secondaryButton,
  logo,
  socialMedia,
  socialIcon,
}) {
  const [menu, setMenu] = React.useState(false);
  const [isFormVisible, setIsFormVisible] = useState(false);
  const [isMobileFormVisible, setIsMobileFormVisible] = useState(false);
  const formRef = useRef(null);
  const router = useRouter();

  const toggleFormVisibility = () => {
    setIsFormVisible(!isFormVisible);
  };

  const toggleMobileFormVisibility = () => {
    setIsMobileFormVisible(!isMobileFormVisible);
  };

  const handleMobileClickOutside = (event) => {
    if (formRef.current && !formRef.current.contains(event.target)) {
      setIsMobileFormVisible(false);
    }
  };

  const handleClickOutside = (event) => {
    if (formRef.current && !formRef.current.contains(event.target)) {
      setIsFormVisible(false);
    }
  };

  const handleSearch = (e) => {
    e.preventDefault();

    const form = e.currentTarget;
    const { search } = form;

    console.log("Search input:", search); // Check if search is defined
    console.log("Search value:", search ? search.value : "N/A"); // Check the value of search

    if (search) {
      router.push(
        {
          pathname: "/search-results",
          query: { q: search.value },
        },
        undefined,
        { shallow: false }
      );
    }
  };

  useEffect(() => {
    document.addEventListener(
      "mousedown",
      handleClickOutside || handleMobileClickOutside
    );
    return () => {
      document.removeEventListener(
        "mousedown",
        handleClickOutside || handleMobileClickOutside
      );
    };
  }, []);

  const showMenu = () => {
    setMenu((prevState) => !prevState);
  };

  const InternalLink = ({ link, styling }) => {
    let href;
    if (link.internalLink === "Home" || link.internalLink === "home") {
      href = "/";
    }
    //else if (link.referenceType === "contactus") {
    //   href = `/contact-us/${link.internalLink}`;
    // } else if (link.referenceType === "attorneys") {
    //   href = `/attorneys/${link.internalLink}`;
    // } else if (link.referenceType === "ourstaff") {
    //   href = `/our-staff/${link.internalLink}`;
    // }
    else {
      href = `/${link.internalLink}`;
    }
    return (
      <Link href={href} legacyBehavior>
        <a
          aria-label={`Navigation ${
            link?.label ?? "Menu"
          } links which directs to ${
            link?.internalLink === undefined
              ? "page-not-found"
              : link?.internalLink
          }`}
          className={styling}
          target={link?.linkTarget}
          rel={link?.linkTarget === "_blank" ? "noopener noreferrer" : null}
        >
          {link?.label}
        </a>
      </Link>
    );
  };

  const ExternalLink = ({ link, styling }) => {
    return (
      <a
        aria-label={`Navigation ${
          link?.label ?? "Menu"
        } links which directs to ${
          link?.externalLink === undefined
            ? "link-not-found"
            : link?.externalLink
        }`}
        className={styling}
        target={link?.linkTarget}
        href={`${!link?.externalLink ? "/#" : link?.externalLink}`}
        rel={link?.linkTarget === "_blank" ? "noopener noreferrer" : null}
      >
        {link?.label}
      </a>
    );
  };

  return (
    <section
      className="sticky top-0 w-full z-50 bg-white p-4"
      style={{ boxShadow: "0px 3px 6px #00000029" }}
    >
      <div className="bg-darkblue">
        <div className="w-full max-w-[1140px] mx-auto">
          <nav className="flex md:flex-row flex-col justify-between items-center lg:px-8 md:py-4 md:gap-4 gap-2">
            <div className="w-full flex flex-row justify-between items-center">
              <div className="flex items-center lg:w-[20%] md:w-[20%] sm:w-[30%] w-[35%] justify-start">
                <div className="lg:justify-start flex">
                  {logo?.image && (
                    <Link
                      aria-label={`Go to ${
                        logoLink(logo) === "/" ? "home page" : logoLink(logo)
                      }`}
                      className="text-3xl font-bold leading-none"
                      href={logoLink(logo)}
                      prefetch={false}
                      target={logo?.linkTarget}
                      rel={
                        logo?.linkTarget === "_blank"
                          ? "noopener noreferrer"
                          : ""
                      }
                    >
                      <Image
                        src={logo?.image}
                        width={600}
                        height={118}
                        className="object-cover w-full"
                        alt={logo?.alt ?? "navigation-logo"}
                      />
                    </Link>
                  )}
                </div>
              </div>

              {/* <div className="hidden top-1/2 left-1/2 lg:flex flex-wrap mx-auto items-center w-full justify-center"> */}
              <div className="hidden md:flex flex-wrap mx-auto items-center w-full lg:w-[50%] md:w-[80%] justify-center">
                {links?.slice(0, links?.length - 1).map((link, index) => (
                  <React.Fragment key={link?._key ?? `link-${index}`}>
                    <div
                      onClick={() => {
                        // Handle click event here
                        console.log("Div clicked!");
                      }}
                      className="group/mainRoute nav-border-right flex flex-end"
                    >
                      <div className="flex items-center cursor-pointer relative lg:px-2 px-3 py-2 group/mainRoute border-b-4 border-transparent hover:border-cfc-secondary transition duration-400">
                        {!link?.internalLink && !link?.externalLink ? (
                          <span className="text-center text-sm lg:text-sm font-normal !text-cfc-gray py-2 ">
                            {link?.label}
                          </span>
                        ) : (
                          <>
                            {link?.type === "linkInternal" ? (
                              <InternalLink
                                {...{
                                  link,
                                  styling:
                                    "w-full text-center lg:text-sm text-sm !text-cfc-gray font-normal text-black",
                                }}
                              />
                            ) : (
                              <ExternalLink
                                {...{
                                  link,
                                  styling:
                                    "w-full text-center text-base lg:text-sm !text-cfc-gray font-normal text-black",
                                }}
                              />
                            )}
                          </>
                        )}

                        {link?.routeType === "multipleRoute" && (
                          <>
                            <svg
                              width="16px"
                              height="16px"
                              fill="#58595b"
                              className="flex-none opacity-55 cursor-pointer ml-2"
                              viewBox="0 0 320 512"
                              xmlns="http://www.w3.org/2000/svg"
                            >
                              <path
                                d="M31.3 192h257.3c17.8 0 26.7 21.5 14.1 34.1L174.1 354.8c-7.8 7.8-20.5 7.8-28.3 0L17.2 226.1C4.6 213.5 13.5 192 31.3 192z"
                                fill="#000"
                              />
                            </svg>

                            {link?.multipleRoutes && (
                              <div className="absolute top-full bg-white -left-3 pointer-events-none opacity-0 group-hover/mainRoute:pointer-events-auto group-hover/mainRoute:opacity-100 transition w-[220px] flex flex-col">
                                {link?.multipleRoutes?.map((mpr, idx) => (
                                  <div
                                    key={mpr?._key ?? `mpr-${idx}`}
                                    onClick={(e) => {
                                      // Handle click event here
                                      e.stopPropagation(); // Prevents parent div click
                                      console.log("Inner Div clicked!");
                                    }}
                                    className="group/innerRoute relative"
                                  >
                                    <div className="group/innerRoute ">
                                      {!mpr?.internalLink &&
                                      !mpr?.externalLink ? (
                                        <span className="flex items-center space-x-[10px] relative transition font-normal text-base text-cfc-gray bg-cfc-secondary hover:bg-white hover:text-cfc-secondary justify-between px-2 py-3 border-opacity-40">
                                          {mpr?.label}
                                          {mpr?.routeType ===
                                            "multipleRoute" && (
                                            <svg
                                              width="20px"
                                              height="20px"
                                              fill="currentColor"
                                              className="-rotate-90 ml-auto flex-none"
                                              viewBox="0 0 320 512"
                                              xmlns="http://www.w3.org/2000/svg"
                                            >
                                              <path d="M31.3 192h257.3c17.8 0 26.7 21.5 14.1 34.1L174.1 354.8c-7.8 7.8-20.5 7.8-28.3 0L17.2 226.1C4.6 213.5 13.5 192 31.3 192z" />
                                            </svg>
                                          )}
                                        </span>
                                      ) : (
                                        <div className="flex items-center space-x-[10px] relative transition font-normal text-base text-white bg-cfc-secondary hover:bg-gray-100 hover:text-cfc-secondary justify-between px-2 py-3 border-opacity-40">
                                          {mpr?.type === "linkInternal" ? (
                                            <InternalLink
                                              {...{
                                                link: mpr,
                                                styling: "w-full",
                                              }}
                                            />
                                          ) : (
                                            <ExternalLink
                                              {...{
                                                link: mpr,
                                                styling:
                                                  "w-full font-normal text-lg",
                                              }}
                                            />
                                          )}
                                          {mpr?.routeType ===
                                            "multipleRoute" && (
                                            <svg
                                              width="20px"
                                              height="20px"
                                              fill="currentColor"
                                              className="-rotate-90 ml-auto flex-none"
                                              viewBox="0 0 320 512"
                                              xmlns="http://www.w3.org/2000/svg"
                                            >
                                              <path d="M31.3 192h257.3c17.8 0 26.7 21.5 14.1 34.1L174.1 354.8c-7.8 7.8-20.5 7.8-28.3 0L17.2 226.1C4.6 213.5 13.5 192 31.3 192z" />
                                            </svg>
                                          )}
                                        </div>
                                      )}

                                      {mpr?.routeType === "multipleRoute" && (
                                        <>
                                          {mpr?.multipleInnerRoutes && (
                                            <div className="absolute top-10 bg-white left-full pointer-events-none opacity-0 group-hover/innerRoute:pointer-events-auto group-hover/innerRoute:opacity-100 transition w-[220px] flex flex-col">
                                              {mpr?.multipleInnerRoutes?.map(
                                                (innerRoute, idx) => (
                                                  <>
                                                    {innerRoute?.type ===
                                                    "linkInternal" ? (
                                                      <InternalLink
                                                        {...{
                                                          link: innerRoute,
                                                          styling:
                                                            "font-futura font-thin px-3 py-2 text-base w-full text-white bg-darkerblue hover:bg-darkblue2 transition border-b-[1px] border-white border-opacity-40",
                                                        }}
                                                      />
                                                    ) : (
                                                      <ExternalLink
                                                        {...{
                                                          link: innerRoute,
                                                          styling:
                                                            "font-futura font-thin px-8 py-3 text-lg w-full hover:text-white hover:bg-darkerblue  transition border-b-[1px] border-white border-opacity-40",
                                                        }}
                                                      />
                                                    )}
                                                  </>
                                                )
                                              )}
                                            </div>
                                          )}
                                        </>
                                      )}
                                    </div>
                                  </div>
                                ))}
                              </div>
                            )}
                          </>
                        )}
                      </div>
                    </div>
                  </React.Fragment>
                ))}

                {links && links.length > 0 && (
                  <React.Fragment
                    key={
                      links[links.length - 1]?._key ??
                      `link-${links.length - 1}`
                    }
                  >
                    <div
                      className="flex items-center space-x-[10px] relative px-5 py-2 group/mainRoute border-b-4 border-transparent hover:border-cfc-secondary transition duration-400"
                      style={{ padding: "8px 14px;" }}
                    >
                      {!links[links.length - 1]?.internalLink &&
                      !links[links.length - 1]?.externalLink ? (
                        <span className="text-center text-sm lg:text-sm font-normal text-cfc-gray cursor-pointer">
                          {links[links.length - 1]?.label}
                        </span>
                      ) : (
                        <>
                          {links[links.length - 1]?.type === "linkInternal" ? (
                            <InternalLink
                              {...{
                                link: links[links.length - 1],
                                styling:
                                  "text-center text-base lg:text-md font-normal text-black",
                              }}
                            />
                          ) : (
                            <ExternalLink
                              {...{
                                link: links[links.length - 1],
                                styling:
                                  "text-center text-base lg:text-md font-bold text-white",
                              }}
                            />
                          )}
                        </>
                      )}

                      {links[links.length - 1]?.routeType ===
                        "multipleRoute" && (
                        <>
                          <svg
                            width="16px"
                            height="16px"
                            className="flex-none opacity-55"
                            viewBox="0 0 320 512"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              d="M31.3 192h257.3c17.8 0 26.7 21.5 14.1 34.1L174.1 354.8c-7.8 7.8-20.5 7.8-28.3 0L17.2 226.1C4.6 213.5 13.5 192 31.3 192z"
                              fill="#000"
                            />
                          </svg>
                          {links[links.length - 1]?.multipleRoutes && (
                            <div className="absolute top-full bg-white -left-5 pointer-events-none opacity-0 group-hover/mainRoute:pointer-events-auto group-hover/mainRoute:opacity-100 transition w-[220px] flex flex-col">
                              {links[links.length - 1]?.multipleRoutes?.map(
                                (mpr, idx) => (
                                  <a
                                    key={mpr?._key ?? `mpr-${idx}`}
                                    href={`${
                                      mpr?.internalLink ?? mpr?.externalLink
                                    }`}
                                  >
                                    <div className="group/innerRoute">
                                      {!mpr?.internalLink &&
                                      !mpr?.externalLink ? (
                                        <span className="flex items-center space-x-[10px] relative transition font-normal text-base text-white bg-cfc-secondary hover:bg-white hover:text-cfc-secondary justify-between px-2 py-3 border-opacity-40">
                                          {mpr?.label}
                                          {mpr?.routeType ===
                                            "multipleRoute" && (
                                            <svg
                                              width="20px"
                                              height="20px"
                                              fill="currentColor"
                                              className="-rotate-90 ml-auto flex-none"
                                              viewBox="0 0 320 512"
                                              xmlns="http://www.w3.org/2000/svg"
                                            >
                                              <path d="M31.3 192h257.3c17.8 0 26.7 21.5 14.1 34.1L174.1 354.8c-7.8 7.8-20.5 7.8-28.3 0L17.2 226.1C4.6 213.5 13.5 192 31.3 192z" />
                                            </svg>
                                          )}
                                        </span>
                                      ) : (
                                        <div className="flex items-center space-x-[10px] relative transition font-normal text-base text-white bg-cfc-secondary hover:bg-white hover:text-cfc-secondary justify-between px-2 py-3 border-opacity-40">
                                          {mpr?.type === "linkInternal" ? (
                                            <InternalLink
                                              {...{
                                                link: mpr,
                                                styling: "",
                                              }}
                                            />
                                          ) : (
                                            <ExternalLink
                                              {...{
                                                link: mpr,
                                                styling:
                                                  "w-full font-normal text-lg",
                                              }}
                                            />
                                          )}
                                          {mpr?.routeType ===
                                            "multipleRoute" && (
                                            <svg
                                              width="20px"
                                              height="20px"
                                              fill="currentColor"
                                              className="-rotate-90 ml-auto flex-none"
                                              viewBox="0 0 320 512"
                                              xmlns="http://www.w3.org/2000/svg"
                                            >
                                              <path d="M31.3 192h257.3c17.8 0 26.7 21.5 14.1 34.1L174.1 354.8c-7.8 7.8-20.5 7.8-28.3 0L17.2 226.1C4.6 213.5 13.5 192 31.3 192z" />
                                            </svg>
                                          )}
                                        </div>
                                      )}

                                      {mpr?.routeType === "multipleRoute" && (
                                        <>
                                          {mpr?.multipleInnerRoutes && (
                                            <div className="absolute top-0 bg-white left-full pointer-events-none opacity-0 group-hover/innerRoute:pointer-events-auto group-hover/innerRoute:opacity-100 transition w-[220px] flex flex-col">
                                              {mpr?.multipleInnerRoutes?.map(
                                                (innerRoute, idx) => (
                                                  <a
                                                    key={
                                                      innerRoute?._key ??
                                                      `innerRoute-${idx}`
                                                    }
                                                    href={`${
                                                      innerRoute?.internalLink ??
                                                      innerRoute?.externalLink
                                                    }`}
                                                  >
                                                    {innerRoute?.type ===
                                                    "linkInternal" ? (
                                                      <InternalLink
                                                        {...{
                                                          link: innerRoute,
                                                          styling:
                                                            "font-futura font-thin px-3 py-2 text-base w-full text-white bg-darkerblue hover:bg-darkblue2 transition border-b-[1px] border-white border-opacity-40",
                                                        }}
                                                      />
                                                    ) : (
                                                      <ExternalLink
                                                        {...{
                                                          link: innerRoute,
                                                          styling:
                                                            "font-futura font-thin px-8 py-3 text-lg w-full hover:text-white hover:bg-darkerblue  transition border-b-[1px] border-white border-opacity-40",
                                                        }}
                                                      />
                                                    )}
                                                  </a>
                                                )
                                              )}
                                            </div>
                                          )}
                                        </>
                                      )}
                                    </div>
                                  </a>
                                )
                              )}
                            </div>
                          )}
                        </>
                      )}
                    </div>
                  </React.Fragment>
                )}
              </div>

              {/* md btn */}
              <div className="hidden md:flex lg:w-[30%] md:w-[20%] xl:flex w-full lg:flex-row flex-col lg:gap-4 gap-2  items-start lg:justify-end justify-center">
                {primaryButton?.label && (
                  <ConditionalLink
                    link={primaryButton}
                    className="w-full hvr-ripple-out text-white tracking-wider text-sm font-medium transition duration-200"
                    ariaLabel={primaryButton?.label}
                  >
                    <div className="w-full text-center rounded-xl px-6 py-1 bg-[#5cb85c] hover:bg-cfc-primary-foreground hover:scale-110 transition-all duration-300 ease-in-out">
                      {primaryButton?.label}
                    </div>
                  </ConditionalLink>
                )}

                {secondaryButton?.label && (
                  <ConditionalLink
                    link={secondaryButton}
                    className="hvr-ripple-out w-full text-white tracking-wider text-sm font-medium transition duration-200"
                    ariaLabel={secondaryButton?.label}
                  >
                    <div className="w-full text-center leading-none rounded-xl px-6 py-1 bg-cfc-secondary hover:bg-[#037FBD] hover:scale-110 transition-all duration-300 ease-in-out">
                      {secondaryButton?.label}
                    </div>
                  </ConditionalLink>
                )}
              </div>

              {/* md/sm screens menu sidebar */}
              <div className="gap-3 inline-flex md:hidden items-center md:pl-60 lg:pl-0">
                <div className="px-2 inline-flex lg:hidden items-center">
                  <button
                    className="xl:hidden rounded text-cfc-gray"
                    aria-label="Navbar Menu button"
                    onClick={showMenu}
                  >
                    <IoMenu className="w-10 h-10 hover:text-cfc-secondary" />
                  </button>
                </div>
              </div>
            </div>

            {/* sm/btns */}
            <div className="w-full md:hidden flex items-start justify-center flex-row md:gap-4 gap-4 md:px-4 px-2">
              {primaryButton?.label && (
                <div className="w-full text-center rounded-xl px-6 py-2 bg-[#5cb85c] hover:bg-cfc-primary-foreground hover:scale-105 transition-all duration-300 ease-in-out !leading-none h-auto">
                  <ConditionalLink
                    link={primaryButton}
                    className="hvr-ripple-out !leading-none text-white tracking-wider text-sm font-medium transition duration-200"
                    ariaLabel={primaryButton?.label}
                    target="_blank"
                  >
                    {primaryButton?.label}
                  </ConditionalLink>
                </div>
              )}

              {secondaryButton?.label && (
                <div className="w-full text-center leading-none rounded-xl px-6 py-2 bg-cfc-secondary hover:bg-[#037FBD] hover:scale-105 transition-all duration-300 ease-in-out">
                  <ConditionalLink
                    link={secondaryButton}
                    className="hvr-ripple-out leading-none text-white tracking-wider text-sm font-medium transition duration-200"
                    ariaLabel={secondaryButton?.label}
                  >
                    {secondaryButton?.label}
                  </ConditionalLink>
                </div>
              )}
            </div>
          </nav>
        </div>
      </div>

      <div
        className={`${menu ? null : "hidden"} w-full mobile-nav relative z-50`}
      >
        <div
          className="fixed inset-0 bg-gray-800 opacity-25 navbar-backdrop"
          onClick={showMenu}
        />

        <nav className="fixed top-0 bottom-0 right-0 flex flex-col w-5/6 max-w-sm px-5 py-6 overflow-y-auto bg-cfc-secondary border-r">
          <div className="flex items-center mb-8">
            <Button
              variant="unstyled"
              as="button"
              ariaLabel="Navigation menu"
              className="navbar-close text-white"
              onClick={showMenu}
            >
              <svg
                className="w-6 h-6 text-white cursor-pointer hover:text-white"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke="white"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </Button>
          </div>

          <Accordion.Root type="multiple" className="flex flex-col ">
            {links?.map((link, i) => (
              <>
                {link?.routeType === "multipleRoute" ? (
                  <Accordion.Item
                    value={`mainRoute-${i}`}
                    className={styles["AccordionItem"]}
                  >
                    <Accordion.Header className="font-futura font-normal py-3  text-xl text-white w-full hover:bg-white hover:text-cfc-secondary group transition justify-between flex border-b border-white px-2">
                      {link?.type === "linkInternal" ? (
                        <InternalLink
                          {...{
                            link,
                            styling: "",
                          }}
                        />
                      ) : (
                        <ExternalLink
                          {...{
                            link,
                            styling: "",
                          }}
                        />
                      )}

                      <Accordion.Trigger className={styles["AccordionTrigger"]}>
                        <svg
                          width="20px"
                          height="20px"
                          fill="currentColor"
                          className={`${styles["AccordionChevron"]}`}
                          viewBox="0 0 320 512"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path d="M31.3 192h257.3c17.8 0 26.7 21.5 14.1 34.1L174.1 354.8c-7.8 7.8-20.5 7.8-28.3 0L17.2 226.1C4.6 213.5 13.5 192 31.3 192z" />
                        </svg>
                      </Accordion.Trigger>
                    </Accordion.Header>

                    <Accordion.Content
                      className={`${styles["AccordionContent"]}`}
                    >
                      <Accordion.Root
                        type="multiple"
                        className="flex flex-col p-2  bg-cfc-secondary"
                      >
                        {link?.multipleRoutes?.map((mpr, ii) => (
                          <>
                            {mpr?.routeType === "multipleRoute" ? (
                              <Accordion.Item
                                value={`innerRoute-${ii}`}
                                className={`${styles["AccordionItem"]}`}
                              >
                                <Accordion.Header
                                  className={`font-futura font-thin py-3 text-xl w-full hover:bg-[#071322] transition justify-between flex text-white`}
                                >
                                  {mpr?.type === "linkInternal" ? (
                                    <InternalLink
                                      {...{
                                        link: mpr,
                                        styling: "",
                                      }}
                                    />
                                  ) : (
                                    <ExternalLink
                                      {...{
                                        link: mpr,
                                        styling: "",
                                      }}
                                    />
                                  )}
                                  <Accordion.Trigger
                                    className={styles["AccordionTrigger"]}
                                  >
                                    <svg
                                      width="20px"
                                      height="20px"
                                      fill="currentColor"
                                      className={`${styles["AccordionChevron"]}`}
                                      viewBox="0 0 320 512"
                                      xmlns="http://www.w3.org/2000/svg"
                                    >
                                      <path d="M31.3 192h257.3c17.8 0 26.7 21.5 14.1 34.1L174.1 354.8c-7.8 7.8-20.5 7.8-28.3 0L17.2 226.1C4.6 213.5 13.5 192 31.3 192z" />
                                    </svg>
                                  </Accordion.Trigger>
                                </Accordion.Header>

                                <Accordion.Content
                                  className={styles["AccordionContent"]}
                                >
                                  <div className="flex flex-col">
                                    {mpr?.multipleInnerRoutes?.map(
                                      (innerRoute, iii) => (
                                        <>
                                          {innerRoute?.type ===
                                          "linkInternal" ? (
                                            <InternalLink
                                              {...{
                                                link: innerRoute,
                                                styling:
                                                  "font-futura font-thin px-3 py-1 text-xl w-full hover:bg-cfc-secondary hover:bg-white transition text-white",
                                              }}
                                            />
                                          ) : (
                                            <ExternalLink
                                              {...{
                                                link: innerRoute,
                                                styling:
                                                  "font-futura font-thin px-3 py-1 text-xl w-full hover:!bg-cfc-secondary hover:!bg-white transition text-white",
                                              }}
                                            />
                                          )}
                                        </>
                                      )
                                    )}
                                  </div>
                                </Accordion.Content>
                              </Accordion.Item>
                            ) : (
                              <>
                                {!mpr?.internalLink && !mpr?.externalLink ? (
                                  <span className="font-futura font-thin px-3 py-1 text-xl w-full hover:text-cfc-secondary hover:bg-white transition text-white">
                                    {mpr?.label}
                                  </span>
                                ) : (
                                  <>
                                    {mpr?.type === "linkInternal" ? (
                                      <InternalLink
                                        {...{
                                          link: mpr,
                                          styling:
                                            "font-futura font-thin px-3 py-1 text-xl w-full hover:text-cfc-secondary hover:bg-white transition text-white",
                                        }}
                                      />
                                    ) : (
                                      <ExternalLink
                                        {...{
                                          link: mpr,
                                          styling:
                                            "font-futura font-thin px-3 py-1 text-xl w-full hover:bg-cfc-secondary hover:bg-brand transition text-white",
                                        }}
                                      />
                                    )}
                                  </>
                                )}
                              </>
                            )}
                          </>
                        ))}
                      </Accordion.Root>
                    </Accordion.Content>
                  </Accordion.Item>
                ) : (
                  <>
                    {!link?.internalLink && !link?.externalLink ? (
                      <span className="font-futura font-thin px-3 py-1 text-xl w-full hover:bg-cfc-secondary hover:bg-brand transition text-white">
                        {link?.label}
                      </span>
                    ) : (
                      <>
                        {link?.type === "linkInternal" ? (
                          <InternalLink
                            {...{
                              link,
                              styling:
                                "font-futura font-normal px-3 py-1 text-xl w-full hover:text-cfc-secondary hover:bg-white hover:bg-cfc-secondary hover:bg-brand transition text-white",
                            }}
                          />
                        ) : (
                          <ExternalLink
                            {...{
                              link,
                              styling:
                                "font-futura font-thin px-3 py-1 text-xl w-full hover:bg-cfc-secondary hover:bg-brand transition text-white",
                            }}
                          />
                        )}
                      </>
                    )}
                  </>
                )}
              </>
            ))}
          </Accordion.Root>
        </nav>
      </div>
    </section>
  );
}

export default React.memo(VariantF);
