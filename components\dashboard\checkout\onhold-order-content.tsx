import { Card } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import React, { useState } from "react";
import ConfirmationModal from "../confirmation-modal";
import { useAlert } from "context/useAlert";
import { useAuthCheck } from "hooks/useAuthCheck";
import { CustomButton } from "@/components/ui/custom-button";
import { Trash2 } from "lucide-react";
import { useHeldOrderData } from "context/useHeldOrderData";
import { useSelectClient } from "context/useSelectClientContext";
import { useClientSearch } from "context/useClientSearchContext";
import { getCurrentActiveMembership } from "utils";

export default function OnholdOrderContent({
  heldOrders,
  handleResumeOrder,
}: {
  heldOrders: any;
  handleResumeOrder: any;
}) {
  const { fetchingHeldOrderData } = useHeldOrderData();
  const { refreshSelectedClient } = useSelectClient();
  const { fetchSearchResults } = useClientSearch();
  const [orderToDelete, setOrderToDelete] = useState<any>(null);
  const { user, token } = useAuthCheck();
  const { showAlert } = useAlert();

  const handleDeleteClick = (order: any) => {
    setOrderToDelete(order);
  };

  const handleConfirmDelete = async () => {
    if (orderToDelete) {
      try {
        const response = await fetch(
          "/api/user-data/checkout/delete-held-order",
          {
            method: "DELETE",
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${token}`,
            },
            body: JSON.stringify({
              id: orderToDelete.id,
              role: user?.user_metadata?.role,
            }),
          }
        );

        const data = await response.json();
        if (!response.ok) {
          showAlert(data?.message, "error");
          throw new Error(data?.message || "An error occurred");
        }

        showAlert(data.message, "success");
        await fetchSearchResults();
        await refreshSelectedClient();
      } catch (error) {
        console.error("Error deleting held order:", error);
        showAlert(error.message, "error");
      } finally {
        setOrderToDelete(null);
      }
    }
  };

  return (
    <>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {fetchingHeldOrderData &&
          Array.from({ length: 3 }).map((_, index) => (
            <div key={index} className="px-2 py-1.5">
              <Skeleton className="h-40 w-full" />
            </div>
          ))}
        {!fetchingHeldOrderData &&
          heldOrders.map((order) => (
            <Card key={order.id} className="p-4">
              <div className="flex justify-between items-start mb-4">
                <div>
                  <h3 className="font-bold">{order.member.first_name}</h3>
                  <div className="text-sm text-muted-foreground space-y-1">
                    <p>{new Date(order.date_of_order).toLocaleString()}</p>
                    <p>
                      Available Points:{" "}
                      {
                        getCurrentActiveMembership(order.member.memberships)
                          ?.available_points
                      }
                    </p>
                  </div>
                </div>
                <div className="flex gap-2">
                  <CustomButton
                    variant="outline"
                    size="sm"
                    onClick={() => handleResumeOrder(order)}
                  >
                    Resume Order
                  </CustomButton>
                  <CustomButton
                    variant="ghost"
                    size="icon"
                    className="text-destructive"
                    onClick={() => handleDeleteClick(order)}
                  >
                    <Trash2 className="h-4 w-4" />
                  </CustomButton>
                </div>
              </div>
              <div className="space-y-2">
                {order.items.map((item) => (
                  <div key={item.id} className="flex justify-between text-sm">
                    <span>{item.product_name}</span>
                    <span>x{item.quantity}</span>
                  </div>
                ))}
              </div>
              <div className="mt-4 pt-4 border-t">
                <p className="font-bold">
                  Total Points: {order.total_points_used}
                </p>
              </div>
            </Card>
          ))}
        {!fetchingHeldOrderData && heldOrders.length === 0 && (
          <div className="col-span-full text-center py-8 text-muted-foreground">
            No orders on hold
          </div>
        )}
      </div>

      <ConfirmationModal
        isOpen={!!orderToDelete}
        onCancel={() => setOrderToDelete(null)}
        onConfirm={handleConfirmDelete}
        message="Are you sure you want to delete this order? This action cannot be undone."
      />
    </>
  );
}
