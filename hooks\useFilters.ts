import { useState } from "react";

export type FilterState = {
  [key: string]: string[];
};

export function useFilters(initialFilters: FilterState = {}) {
  const [selectedFilters, setSelectedFilters] =
    useState<FilterState>(initialFilters);

  const handleFilterChange = (filterKey: string, value: string) => {
    setSelectedFilters((prev) => {
      const currentFilters = prev[filterKey] || [];
      return {
        ...prev,
        [filterKey]: currentFilters.includes(value)
          ? currentFilters.filter((v) => v !== value)
          : [...currentFilters, value],
      };
    });
  };

  return {
    selectedFilters,
    handleFilterChange,
    setSelectedFilters,
  };
}
