import { NextApiRequest, NextApiResponse } from "next";
import { supabaseAdmin } from "utils/supabase/admin";
import { supabaseClient } from "utils/supabase/client";

export default async function POST(req: NextApiRequest, res: NextApiResponse) {
  const { role, user_auth_id, ban_duration } = await req.body;

  if (role !== "admin") {
    return res
      .status(401)
      .json({ error: "Unauthorized, only Admin can ban a user" });
  }

  const { data: fetchUserData, error: fetchError } = await supabaseClient
    .from("users")
    .select("id")
    .eq("user_auth_id", user_auth_id)
    .single();

  if (fetchError) {
    return res
      .status(500)
      .json({ message: "Error fetching user data", error: fetchError.message });
  }

  const { error } = await supabaseAdmin.updateUserById(user_auth_id, {
    ban_duration,
  });

  if (error) {
    return res
      .status(500)
      .json({ message: "Error banning user", error: error.message });
  }

  const { error: userError } = await supabaseClient
    .from("users")
    .update({ status: "suspended" })
    .eq("id", fetchUserData.id);

  if (userError) {
    return res
      .status(500)
      .json({ message: "Error suspending user", error: userError.message });
  }

  return res.status(200).json({ message: "User banned successfully" });
}
