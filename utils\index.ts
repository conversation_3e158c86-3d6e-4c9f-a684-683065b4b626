import {
  endOfDay,
  format,
  getYear,
  isToday,
  isWithinInterval,
  isYesterday,
  parseISO,
  startOfDay,
} from "date-fns";

export const formatReadableDate = (d: Date) => {
  if (isToday(d)) {
    return `Today at ${format(d, "p")}`;
  }

  if (isYesterday(d)) {
    return `Yesterday at ${format(d, "p")}`;
  }

  const localizedDate = format(d, "PP").replace(`, ${getYear(d)}`, "");

  return `${localizedDate} at ${format(d, "p")}`;
};

export const formatDate = (d: Date | string) => {
  if (!d) return "N/A";

  try {
    const dateInput = typeof d === "string" ? parseISO(d) : d;
    return format(dateInput, "MM/dd/yyyy");
  } catch (error) {
    console.error("Error formatting date:", error);
    return "N/A";
  }
};

export const formatDateWithTimestamp = (d: Date | string) => {
  if (!d) return "N/A";

  try {
    const dateInput = typeof d === "string" ? parseISO(d) : d;
    return `${format(dateInput, "MM/dd/yyyy")} at ${format(dateInput, "p")}`;
  } catch (error) {
    console.error("Error formatting date with timestamp:", error);
    return "N/A";
  }
};

export function addTimeToDate(dateString: string) {
  const date = new Date(dateString);
  date.setHours(0, 0, 0, 0);
  return date.toISOString();
}

export function getInitials(fullName?: string): string {
  if (!fullName) return "-";

  return fullName
    .split(" ")
    .map((word) => word.charAt(0))
    .join("")
    .toUpperCase();
}

export function calculateCheckoutStatus(checkins: any[], membership: any[]) {
  if (!membership || membership.length === 0) {
    return "no-membership";
  }

  let currentMembership = getCurrentActiveMembership(membership);

  if (!currentMembership) {
    // Get the latest membership from the membership array
    currentMembership = membership[membership.length - 1];

    if (!currentMembership) {
      return "no-membership";
    }

    const membershipEndDate = new Date(currentMembership.end_date);
    const sixMonthsAgo = new Date();
    sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);

    if (isNaN(membershipEndDate.getTime())) {
      return "no-membership";
    }

    const availablePoints = Number(currentMembership.available_points) || 0;

    // Check if the membership end date is less than 6 months old
    if (membershipEndDate >= sixMonthsAgo) {
      return availablePoints > 0 ? "inactive" : "expired";
    }

    return "expired";
  }

  const now = new Date();
  const today = now.toISOString().split("T")[0];

  const membershipEndDate = new Date(currentMembership.end_date);
  const thirtyDaysAgo = new Date();
  thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

  // Ensure membershipEndDate is valid before comparison
  if (isNaN(membershipEndDate.getTime())) {
    return "no-membership";
  }

  const sixMonthsAgo = new Date();
  sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);

  if (membershipEndDate < sixMonthsAgo) {
    return "expired";
  }

  const availablePoints = Number(currentMembership.available_points) || 0;

  if (availablePoints <= 0 || membershipEndDate < thirtyDaysAgo) {
    return "inactive";
  }

  // Find today's check-in record
  const todayCheckIn = checkins.find((checkIn) => {
    if (!checkIn?.checkin_date) return false;
    const checkinDate = new Date(checkIn.checkin_date);
    if (isNaN(checkinDate.getTime())) return false;
    return checkinDate.toISOString().split("T")[0] === today;
  });

  if (!todayCheckIn) {
    return "not-checked-in";
  }

  if (todayCheckIn.checkout_date) {
    const checkoutDate = new Date(todayCheckIn.checkout_date);
    if (!isNaN(checkoutDate.getTime())) {
      if (checkoutDate.toISOString().split("T")[0] === today) {
        return "already-checked-out";
      }
    }
  }

  return "has-checked-in";
}

export function determineMembershipStatus(membership) {
  if (!membership) return "expired";

  const endDate = new Date(membership.end_date);
  const now = new Date();
  const sixMonthsAgo = new Date();
  sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);

  if (isNaN(endDate.getTime())) return "expired";

  // Check if the membership end date is more than 6 months ago, EXPIRED
  if (endDate <= sixMonthsAgo) return "expired";

  const availablePoints = Number(membership.available_points) || 0;

  // If end date is in the past but within 6 months, INACTIVE
  if (endDate < now) {
    return availablePoints > 0 ? "inactive" : "expired";
  }

  // If membership is still valid, ACTIVE
  if (availablePoints > 0) return "active";

  return "expired";
}

export const getCurrentActiveMembership = (memberships?: any[]) => {
  if (!memberships?.length) return undefined;

  return memberships
    ?.sort(
      (a, b) =>
        new Date(a.start_date).getTime() - new Date(b.start_date).getTime()
    )
    ?.find((membership) => {
      const endDate = new Date(membership.end_date);
      const today = new Date();
      return endDate >= today;
    });
};

export const isMembershipActive = (
  startDate?: string | Date,
  endDate?: string | Date
): boolean => {
  if (!startDate || !endDate) return false;

  try {
    const start =
      typeof startDate === "string" ? parseISO(startDate) : startDate;
    const end = typeof endDate === "string" ? parseISO(endDate) : endDate;
    const today = new Date();

    return isWithinInterval(today, {
      start: startOfDay(start),
      end: endOfDay(end),
    });
  } catch (error) {
    console.error("Error checking membership dates:", error);
    return false;
  }
};

export const getNoteStyles = (checkoutStatus?: string) => {
  const statusStyles: Record<string, string> = {
    "not-checked-in": "bg-teal-50 border-teal-200",
    "has-checked-in": "bg-green-50 border-green-200",
    inactive: "bg-orange-50 border-orange-200",
    expired: "bg-red-50 border-red-200",
    "no-membership": "bg-black/80 border-black text-white",
  };

  return `${
    statusStyles[checkoutStatus || ""] || "bg-gray-50 border-gray-200"
  }`;
};

export const playSound = () => {
  const audio = new Audio("/sounds/beep.wav");
  audio.play();
};

export const phoneTypes = ["Home", "Mobile", "Work", "Other"];

export const housingTypes = ["Own", "Rent", "Other"];

export const raceTypes = [
  "African American and White Combo",
  "American Indian",
  "American Indian and African American Combo",
  "American Indian and White Combo",
  "Asian",
  "Asian and White Combo",
  "Black",
  "No Data",
  "Other Multi-Racial",
  "Pacific Islander",
  "White",
];

export const genderTypes = ["Male", "Female", "Non-Binary", "Other"];

//todo: languages selection should be dynamic based on admin dashboard
export const languages = ["English", "Chinese", "Spanish"];
