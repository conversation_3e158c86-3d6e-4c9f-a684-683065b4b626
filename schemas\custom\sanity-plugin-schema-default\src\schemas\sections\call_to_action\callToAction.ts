import { rootSchema } from "@webriq-pagebuilder/sanity-plugin-schema-default"
import { MdCallToAction } from "react-icons/md"

// Images
import variantAImage from "./images/variant_a.jpg"
import variantBImage from "./images/variant_b.jpg"
import variantCImage from "./images/variant_c.jpg"
import variantDImage from "./images/variant_d.jpg"
import variantEImage from "./images/variant_e.jpg"
import variantFImage from "./images/variant_f.png"
import variantGImage from "./images/variant_g.png"
import initialValue from "./initialValue"
import { callToActionSchema } from "./schema"

export const variantsList = [
  {
    title: "Variant A",
    description: "Call to Action with logo and primary button",
    value: "variant_a",
    image: variantAImage.src,
  },
  {
    title: "Variant B",
    description: "Call to Action with logo and two form fields",
    value: "variant_b",
    image: variantBImage.src,
  },
  {
    title: "Variant C",
    description: "Call to Action with single form field and list of features",
    value: "variant_c",
    image: variantCImage.src,
  },
  {
    title: "Variant D",
    description: "Call to Action full sign up form",
    value: "variant_d",
    image: variantDImage.src,
  },
  {
    title: "Variant E",
    description: "Call to Action with form only",
    value: "variant_e",
    image: variantEImage.src,
  },
  {
    title: "Variant F",
    description: "Call to Action with form only",
    value: "variant_f",
    image: variantFImage.src,
  },
  {
    title: "Variant G",
    description: "Call to Action with form only",
    value: "variant_g",
    image: variantGImage.src,
  },
]

export default rootSchema(
  "callToAction",
  "Call to Action",
  MdCallToAction,
  variantsList,
  callToActionSchema,
  initialValue
)
