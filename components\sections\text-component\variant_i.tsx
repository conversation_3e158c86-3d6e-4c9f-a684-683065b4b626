import { PortableText } from "@portabletext/react";
import { Container } from "@stackshift-ui/container";
import { Flex } from "@stackshift-ui/flex";
import { Heading } from "@stackshift-ui/heading";
import { Section } from "@stackshift-ui/section";
import { TextComponentProps } from ".";
import { MyPortableTextComponents } from "types";

import Image from "next/image";
import { urlFor } from "lib/sanity";

export const textComponentBlockStyling: MyPortableTextComponents = {
  block: {
    h1: ({ children }) => (
      <Heading
        fontSize="3xl"
        weight="bold"
        className="mb-8 leading-normal text-black dark:text-white"
      >
        {children}
      </Heading>
    ),

    h2: ({ children }) => (
      <Heading
        type="h2"
        weight="bold"
        className="mb-10 !text-5xl leading-10 tracking-wide !text-cfc-gray dark:text-white"
      >
        {children}
      </Heading>
    ),
    h3: ({ children }) => (
      <Heading
        type="h3"
        fontSize="xl"
        weight="bold"
        className="mb-8 leading-normal text-black dark:text-white"
      >
        {children}
      </Heading>
    ),
    h4: ({ children }) => (
      <Heading
        type="h4"
        weight="bold"
        fontSize="lg"
        className="mb-8 leading-normal text-black dark:text-white"
      >
        {children}
      </Heading>
    ),

    normal: ({ children }) => (
      <p className="md:mb-8 mb-6 md:text-lg text-base font-light leading-relaxed text-gray-500">
        {children}
      </p>
    ),

    custom: ({ children }) => (
      <Heading
        type="h3"
        weight="extrabold"
        className="mb-8 md:!text-3xl !text-2xl tracking-wide leading-none text-white dark:text-white bg-cfc-secondary/65 rounded-2xl md:p-4 p-3"
      >
        {children}
      </Heading>
    ),

    blockquote: ({ children }) => (
      <blockquote className="mb-6 italic leading-loose text-gray-500 px-14">
        - {children}
      </blockquote>
    ),
  },
  code: ({ value }) => (
    <pre data-language={value.language}>
      <code>{value.code}</code>
    </pre>
  ),
  list: {
    bullet: ({ children }) => (
      <ul className="flex flex-col text-gray-500 md:text-base text-base font-light md:pl-20 pl-14 mb-8 space-y-4 leading-relaxed list-disc">
        {children}
      </ul>
    ),
    number: ({ children }) => (
      <ol className="flex flex-col pl-10 mb-8 space-y-4 leading-relaxed list-decimal">
        {children}
      </ol>
    ),
  },
  listItem: {
    bullet: ({ children }) => (
      <li className="leading-snug text-left text-gray-500 md:text-base text-base font-light">
        {children}
      </li>
    ),
    number: ({ children }) => <li className="leading-relaxed">{children}</li>,
  },
  marks: {
    strong: ({ children }) => <strong>{children}</strong>,
    em: ({ children }) => <em>{children}</em>,
    code: ({ children }) => <code>{children}</code>,
    link: ({ children, value }) => (
      <a
        className="hover:text-primary-foreground text-primary"
        href={value.href}
        target="_blank"
        rel="noopener noreferrer"
      >
        {children}
      </a>
    ),
  },
  types: {
    addImage: ({ value }) => (
      <Image
        className="w-full h-full mb-5"
        width={500}
        height={500}
        sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
        src={urlFor(value?.image)}
        alt={value?.alt ?? value?.image?.asset?._ref}
      />
    ),
  },
};

export default function TextComponent_I({
  heading,
  firstColumn,
  secondColumn
}: TextComponentProps) {
  return (
    <Section className="relative py-20">
      <Container maxWidth={1280} className="!px-0">
        <Flex wrap justify="center" className="mx-auto w-11/12 md:w-10/12">
          {heading && (
            <Heading
              type="h1"
              weight="extrabold"
              className="text-5xl !text-cfc-secondary"
            >
              {heading}
            </Heading>
          )}

          {firstColumn && (
            <div className="text-xs w-full text-center">
              <PortableText
                value={firstColumn}
                components={textComponentBlockStyling}
                onMissingComponent={false} // Disabling warnings / handling unknown types
              />
            </div>
          )}

          {secondColumn && (
            <div className="text-xs w-full md:w-10/12 mx-auto text-center md:px-4">
              <PortableText
                value={secondColumn}
                components={textComponentBlockStyling}
                onMissingComponent={false} // Disabling warnings / handling unknown types
              />
            </div>
          )}
        </Flex>
      </Container>
    </Section>
  );
}

export { TextComponent_I };
