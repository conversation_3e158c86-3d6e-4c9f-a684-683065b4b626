import React, {
  createContext,
  useState,
  useContext,
  useEffect,
  ReactNode,
} from "react";
import axios from "axios";
import { useAuthCheck } from "hooks/useAuthCheck";
import { supabaseClient } from "utils/supabase/client";

interface CheckedInTodayContextType {
  checkedInTodayData: any[];
  fetchingCheckedInToday: boolean;
  setFetchingCheckedInToday: React.Dispatch<React.SetStateAction<boolean>>;
  refreshData: () => void;
}

const CheckedInTodayContext = createContext<CheckedInTodayContextType | null>(
  null
);

export const useCheckedInToday = () => {
  const context = useContext(CheckedInTodayContext);
  if (!context) {
    throw new Error(
      "useCheckedInToday must be used within a CheckedInTodayProvider"
    );
  }
  return context;
};

interface CheckedInTodayProviderProps {
  children: ReactNode;
}

export const CheckedInTodayProvider: React.FC<CheckedInTodayProviderProps> = ({
  children,
}) => {
  const [checkedInTodayData, setCheckedInTodayData] = useState<any[]>([]);
  const [fetchingCheckedInToday, setFetchingCheckedInToday] =
    useState<boolean>(false);
  const { token } = useAuthCheck();

  const fetchData = async () => {
    setFetchingCheckedInToday(true);
    try {
      const response = await axios.get("/api/user-data/checked-in-today", {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
      });
      setCheckedInTodayData(response.data);
    } catch (error) {
      console.error("Error fetching checked in today data:", error);
    } finally {
      setFetchingCheckedInToday(false);
    }
  };

  const refreshData = () => {
    fetchData();
  };

  const fetchDataForMember = async (memberId: number) => {
    try {
      const response = await axios.get("/api/user-data/checked-in-today", {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        params: { memberId },
      });

      if (response.data.length) {
        return response.data[0];
      }
    } catch (error) {
      console.error(`Error fetching member data for ID ${memberId}:`, error);
      return null;
    }
  };

  const handleRealtimeUpdate = async (payload: any) => {
    const { eventType, new: newData, old: oldData } = payload;

    if (eventType === "INSERT" || eventType === "UPDATE") {
      const updatedMemberData = await fetchDataForMember(newData.member_id);
      if (updatedMemberData) {
        setCheckedInTodayData((prevData) => {
          const existingIndex = prevData.findIndex(
            (item) => item.id === updatedMemberData.id
          );
          if (existingIndex !== -1) {
            const updatedData = [...prevData];
            updatedData[existingIndex] = updatedMemberData;
            return updatedData;
          } else {
            return [updatedMemberData, ...prevData];
          }
        });
      }
    }

    if (eventType === "DELETE") {
      setCheckedInTodayData((prevData) =>
        prevData.filter((checkin) => checkin.id !== oldData.member_id)
      );
    }
  };

  useEffect(() => {
    fetchData();

    const checkinSubscription = supabaseClient
      .channel("checkins-realtime")
      .on(
        "postgres_changes",
        {
          event: "*",
          schema: "public",
          table: "checkins",
        },
        async (payload) => {
          await handleRealtimeUpdate(payload);
        }
      )
      .subscribe();

    return () => {
      supabaseClient.removeChannel(checkinSubscription);
    };
  }, []);

  return (
    <CheckedInTodayContext.Provider
      value={{
        checkedInTodayData,
        fetchingCheckedInToday,
        setFetchingCheckedInToday,
        refreshData,
      }}
    >
      {children}
    </CheckedInTodayContext.Provider>
  );
};
