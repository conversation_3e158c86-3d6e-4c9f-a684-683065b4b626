import { CustomButton } from "@/components/ui/custom-button";
import { Input } from "@/components/ui/input";
import { Plus, Search, Pencil, Trash2 } from "lucide-react";
import React, { useState, useEffect } from "react";
import TableFilters from "../table-filters";
import { Label } from "@/components/ui/label";
import { useAlert } from "context/useAlert";
import ConfirmationModal from "../confirmation-modal";
import { DataTable } from "@/components/data-table";

interface FeeGuideline {
  id: string;
  year: number;
  household_size: number;
  household_points: number;
  lower_threshold: number;
  middle_threshold: number;
  upper_threshold: number;
  lower_price: number;
  middle_price: number;
  upper_price: number;
  created_at: string;
}

export default function MembershipFeeGuidelines() {
  const [guidelines, setGuidelines] = useState<FeeGuideline[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [showForm, setShowForm] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [editingGuideline, setEditingGuideline] = useState<FeeGuideline | null>(
    null
  );
  const [guidelineToDelete, setGuidelineToDelete] =
    useState<FeeGuideline | null>(null);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const { showAlert } = useAlert();

  const [formData, setFormData] = useState({
    household_size: "",
    household_points: "",
    lower_threshold: "",
    middle_threshold: "",
    upper_threshold: "",
    lower_price: "",
    middle_price: "",
    upper_price: "",
  });

  useEffect(() => {
    fetchGuidelines();
  }, []);

  const fetchGuidelines = async () => {
    try {
      const response = await fetch("/api/admin/fee-guidelines");
      const data = await response.json();
      setGuidelines(Array.isArray(data) ? data : []);
    } catch (error) {
      showAlert("Error fetching guidelines", "error");
      setGuidelines([]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const resetForm = () => {
    setFormData({
      household_size: "",
      household_points: "",
      lower_threshold: "",
      middle_threshold: "",
      upper_threshold: "",
      lower_price: "",
      middle_price: "",
      upper_price: "",
    });
    setEditingGuideline(null);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      const url = "/api/admin/fee-guidelines";
      const method = editingGuideline ? "PUT" : "POST";
      const body = editingGuideline
        ? { id: editingGuideline.id, ...formData }
        : formData;

      console.log("🚀 ~ body", body);
      const response = await fetch(url, {
        method,
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(
          editingGuideline ? { id: editingGuideline.id, ...formData } : formData
        ),
      });

      if (!response.ok) {
        throw new Error("Failed to save guideline");
      }

      showAlert(
        `Guideline ${editingGuideline ? "updated" : "added"} successfully`,
        "success"
      );
      setShowForm(false);
      resetForm();
      fetchGuidelines();
    } catch (error) {
      showAlert("Error saving guideline", "error");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleEdit = (guideline: FeeGuideline) => {
    setEditingGuideline(guideline);
    setFormData({
      household_size: guideline.household_size.toString(),
      household_points: guideline.household_points.toString(),
      lower_threshold: guideline.lower_threshold.toString(),
      middle_threshold: guideline.middle_threshold.toString(),
      upper_threshold: guideline.upper_threshold.toString(),
      lower_price: guideline.lower_price.toString(),
      middle_price: guideline.middle_price.toString(),
      upper_price: guideline.upper_price.toString(),
    });
    setShowForm(true);
  };

  const handleDeleteClick = (guideline: FeeGuideline) => {
    setGuidelineToDelete(guideline);
    setShowDeleteModal(true);
  };

  const handleDeleteConfirm = async () => {
    if (!guidelineToDelete) return;

    setIsSubmitting(true);
    try {
      const response = await fetch("/api/admin/fee-guidelines", {
        method: "DELETE",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ id: guidelineToDelete.id }),
      });

      if (!response.ok) {
        throw new Error("Failed to delete guideline");
      }

      showAlert("Guideline deleted successfully", "success");
      setShowDeleteModal(false);
      setGuidelineToDelete(null);
      fetchGuidelines();
    } catch (error) {
      showAlert("Error deleting guideline", "error");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDeleteCancel = () => {
    setShowDeleteModal(false);
    setGuidelineToDelete(null);
  };

  const filteredGuidelines = guidelines.filter((guideline) =>
    guideline.household_size.toString().includes(searchTerm)
  );

  const headers = [
    "Household Size",
    "Points",
    "Lower Threshold",
    "Middle Threshold",
    "Upper Threshold",
    "Lower Price",
    "Middle Price",
    "Upper Price",
    "Actions",
  ];

  const renderRow = (guideline: FeeGuideline) => (
    <tr key={guideline.id}>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
        {guideline.household_size}
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
        {guideline.household_points}
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
        ${guideline.lower_threshold.toFixed(2)}
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
        ${guideline.middle_threshold.toFixed(2)}
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
        ${guideline.upper_threshold.toFixed(2)}
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
        ${guideline.lower_price.toFixed(2)}
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
        ${guideline.middle_price.toFixed(2)}
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
        ${guideline.upper_price.toFixed(2)}
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
        <div className="flex space-x-2 justify-center">
          <CustomButton
            variant="ghost"
            size="icon"
            onClick={() => handleEdit(guideline)}
          >
            <Pencil className="h-4 w-4" />
          </CustomButton>
          <CustomButton
            variant="ghost"
            size="icon"
            className="text-destructive"
            onClick={() => handleDeleteClick(guideline)}
          >
            <Trash2 className="h-4 w-4" />
          </CustomButton>
        </div>
      </td>
    </tr>
  );

  return (
    <div className="p-6 space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold">Membership Fee Guidelines</h2>
        <CustomButton
          variant="outline"
          onClick={() => {
            resetForm();
            setShowForm(true);
          }}
        >
          <Plus className="h-4 w-4 mr-2" />
          Add New Guideline
        </CustomButton>
      </div>

      <div className="flex gap-4 items-center">
        <div className="relative flex-1">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
          <Input
            placeholder="Search by household size..."
            className="pl-8"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        <TableFilters
          filters={{}}
          selectedFilters={{}}
          onFilterChange={() => console.log("filter")}
        />
      </div>

      {showForm && (
        <div className="mb-6 p-6 border rounded-lg bg-gray-50">
          <h3 className="text-lg font-medium mb-4">
            {editingGuideline ? "Edit Guideline" : "Create New Guideline"}
          </h3>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="household_size">Household Size</Label>
                <Input
                  id="household_size"
                  name="household_size"
                  type="number"
                  value={formData.household_size}
                  onChange={handleInputChange}
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="household_points">Household Points</Label>
                <Input
                  id="household_points"
                  name="household_points"
                  type="number"
                  value={formData.household_points}
                  onChange={handleInputChange}
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="lower_threshold">Lower Threshold</Label>
                <Input
                  id="lower_threshold"
                  name="lower_threshold"
                  type="number"
                  step="0.01"
                  value={formData.lower_threshold}
                  onChange={handleInputChange}
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="middle_threshold">Middle Threshold</Label>
                <Input
                  id="middle_threshold"
                  name="middle_threshold"
                  type="number"
                  step="0.01"
                  value={formData.middle_threshold}
                  onChange={handleInputChange}
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="upper_threshold">Upper Threshold</Label>
                <Input
                  id="upper_threshold"
                  name="upper_threshold"
                  type="number"
                  step="0.01"
                  value={formData.upper_threshold}
                  onChange={handleInputChange}
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="lower_price">Lower Price</Label>
                <Input
                  id="lower_price"
                  name="lower_price"
                  type="number"
                  step="0.01"
                  value={formData.lower_price}
                  onChange={handleInputChange}
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="middle_price">Middle Price</Label>
                <Input
                  id="middle_price"
                  name="middle_price"
                  type="number"
                  step="0.01"
                  value={formData.middle_price}
                  onChange={handleInputChange}
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="upper_price">Upper Price</Label>
                <Input
                  id="upper_price"
                  name="upper_price"
                  type="number"
                  step="0.01"
                  value={formData.upper_price}
                  onChange={handleInputChange}
                  required
                />
              </div>
            </div>

            <div className="flex justify-end space-x-2 pt-4">
              <CustomButton
                type="button"
                variant="outline"
                onClick={() => {
                  setShowForm(false);
                  resetForm();
                }}
              >
                Cancel
              </CustomButton>
              <CustomButton type="submit" disabled={isSubmitting}>
                {editingGuideline
                  ? isSubmitting
                    ? "Updating..."
                    : "Update Guideline"
                  : !editingGuideline && isSubmitting
                  ? "Creating..."
                  : "Create Guideline"}
              </CustomButton>
            </div>
          </form>
        </div>
      )}

      <DataTable
        headers={headers}
        data={filteredGuidelines}
        renderRow={renderRow}
        isLoading={isLoading}
      />

      <ConfirmationModal
        isOpen={showDeleteModal}
        message={`Are you sure you want to delete the guideline for household size ${guidelineToDelete?.household_size}?`}
        onConfirm={handleDeleteConfirm}
        onCancel={handleDeleteCancel}
        isLoading={isSubmitting}
      />
    </div>
  );
}
