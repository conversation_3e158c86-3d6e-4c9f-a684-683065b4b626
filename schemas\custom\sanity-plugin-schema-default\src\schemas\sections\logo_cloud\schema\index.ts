import {
  arrayOfImages,
  blockContentNormalStyle,
  plainText,
  primaryButton,
  title,
} from "../../../common/fields";
import { hideIfVariantIn } from "@webriq-pagebuilder/sanity-plugin-schema-default";

export const logoCloudSchema = [
  blockContentNormalStyle(
    "firstColumn",
    "Content",
    "Add text content in a single column.",
    hideIfVariantIn([
      "variant_a",
      "variant_b",
      "variant_c",
      "variant_d",
      "variant_e",
    ])
  ),
  title(hideIfVariantIn(["variant_d"])),

  plainText(
    hideIfVariantIn([
      "variant_a",
      "variant_c",
      "variant_d",
      "variant_e",
      "variant_f",
      "variant_g",
    ])
  ),
  arrayOfImages(),
  primaryButton(
    hideIfVariantIn([
      "variant_a",
      "variant_b",
      "variant_d",
      "variant_e",
      "variant_f",
      "variant_g",
    ])
  ),
];
