import { Container } from "@stackshift-ui/container";
import { Flex } from "@stackshift-ui/flex";
import { Heading } from "@stackshift-ui/heading";
import { Image } from "@stackshift-ui/image";
import { Section } from "@stackshift-ui/section";
import { Text } from "@stackshift-ui/text";
import React from "react";
import { FeaturesProps } from ".";
import { ArrayOfImageTitleAndText, Images } from "../../../types";

interface FeatureItemProps {
  feature: {
    caption?: string | null;
    title?: string | null;
    plainText?: string | null;
    mainImage?: Images;
  };
}

export default function Features_M({
  caption,
  title,
  features,
}: FeaturesProps) {
  return (
    <Section className="py-2 bg-[#EBECEE]">
      <Container maxWidth={1280}>
        <Container maxWidth={448} className="mb-2 text-center ">
          <CaptionAndTitleText title={title} />
        </Container>
        <Flex wrap className="max-w-[1100px] mx-auto">
          <FeatureItems features={features} />
        </Flex>
      </Container>
    </Section>
  );
}

function CaptionAndTitleText({
  caption,
  title,
}: {
  caption?: string;
  title?: string;
}) {
  return (
    <React.Fragment>
      {title ? (
        <Heading type="h2" className="!text-cfc-gray md:!text-4xl font-thin">
          {title}
        </Heading>
      ) : null}
    </React.Fragment>
  );
}

function FeatureItems({ features }: { features?: ArrayOfImageTitleAndText[] }) {
  if (!features) return null;

  return (
    <React.Fragment>
      {features?.map((feature) => {
        return <FeatureItem feature={feature} key={feature._key} />;
      })}
    </React.Fragment>
  );
}

function FeatureItem({ feature }: FeatureItemProps) {
  return (
    <div className="w-full p-7 md:w-1/3">

      {feature?.title ? (
        <Text weight="extrabold" className="mb-4 text-cfc-gray text-base text-center leading-none">
          {feature?.title}
        </Text>
      ) : null}

      {feature?.plainText ? (
        <Text muted className="leading-normal text-center">
          {feature?.plainText}
        </Text>
      ) : null}
    </div>
  );
}

// function FeatureImage({ feature }: FeatureItemProps) {
//   if (!feature?.mainImage?.image) return null;

//   return (
//     <span className="inline-block p-3 mb-4 rounded bg-secondary-foreground text-primary-foreground md:mb-6">
//       <Image
//         className="object-scale-down"
//         src={`${feature?.mainImage?.image}`}
//         width={40}
//         height={40}
//         alt={feature?.mainImage?.alt ?? `features-image-`}
//       />
//     </span>
//   );
// }

export { Features_M };
