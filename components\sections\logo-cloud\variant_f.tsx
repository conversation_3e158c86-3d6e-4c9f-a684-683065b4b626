import { Container } from "@stackshift-ui/container";
import { Flex } from "@stackshift-ui/flex";
import { Heading } from "@stackshift-ui/heading";
import { Image } from "@stackshift-ui/image";
import { Section } from "@stackshift-ui/section";
import React from "react";
import { LogoCloudProps } from ".";
import { Images } from "types";
import { Text } from "@stackshift-ui/text";

import { PortableText } from "@portabletext/react";
import { PortableTextBlock } from "sanity";
import { MyPortableTextComponents } from "types";

export const defaultBlockStyle: MyPortableTextComponents = {
  block: {
    h1: ({ children }) => (
      <Heading
        fontSize="3xl"
        weight="bold"
        className="mb-8 leading-normal text-black dark:text-white"
      >
        {children}
      </Heading>
    ),
    h2: ({ children }) => (
      <Heading
        type="h2"
        weight="bold"
        fontSize="2xl"
        className="mb-8 text-black dark:text-white"
      >
        {children}
      </Heading>
    ),
    h3: ({ children }) => (
      <Heading
        type="h3"
        fontSize="xl"
        weight="bold"
        className="mb-8 leading-normal text-black dark:text-white"
      >
        {children}
      </Heading>
    ),
    h4: ({ children }) => (
      <Heading
        type="h4"
        weight="bold"
        fontSize="lg"
        className="mb-8 leading-normal text-black dark:text-white"
      >
        {children}
      </Heading>
    ),
    normal: ({ children }) => (
      <Text className="md:text-lg text-sm leading-snug tracking-normal text-cfc-gray mb-10">
        {children}
      </Text>
    ),
    blockquote: ({ children }) => (
      <blockquote className="mb-6 italic leading-loose text-gray-500 px-14">
        - {children}
      </blockquote>
    ),
  },
  code: ({ value }) => (
    <pre data-language={value.language}>
      <code>{value.code}</code>
    </pre>
  ),
  list: {
    bullet: ({ children }) => (
      <ul className="flex flex-col pl-10 mb-8 space-y-4 leading-relaxed list-disc">
        {children}
      </ul>
    ),
    number: ({ children }) => (
      <ol className="flex flex-col pl-10 mb-8 space-y-4 leading-relaxed list-decimal">
        {children}
      </ol>
    ),
  },
  listItem: {
    bullet: ({ children }) => <li className="leading-relaxed">{children}</li>,
    number: ({ children }) => <li className="leading-relaxed">{children}</li>,
  },
  marks: {
    strong: ({ children }) => <strong>{children}</strong>,
    em: ({ children }) => <em>{children}</em>,
    code: ({ children }) => <code>{children}</code>,
    link: ({ children, value }) => (
      <a
        className="hover:text-primary-foreground text-primary"
        href={value.href}
        target="_blank"
        rel="noopener noreferrer"
      >
        {children}
      </a>
    ),
  },
  types: {
    addImage: ({ value }) => (
      <Image
        className="w-full h-full mb-5"
        width={500}
        height={500}
        sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
        src={urlFor(value?.image)}
        alt={value?.alt ?? value?.image?.asset?._ref}
      />
    ),
  },
};

export default function LogoCloud_F({
  title,
  images,
  firstColumn,
}: LogoCloudProps) {
  return (
    <Section className="py-20 bg-white">
      <Container maxWidth={1560}>
        <Flex wrap justify="center" className="max-w-5xl mx-auto">
          {firstColumn && (
            <div className="mb-2 text-xs w-full text-center">
              <PortableText
                value={firstColumn}
                components={defaultBlockStyle}
                onMissingComponent={false} // Disabling warnings / handling unknown types
              />
            </div>
          )}
        </Flex>

        <Title title={title} />

        <Flex
          wrap
          justify="center"
          align="center"
          className="max-w-4xl mx-auto"
        >
          <LogoCloudImages images={images} />
        </Flex>
      </Container>
    </Section>
  );
}

function Title({ title }: { title?: string }) {
  if (!title) return null;

  return (
    <Heading
      type="h2"
      weight="bold"
      className="mb-8 text-left lg:text-3xl leading-none tracking-tight text-2xl !text-cfc-secondary"
    >
      {title}
    </Heading>
  );
}

function LogoCloudImages({ images }: { images?: Images[] }) {
  if (!images) return null;

  return (
    <React.Fragment>
      {images?.map((image, index) => (
        <div className="w-full px-2 mb-4 md:w-1/2" key={index}>
          {image?.image ? (
            <Flex
              align="center"
              justify="center"
              //   className="mx-auto  h-[192px] w-[192px] rounded-global bg-white"
              className="mx-auto w-full rounded-global bg-white"
            >
              <Image
                className="object-cover"
                src={`${image?.image}`}
                sizes="1200px"
                width={1200}
                height={800}
                alt={image?.alt ?? `logoCloud-image${index}`}
              />
            </Flex>
          ) : null}
        </div>
      ))}
    </React.Fragment>
  );
}

export { LogoCloud_F };
