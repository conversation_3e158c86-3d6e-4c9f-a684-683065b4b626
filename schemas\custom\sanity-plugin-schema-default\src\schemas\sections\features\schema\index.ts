import {
  arrayOfImages,
  arrayOfImageTitleAndText,
  arrayOfTitleAndDescription,
  description,
  featuredItems,
  primaryButton,
  subtitle,
  tags,
  title,
} from "../../../common/fields";
import { hideIfVariantIn } from "@webriq-pagebuilder/sanity-plugin-schema-default";

export const featuresSchema = [
  subtitle(
    hideIfVariantIn([
      "variant_e",
      "variant_i",
      "variant_j",
      "variant_k",
      "variant_l",
      "variant_m",
    ])
  ),
  title(hideIfVariantIn(["variant_e", "variant_k"])),
  description(
    hideIfVariantIn([
      "variant_a",
      "variant_c",
      "variant_d",
      "variant_e",
      "variant_h",
      "variant_i",
      "variant_j",
      "variant_k",
      "variant_l",
      "variant_m",
    ])
  ),
  arrayOfImageTitleAndText(
    "Features",
    "Click the 'Add item' button to add a feature. If you want to edit added features, click this ⋮ icon found on its right.",
    hideIfVariantIn(["variant_e", "variant_f", "variant_g"])
  ),

  primaryButton(
    hideIfVariantIn([
      "variant_a",
      "variant_b",
      "variant_c",
      "variant_d",
      "variant_e",
      "variant_g",
      "variant_h",
      "variant_i",
      "variant_j",
      "variant_k",
      "variant_l",
      "variant_m",
    ])
  ),
  arrayOfImages(
    hideIfVariantIn([
      "variant_a",
      "variant_b",
      "variant_c",
      "variant_d",
      "variant_e",
      "variant_i",
      "variant_j",
      "variant_k",
      "variant_m",
    ])
  ),
  featuredItems(
    hideIfVariantIn([
      "variant_a",
      "variant_b",
      "variant_c",
      "variant_d",
      "variant_f",
      "variant_g",
      "variant_h",
      "variant_i",
      "variant_j",
      "variant_k",
      "variant_l",
      "variant_m",
    ])
  ),

  tags(
    "Featured Items",
    null,
    hideIfVariantIn([
      "variant_a",
      "variant_c",
      "variant_d",
      "variant_e",
      "variant_f",
      "variant_h",
      "variant_i",
      "variant_j",
      "variant_k",
      "variant_l",
      "variant_m",
    ])
  ),
];
