import { NextApiRequest, NextApiResponse } from "next";
import { calculateCheckoutStatus, determineMembershipStatus } from "utils";
import { supabaseAdmin } from "utils/supabase/admin";
import { supabaseClient } from "utils/supabase/client";

interface AuthUser {
  id: string;
  last_sign_in_at: string;
}

interface AuthUserMap {
  [key: string]: {
    last_sign_in_at: string;
  };
}

interface User {
  id: string;
  role: string;
  email: string;
  user_auth_id: string;
  username: string;
}

interface TableData {
  id: string;
  [key: string]: any;
}

const handleError = (res: NextApiResponse, message: string, error: any) => {
  console.error(message, error);
  return res.status(500).json({ message, error: error.message });
};

const fetchAuthUsers = async () => {
  const {
    data: { users },
    error,
  } = await supabaseAdmin.listUsers();

  if (error) {
    throw new Error(`Error fetching auth users: ${error.message}`);
  }

  return (users as AuthUser[]).reduce<AuthUserMap>(
    (acc, user) => ({
      ...acc,
      [user.id]: {
        last_sign_in_at: user.last_sign_in_at,
      },
    }),
    {}
  );
};

const fetchDataFromTable = async (
  table: string,
  select: string,
  range: { start: number; end: number },
  order?: string,
  filter?: { column: string; value: string | string[] }
) => {
  // First, get the total count
  const countQuery = supabaseClient
    .from(table)
    .select("*", { count: "exact", head: true });

  if (filter) {
    if (Array.isArray(filter.value)) {
      countQuery.in(filter.column, filter.value);
    } else {
      countQuery.eq(filter.column, filter.value);
    }
  }

  const { count, error: countError } = await countQuery;

  if (countError) {
    throw new Error(
      `Error fetching count from ${table}: ${countError.message}`
    );
  }

  // Then fetch the actual data for the current page
  let query = supabaseClient
    .from(table)
    .select(select)
    .order(order ?? "created_at", { ascending: false });

  // Apply filters before pagination
  if (filter) {
    if (Array.isArray(filter.value)) {
      query = query.in(filter.column, filter.value);
    } else {
      query = query.eq(filter.column, filter.value);
    }
  }

  // Apply range last
  query = query.range(range.start, range.end);

  const { data, error } = await query;

  if (error) {
    throw new Error(`Error fetching data from ${table}: ${error.message}`);
  }

  return { data: data || [], count };
};

const updateMembershipStatus = async (membership: any) => {
  const newStatus = determineMembershipStatus(membership);

  const { error } = await supabaseClient
    .from("memberships")
    .update({ status: newStatus })
    .eq("id", membership.id);

  if (error) console.error("Error updating membership status:", error);
  return { ...membership, status: newStatus };
};

const mapMembersWithMemberships = async (
  users: any[],
  members: any[],
  memberships: any[],
  payments: any[],
  notes: any[],
  checkins: any[],
  orders: any[],
  allMembers: any[],
  additionalInfo: any[]
) => {
  const membershipsWithPayments = memberships.map((membership) => {
    const addedByMember = allMembers.find((m) => m.id === membership.added_by);

    return {
      ...membership,
      payments: payments.filter((pay) => pay.membership_id === membership.id),
      added_by: addedByMember
        ? `${addedByMember.first_name} ${addedByMember.last_name}`
        : "Unknown",
    };
  });

  return Promise.all(
    members.map(async (member) => {
      const memberNotes = notes.filter((note) => note.member_id === member.id);
      const memberCheckins = checkins
        .filter((c) => c.member_id === member.id)
        .map((checkin) => ({
          ...checkin,
          checked_in_by:
            members.find((m) => m.id === checkin.checked_in_by)?.username ||
            "Unknown",
        }));

      const memberMemberships = await Promise.all(
        membershipsWithPayments
          .filter((m) => m.member_id === member.id)
          .map(async (m) => await updateMembershipStatus(m))
      );

      const userStatus = users.find((user) => user.id === member.user_id);

      const memberAdditionalInfo = additionalInfo?.find(
        (a) => a.member_id === member.id
      )
        ? {
            ...additionalInfo.find((a) => a.member_id === member.id),
            personal_info: additionalInfo.find((a) => a.member_id === member.id)
              ?.personal_info
              ? JSON.parse(
                  additionalInfo.find((a) => a.member_id === member.id)
                    .personal_info
                )
              : {},
            age_groups: additionalInfo.find((a) => a.member_id === member.id)
              ?.age_groups
              ? JSON.parse(
                  additionalInfo.find((a) => a.member_id === member.id)
                    .age_groups
                )
              : {},
            household_members: additionalInfo.find(
              (a) => a.member_id === member.id
            )?.household_members
              ? JSON.parse(
                  additionalInfo.find((a) => a.member_id === member.id)
                    .household_members
                )
              : [],
          }
        : null;

      return {
        ...member,
        memberships: memberMemberships.sort(
          (a, b) =>
            new Date(b.start_date).getTime() - new Date(a.start_date).getTime()
        ),
        status: userStatus?.status,
        orders: orders.filter((order) => order.member_id === member.id),
        notes: memberNotes,
        check_in: memberCheckins,
        checkoutStatus: calculateCheckoutStatus(
          memberCheckins,
          memberMemberships
        ),
        additionalInfo: memberAdditionalInfo,
      };
    })
  );
};

const addRolesToMembers = (members: any[], users: any[], authUsers: any) => {
  return members.map((member) => {
    const user = users.find((u) => u.id === member.user_id);
    return {
      ...member,
      role: user?.role,
      email: user?.email,
      username: user?.username,
      last_login: user?.user_auth_id
        ? authUsers[user.user_auth_id]?.last_sign_in_at || null
        : null,
      user_auth_id: user?.user_auth_id,
    };
  });
};

export default async function GET(req: NextApiRequest, res: NextApiResponse) {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const pageSize = parseInt(req.query.pageSize as string) || 20;
    const start = (page - 1) * pageSize;
    const end = start + pageSize - 1;

    const range = { start, end };
    const role = req.query.role
      ? Array.isArray(req.query.role)
        ? req.query.role
        : [req.query.role]
      : undefined;

    // Fetch users and their IDs concurrently
    const [usersData, authUsers] = await Promise.all([
      fetchDataFromTable(
        "users",
        "id, role, email, user_auth_id, username, status",
        range,
        undefined,
        role ? { column: "role", value: role } : undefined
      ),
      fetchAuthUsers(),
    ]);

    const userIds = (usersData.data as unknown as TableData[]).map(
      (user) => user.id
    );

    // Only proceed if we have user IDs
    if (userIds.length > 0) {
      // Fetch related data concurrently
      const [
        membersData,
        membershipsData,
        paymentsData,
        notesData,
        checkinsData,
        ordersData,
        allMembersData,
        additionalInfoData,
      ] = await Promise.all([
        fetchDataFromTable(
          "members",
          "*",
          { start: 0, end: userIds.length - 1 },
          undefined,
          { column: "user_id", value: userIds }
        ),
        fetchDataFromTable("memberships", "*", range, "start_date"),
        fetchDataFromTable(
          "payments",
          "id, amount, method, date, membership_id",
          { start: 0, end: 999 },
          "date"
        ),
        fetchDataFromTable(
          "notes",
          "id, created_at, member_id, subject, description, modified_by",
          { start: 0, end: 999 }
        ),
        fetchDataFromTable(
          "checkins",
          "id, member_id, checkin_date, checkout_date, checked_in_by, checked_out_by, is_forced_checked_in",
          range,
          undefined
        ),
        fetchDataFromTable(
          "orders",
          "*",
          { start: 0, end: 999 },
          "date_of_order"
        ),
        fetchDataFromTable("members", "id, first_name, last_name", {
          start: 0,
          end: 9999,
        }),
        fetchDataFromTable("additional_info", "*", {
          start: 0,
          end: 9999,
        }),
      ]);

      const authUsers = await fetchAuthUsers();

      const membersWithMemberships = await mapMembersWithMemberships(
        usersData.data,
        membersData.data,
        membershipsData.data,
        paymentsData.data,
        notesData.data,
        checkinsData.data,
        ordersData.data,
        allMembersData.data,
        additionalInfoData.data
      );

      const validUsers = (Array.isArray(usersData.data)
        ? usersData.data
        : []) as unknown as User[];
      const filteredMembers = membersWithMemberships.filter((member) =>
        validUsers.some((user) => user.id === member.user_id)
      );

      const combinedData = addRolesToMembers(
        filteredMembers,
        usersData.data,
        authUsers
      );

      return res.status(200).json({
        data: combinedData || [],
        pagination: {
          total: usersData.count || 0,
          page,
          pageSize,
          totalPages: Math.ceil((usersData.count || 0) / pageSize),
        },
      });
    }
  } catch (error) {
    return handleError(
      res,
      "Failed to process the request due to an unexpected error.",
      error
    );
  }
}
