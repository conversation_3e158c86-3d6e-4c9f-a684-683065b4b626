import React from "react";
import AdminDashboard from "@/components/dashboard/admin/admin-dashboard";
import { DashboardNav } from "@/components/dashboard/dashboard-nav";
import { useAuthCheck } from "hooks/useAuthCheck";

export default function AdminDashboardPage() {
  const { user, loading } = useAuthCheck();

  const hasAccess = ["admin", "staff"].includes(user?.user_metadata?.role);

  return (
    <div className="min-h-screen bg-background">
      <DashboardNav />
      {loading ? null : hasAccess ? (
        <AdminDashboard />
      ) : user ? (
        <div className="text-center text-red-500 h-screen flex items-center justify-center">
          Access Denied: Your role does not have access to this page.
        </div>
      ) : null}
    </div>
  );
}
