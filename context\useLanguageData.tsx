import React, {
  createContext,
  useState,
  useContext,
  useEffect,
  ReactNode,
} from "react";
import axios from "axios";
import { useAuthCheck } from "hooks/useAuthCheck";
import { supabaseClient } from "utils/supabase/client";

interface LanguageContextType {
  languageData: any[];
  fetchingLanguage: boolean;
  setFetchingLanguage: React.Dispatch<React.SetStateAction<boolean>>;
  refreshData: () => void;
}

const LanguageContext = createContext<LanguageContextType | null>(null);

export const useLanguageData = () => {
  const context = useContext(LanguageContext);
  if (!context) {
    throw new Error("useMemberData must be used within a MemberDataProvider");
  }
  return context;
};

interface LanguageDataProviderProps {
  children: ReactNode;
}

interface LanguageData {
  id: number;
  name: string;
  created_at: string;
}

export const LanguageDataProvider: React.FC<LanguageDataProviderProps> = ({
  children,
}) => {
  const [languageData, setLanguageData] = useState<LanguageData[]>([]);
  const [fetchingLanguage, setFetchingLanguage] = useState<boolean>(false);
  const { token } = useAuthCheck();

  const fetchLanguage = async () => {
    setFetchingLanguage(true);
    try {
      const response = await axios.get(`/api/admin/language`, {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
      });

      setLanguageData(response.data);
    } catch (error) {
      console.error("Error fetching language data:", error);
    } finally {
      setFetchingLanguage(false);
    }
  };

  const refreshData = () => {
    fetchLanguage();
  };

  const handleRealtimeUpdate = async (payload: any) => {
    const { eventType, new: newData, old: oldData } = payload;

    if (eventType === "INSERT") {
      setLanguageData((prevData) => [newData, ...prevData]);
    }

    if (eventType === "UPDATE") {
      setLanguageData((prevData) =>
        prevData.map((item) => (item.id === newData.id ? newData : item))
      );
    }

    if (eventType === "DELETE") {
      setLanguageData((prevData) =>
        prevData.filter((item) => item.id !== oldData.id)
      );
    }
  };

  useEffect(() => {
    refreshData();

    const couponSubscription = supabaseClient
      .channel("realtime_language")
      .on(
        "postgres_changes",
        { event: "*", schema: "public", table: "languages" },
        handleRealtimeUpdate
      )
      .subscribe();

    return () => {
      supabaseClient.removeChannel(couponSubscription);
    };
  }, []);

  return (
    <LanguageContext.Provider
      value={{
        languageData,
        fetchingLanguage,
        setFetchingLanguage,
        refreshData,
      }}
    >
      {children}
    </LanguageContext.Provider>
  );
};
