import dynamic from "next/dynamic";
import { AskedQuestion, FaqsWithCategory, SectionsProps } from "types";
import * as FaqsVariants from "@stackshift-ui/faqs";


const Variants = {
   variant_a: FaqsVariants.Faqs_A,
   variant_b: FaqsVariants.Faqs_B,
   variant_c: FaqsVariants.Faqs_C,
   variant_d: dynamic(() => import("./variant_d")),
};

export interface FAQProps {
  subtitle?: string;
  title?: string; 
  faqs?: AskedQuestion[];
  faqsWithCategories?: FaqsWithCategory[];
}

const displayName = "Faqs";

export const Faqs: React.FC<SectionsProps> = ({ data }) => {
  const variant = data?.variant;
  const Variant = variant && Variants[variant as keyof typeof Variants];

  const props = {
    subtitle: data?.variants?.subtitle ?? undefined,
    title: data?.variants?.title ?? undefined,
    faqs:  data?.variants?.askedQuestions ?? undefined,
    faqsWithCategories: data?.variants?.faqsWithCategory ?? undefined,
  };

  return Variant ? <Variant {...props} /> : null;
};

Faqs.displayName = displayName;
