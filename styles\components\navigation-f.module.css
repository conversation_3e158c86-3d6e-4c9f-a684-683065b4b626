
.AccordionRoot {
    border-radius: 6px;
    width: 300px;
    background-color: var(--mauve-6);
    box-shadow: 0 2px 10px var(--black-a4);
  }
  
  .AccordionItem {
    overflow: hidden;
    margin-top: 1px;
  }
  
  .AccordionItem:first-child {
    margin-top: 0;
  }
  
  .AccordionContent {
    @apply p-3;
    overflow: hidden;
  }
  .AccordionContent[data-state='open'] {
    animation: slideDown 300ms cubic-bezier(0.87, 0, 0.13, 1);
  }
  .AccordionContent[data-state='closed'] {
    animation: slideUp 300ms cubic-bezier(0.87, 0, 0.13, 1);
  }
  
  .AccordionChevron{
    @apply flex-none ml-3;
  }
  
  .AccordionTrigger[data-state='open'] > .AccordionChevron {
    transform: rotate(180deg);
  }
  
  @keyframes slideDown {
    from {
      height: 0;
    }
    to {
      height: var(--radix-accordion-content-height);
    }
  }
  
  @keyframes slideUp {
    from {
      height: var(--radix-accordion-content-height);
    }
    to {
      height: 0;
    }
  }