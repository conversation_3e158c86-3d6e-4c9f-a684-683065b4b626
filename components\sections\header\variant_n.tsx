import { Button } from "@stackshift-ui/button";
import { Container } from "@stackshift-ui/container";
import { Flex } from "@stackshift-ui/flex";
import { Heading } from "@stackshift-ui/heading";
import { Image } from "@stackshift-ui/image";
import { Section } from "@stackshift-ui/section";
import { Text } from "@stackshift-ui/text";
import React from "react";
import { ButtonProps, HeaderProps } from ".";
import { urlFor } from "lib/sanity";

export default function Header_N({
  mainImage,
  title,
  description,
  primaryButton,
  secondaryButton,
  bgImage,
}: HeaderProps) {
  return (
    <Section
      className="w-full relative py-24 !px-0"
      style={{
        backgroundImage: `url(${urlFor(bgImage)})`,
        backgroundRepeat: "no-repeat",
        // backgroundSize: "113%",
        backgroundSize: "cover",
        backgroundPosition: "center",
      }}
    >
      <Container maxWidth={1560}>
        <Flex className="w-full flex h-full items-center justify-center">
          <TitleAndDescription title={title} description={description} />
          {/* <div className="w-full basis-1/2 flex items-center mx-auto">
            <MainImage mainImage={mainImage} />
          </div> */}
        </Flex>
      </Container>
    </Section>
  );
}

function TitleAndDescription({
  title,
  description,
}: {
  title?: string;
  description?: string;
}) {
  return (
    <div className="px-8">
      {title && (
        <Heading
          type="h1"
          className="font-bold text-center font-heading md:font-extrabold leading-none !text-white !text-6xl md:tracking-wide !drop-shadow-md"
        >
          {title}
        </Heading>
      )}
    </div>
  );
}

function Buttons({
  primaryButton,
  secondaryButton,
}: {
  primaryButton?: ButtonProps;
  secondaryButton?: ButtonProps;
}) {
  return (
    <Flex
      align="center"
      justify="center"
      gap={2}
      direction="col"
      className="lg:justify-start md:flex-row"
    >
      {primaryButton?.label && (
        <Button as="link" link={primaryButton} ariaLabel={primaryButton?.label}>
          {primaryButton?.label}
        </Button>
      )}

      {secondaryButton?.label && (
        <Button
          as="link"
          link={secondaryButton}
          className="bg-secondary hover:bg-secondary/50 inline-block rounded-global font-bold transition duration-200 px-3 py-4"
          ariaLabel={secondaryButton?.label}
        >
          {secondaryButton?.label}
        </Button>
      )}
    </Flex>
  );
}

interface MainImageProps {
  mainImage?: {
    image?: string | any;
    alt?: string;
  };
}

function MainImage({ mainImage }: MainImageProps) {
  if (!mainImage?.image) return null;

  return (
    <div className="relative w-full max-w-md mx-auto">
      <Image
        className=" object-cover w-full lg:w-[500px] relative z-10 md:scale-110"
        src={`${mainImage.image}`}
        sizes="(min-width: 520px) 520px, 90vw"
        width={800}
        height={500}
        alt={mainImage.alt ?? "header-main-image"}
      />
    </div>
  );
}

export { Header_N };
