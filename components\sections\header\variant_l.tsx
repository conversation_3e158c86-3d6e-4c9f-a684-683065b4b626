import { Button } from "@stackshift-ui/button";
import { Container } from "@stackshift-ui/container";
import { Flex } from "@stackshift-ui/flex";
import { Heading } from "@stackshift-ui/heading";
import { Image } from "@stackshift-ui/image";
import { Section } from "@stackshift-ui/section";
import { Text } from "@stackshift-ui/text";
import React from "react";
import { ButtonProps, HeaderProps } from ".";
import { PortableText } from "@portabletext/react";
import { urlFor } from "lib/sanity";
import { MyPortableTextComponents } from "types";

export const defaultBlockStyle: MyPortableTextComponents = {
  block: {
    h1: ({ children }) => (
      <Heading
        fontSize="3xl"
        weight="bold"
        className="mb-8 leading-normal text-black dark:text-white"
      >
        {children}
      </Heading>
    ),
    h2: ({ children }) => (
      <Heading
        type="h2"
        weight="bold"
        fontSize="2xl"
        className="mb-8 text-black dark:text-white"
      >
        {children}
      </Heading>
    ),
    h3: ({ children }) => (
      <Heading
        type="h3"
        fontSize="xl"
        weight="bold"
        className="mb-8 leading-normal text-black dark:text-white"
      >
        {children}
      </Heading>
    ),
    h4: ({ children }) => (
      <Heading
        type="h4"
        weight="bold"
        fontSize="lg"
        className="mb-8 leading-normal text-black dark:text-white"
      >
        {children}
      </Heading>
    ),
    normal: ({ children }) => (
      <Text className="md:text-base text-sm leading-relaxed mb-4 tracking-normal text-cfc-gray">
        {children}
      </Text>
    ),
    blockquote: ({ children }) => (
      <blockquote className="mb-6 italic leading-loose text-gray-500 px-14">
        - {children}
      </blockquote>
    ),
  },
  code: ({ value }) => (
    <pre data-language={value.language}>
      <code>{value.code}</code>
    </pre>
  ),
  list: {
    bullet: ({ children }) => (
      <ul className="flex flex-col pl-10 mb-8 space-y-4 leading-relaxed list-disc">
        {children}
      </ul>
    ),
    number: ({ children }) => (
      <ol className="flex flex-col pl-10 mb-8 space-y-4 leading-relaxed list-decimal">
        {children}
      </ol>
    ),
  },
  listItem: {
    bullet: ({ children }) => <li className="leading-relaxed">{children}</li>,
    number: ({ children }) => <li className="leading-relaxed">{children}</li>,
  },
  marks: {
    strong: ({ children }) => <strong>{children}</strong>,
    em: ({ children }) => <em>{children}</em>,
    code: ({ children }) => <code>{children}</code>,
    link: ({ children, value }) => (
      <a
        className="hover:text-primary-foreground text-primary"
        href={value.href}
        target="_blank"
        rel="noopener noreferrer"
      >
        {children}
      </a>
    ),
  },
  types: {
    addImage: ({ value }) => (
      <Image
        className="w-full h-full mb-5"
        width={500}
        height={500}
        sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
        src={urlFor(value?.image)}
        alt={value?.alt ?? value?.image?.asset?._ref}
      />
    ),
    addIframe: ({ value }) => {
      return (
        <div className="relative flex">
          <iframe
            loading="lazy"
            frameBorder="0"
            src={value?.iframe}
            width="100%"
            data-rocket-lazyload="fitvidscompatible"
            className="entered w-full h-[50rem]"
          />
        </div>
      );
    },
  },
};

export default function Header_L({
  mainImage,
  title,
  description,
  primaryButton,
  secondaryButton,
  firstColumn,
  secondColumn,
}: HeaderProps) {
  return (
    <Section className="py-20 bg-white">
      <Container maxWidth={1280} className="!px-0">
        <Flex
          align="start"
          gap={4}
          className="w-11/12 mx-auto flex-col md:flex-row"
        >
          <Flex
            align="center"
            direction="col"
            className="w-full basis-1/2 px-0"
          >
            <Flex wrap justify="center" className="mx-auto !px-0">
              {firstColumn && (
                <div className="mb-2 text-xs w-full text-center">
                  <PortableText
                    value={firstColumn}
                    components={defaultBlockStyle}
                    onMissingComponent={false} // Disabling warnings / handling unknown types
                  />
                </div>
              )}
            </Flex>
            <div className="w-full mx-auto text-center lg:text-left mb-4">
              <MainImage mainImage={mainImage} />
              <TitleAndDescription title={title} description={description} />
            </div>

            {primaryButton?.label && (
              <Button
                as="link"
                link={primaryButton}
                ariaLabel={primaryButton?.label}
                className="bg-cfc-primary text-white hover:bg-cfc-primary-foreground inline-block rounded-global font-bold transition duration-200 px-8 py-4"
              >
                {primaryButton?.label}
              </Button>
            )}
          </Flex>

          <div className="w-full flex h-full basis-1/2 flex-col justify-center items-center">
            <div className="mx-auto !px-0 w-full">
              {secondColumn && (
                <div className="mb-2 text-xs w-full text-center">
                  <PortableText
                    value={secondColumn}
                    components={defaultBlockStyle}
                    onMissingComponent={false} // Disabling warnings / handling unknown types
                  />
                </div>
              )}
            </div>

            {secondaryButton?.label && (
              <Button
                as="link"
                link={secondaryButton}
                className="bg-cfc-primary text-center text-white hover:bg-cfc-primary-foreground inline-block rounded-global font-bold transition duration-200 px-8 py-4"
                ariaLabel={secondaryButton?.label}
              >
                {secondaryButton?.label}
              </Button>
            )}
          </div>
        </Flex>
        {/* 
        <Buttons
          primaryButton={primaryButton}
          secondaryButton={secondaryButton}
        /> */}
      </Container>
    </Section>
  );
}

function TitleAndDescription({
  title,
  description,
}: {
  title?: string;
  description?: string;
}) {
  return (
    <React.Fragment>
      {title && (
        <Heading fontSize="3xl" className="mb-3">
          {title}
        </Heading>
      )}
      {description && (
        <Text muted className="my-2 text-xs text-center">
          {description}
        </Text>
      )}
    </React.Fragment>
  );
}

function Buttons({
  primaryButton,
  secondaryButton,
}: {
  primaryButton?: ButtonProps;
  secondaryButton?: ButtonProps;
}) {
  return (
    <Flex
      align="center"
      justify="center"
      gap={2}
      direction="col"
      className="w-full lg:justify-around md:flex-row md:w-11/12 px-4"
    >
      {primaryButton?.label && (
        <Button
          as="link"
          link={primaryButton}
          ariaLabel={primaryButton?.label}
          className="bg-cfc-primary text-white hover:bg-cfc-primary-foreground inline-block rounded-global font-bold transition duration-200 px-8 py-4"
        >
          {primaryButton?.label}
        </Button>
      )}

      {secondaryButton?.label && (
        <Button
          as="link"
          link={secondaryButton}
          className="bg-cfc-primary text-white hover:bg-cfc-primary-foreground inline-block rounded-global font-bold transition duration-200 px-8 py-4"
          ariaLabel={secondaryButton?.label}
        >
          {secondaryButton?.label}
        </Button>
      )}
    </Flex>
  );
}

interface MainImageProps {
  mainImage?: {
    image?: string | any;
    alt?: string;
  };
}

function MainImage({ mainImage }: MainImageProps) {
  if (!mainImage?.image) return null;

  return (
    <div className="relative w-full">
      <Image
        className="overflow-hidden object-cover relative z-10 w-full"
        src={`${mainImage.image}`}
        // sizes="(min-width: 520px) 448px, 90vw"
        width={448}
        height={448}
        alt={mainImage.alt ?? "header-main-image"}
      />
    </div>
  );
}

// function DecorativeImages() {
//   return (
//     <>
//       <ImageElement
//         src="/assets/elements/webriq-blue-dark-up.png"
//         alt="webriq-blue-dark-up-mainImage-element"
//         className="absolute hidden md:block"
//         style={{ top: "-2rem", right: "3rem", zIndex: 2 }}
//         width={112}
//         height={112}
//       />
//       <ImageElement
//         src="/assets/elements/wing-webriq-blue-down.png"
//         alt="wing-webriq-blue-down-mainImage-element"
//         className="absolute hidden md:block"
//         style={{ bottom: "-2rem", right: "-2rem", zIndex: 2 }}
//         width={144}
//         height={144}
//       />
//       <ImageElement
//         src="/assets/elements/bullets-gray-right.svg"
//         alt="bullets-gray-right-mainImage-element"
//         className="absolute hidden md:block"
//         style={{ top: "3rem", right: "-3rem", zIndex: 2 }}
//         width={115}
//         height={157}
//       />
//       <ImageElement
//         src="/assets/elements/bullets-gray-left.svg"
//         alt="bullets-gray-left-mainImage-element"
//         className="absolute hidden md:block"
//         style={{ bottom: "2.5rem", left: "-4.5rem", zIndex: 2 }}
//         width={157}
//         height={115}
//       />
//     </>
//   );
// }

// function ImageElement({
//   src,
//   alt,
//   className,
//   style,
//   width,
//   height,
// }: {
//   src: string;
//   alt: string;
//   className?: string;
//   style?: React.CSSProperties;
//   width: number;
//   height: number;
// }) {
//   return (
//     <Image src={src} alt={alt} className={className} style={style} width={width} height={height} />
//   );
// }

export { Header_L };
