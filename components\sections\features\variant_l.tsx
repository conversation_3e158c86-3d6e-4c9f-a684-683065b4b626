import { Container } from "@stackshift-ui/container";
import { Flex } from "@stackshift-ui/flex";
import { Heading } from "@stackshift-ui/heading";
import { Image } from "@stackshift-ui/image";
import { Section } from "@stackshift-ui/section";
import { Text } from "@stackshift-ui/text";
import React from "react";
import { FeaturesProps } from ".";
import { ArrayOfImageTitleAndText, Images } from "types";
import { urlFor } from "lib/sanity";

import { Swiper, SwiperSlide } from "swiper/react";
import { Navigation, Thumbs, Pagination, A11y } from "swiper/modules";

// Import Swiper styles
import "swiper/css";
import "swiper/css/navigation";
import "swiper/css/thumbs";
import "swiper/css/pagination";
import "swiper/css/a11y";

interface FeatureItemProps {
  feature: {
    caption?: string | null;
    title?: string | null;
    plainText?: string | null;
    mainImage?: Images;
  };
}

export default function Features_L({
  caption,
  title,

  images,
}: FeaturesProps) {
  return (
    <Section className="py-20 bg-white">
      <Container maxWidth={1280}>
        {/* <Container maxWidth={448} className="mb-4 text-center ">
          <CaptionAndTitleText caption={caption} title={title} />
        </Container> */}
        <Swiper
          navigation={{
            prevEl: "#piprev",
            nextEl: "#pinext",
          }}
          modules={[Thumbs, Navigation, Pagination, A11y]}
          spaceBetween={20}
          slidesPerView={1}
          loop={true}
          speed={500}
          watchSlidesProgress={true}
        >
          {images &&
            images?.map((item, index) => (
              <SwiperSlide key={index}>
                <div className="h-full">
                  <Image
                    width="1360"
                    height="900"
                    quality={100}
                    src={item?.image}
                    style={{
                      objectFit: "cover",
                      objectPosition: "center",
                    }}
                    alt={item?.alt ?? `product-image-${index + 1}`}
                  />
                </div>
              </SwiperSlide>
            ))}
        </Swiper>
      </Container>
    </Section>
  );
}

function CaptionAndTitleText({
  caption,
  title,
}: {
  caption?: string;
  title?: string;
}) {
  return (
    <React.Fragment>
      {caption ? (
        <Text weight="bold" className="text-cfc-secondary">
          {caption}
        </Text>
      ) : null}
      {title ? (
        <Heading
          type="h2"
          fontSize="4xl"
          weight="thin"
          className="text-center !text-cfc-secondary"
        >
          {title}
        </Heading>
      ) : null}
    </React.Fragment>
  );
}

export { Features_L };
