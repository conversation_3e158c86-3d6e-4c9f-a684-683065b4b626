import React, {
  createContext,
  useState,
  useContext,
  useEffect,
  ReactNode,
} from "react";
import axios from "axios";
import { useAuthCheck } from "hooks/useAuthCheck";
import { supabaseClient } from "utils/supabase/client";

interface Product {
  id: string;
  product_name: string;
  product_code: string;
  category: string;
  unit_points: number;
  order_max_quantity: number;
  monthly_max_quantity: number;
  is_active: boolean;
  icon?: {
    base64Data: string;
    fileName: string;
    fileType: string;
  };
}

interface ProductContextType {
  productData: Product[];
  fetchingProduct: boolean;
  setFetchingProduct: React.Dispatch<React.SetStateAction<boolean>>;
  refreshProduct: () => void;
}

const ProductContext = createContext<ProductContextType | null>(null);

export const useProductData = () => {
  const context = useContext(ProductContext);
  if (!context) {
    throw new Error("useProductData must be used within a ProductDataProvider");
  }
  return context;
};

interface ProductDataProviderProps {
  children: ReactNode;
}

export const ProductDataProvider: React.FC<ProductDataProviderProps> = ({
  children,
}) => {
  const [productData, setProductData] = useState<Product[]>([]);
  const [fetchingProduct, setFetchingProduct] = useState<boolean>(false);
  const { user, token } = useAuthCheck();

  const fetchData = async () => {
    setFetchingProduct(true);
    try {
      const response = await axios.get("/api/admin/products/get-product", {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        params: { userId: user?.id },
      });

      const formattedResults = response.data.map((product: any) => ({
        ...product,
        category: product.categoryData?.name || "Unknown",
      }));

      setProductData(formattedResults);
    } catch (error) {
      console.error("Error fetching product data:", error);
    } finally {
      setFetchingProduct(false);
    }
  };

  const refreshProduct = () => {
    fetchData();
  };

  const handleRealtimeUpdate = (payload: any) => {
    const { eventType, new: newData, old: oldData } = payload;

    setProductData((prevProducts) => {
      switch (eventType) {
        case "INSERT":
          return [newData, ...prevProducts];
        case "UPDATE":
          return prevProducts.map((product) =>
            product.id === newData.id ? newData : product
          );
        case "DELETE":
          return prevProducts.filter((product) => product.id !== oldData.id);
        default:
          return prevProducts;
      }
    });
  };

  useEffect(() => {
    if (user) {
      fetchData();

      const productSubscription = supabaseClient
        .channel("products-realtime")
        .on(
          "postgres_changes",
          {
            event: "*",
            schema: "public",
            table: "products",
          },
          (payload) => {
            console.log("Real-time change received!", payload);
            handleRealtimeUpdate(payload);
          }
        )
        .subscribe();

      return () => {
        supabaseClient.removeChannel(productSubscription);
      };
    }
  }, [user]);

  return (
    <ProductContext.Provider
      value={{
        productData,
        fetchingProduct,
        setFetchingProduct,
        refreshProduct,
      }}
    >
      {children}
    </ProductContext.Provider>
  );
};
