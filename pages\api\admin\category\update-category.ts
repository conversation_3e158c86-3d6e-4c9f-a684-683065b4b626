import { NextApiRequest, NextApiResponse } from "next";
import { supabaseClient } from "utils/supabase/client";

export default async function PUT(req: NextApiRequest, res: NextApiResponse) {
  try {
    const { id, name, description, userId, image_url } = req.body;

    const { data: userData, error: userError } = await supabaseClient
      .from("users")
      .select("role")
      .eq("user_auth_id", userId)
      .single();

    if (userError) {
      return res.status(404).json({
        message: "User not found",
      });
    }

    if (!userData || userData?.role !== "admin") {
      return res.status(403).json({
        message: "Unauthorized. Only admin can update category data.",
      });
    }

    const { error: categoryError } = await supabaseClient
      .from("category")
      .update({
        name,
        description,
        updated_at: new Date().toISOString(),
        icon: image_url,
      })
      .eq("id", id)
      .select()
      .single();

    if (categoryError) {
      console.error("Error product category data:", categoryError.message);
      return res.status(500).json({
        message: "Error updating product category data to the database",
        error: categoryError.message,
      });
    }

    return res.status(200).json({
      message: "Insert product category data successfully!",
    });
  } catch (error) {
    console.error(error);
    return res.status(500).json({
      message: "Failed to process the request.",
    });
  }
}
