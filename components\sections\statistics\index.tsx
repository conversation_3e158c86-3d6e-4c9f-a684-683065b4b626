import { lazy } from "react";
import dynamic from "next/dynamic";
import { SectionsProps, StatItems } from "../../../types";
import * as StatisticsVariants from "@stackshift-ui/statistics";

const Variants = {
  variant_a: StatisticsVariants.Statistics_A,
  variant_b: StatisticsVariants.Statistics_B,
  // variant_c: StatisticsVariants.Statistics_C,
  variant_d: dynamic(() => import("./variant_d")),
  variant_e: dynamic(() => import("./variant_e")),
};

export interface StatsProps {
  stats?: StatItems[];
}

const displayName = "Statistics";

export const Statistics: React.FC<SectionsProps> = ({ data }) => {
  const variant = data?.variant;
  const Variant = variant && Variants[variant as keyof typeof Variants];

  const props = {
    stats: (data?.variants?.stats || data?.variants?.statItems) ?? undefined,
  };

  return Variant ? <Variant {...props} /> : null;
};

Statistics.displayName = displayName;
