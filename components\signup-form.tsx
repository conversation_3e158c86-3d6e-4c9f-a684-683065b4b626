import React, { useEffect, useState } from "react";
import { CustomButton } from "@/components/ui/custom-button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { AlertCircle, CheckCircle2, Eye, EyeOff, Info } from "lucide-react";
import { useRouter } from "next/router";
import { useAlert } from "context/useAlert";
import { useAuthCheck } from "hooks/useAuthCheck";
import {
  calculateMembershipPrice,
  calculatePoints,
} from "utils/membership-calculator";
import { useMemberData } from "context/useMemberData";
import { RequiredLabel } from "./ui/required-label";
import { QualificationModal } from "@/components/ui/qualification-modal";
import { Tooltip } from "react-tooltip";

export default function SignupForm() {
  const router = useRouter();
  const { showAlert } = useAlert();
  const { user, token } = useAuthCheck();
  const { refreshData } = useMemberData();
  const [isPending, setIsPending] = useState(false);
  const [status, setStatus] = useState<{ message: string; success: boolean }>({
    message: "",
    success: false,
  });
  const [role, setRole] = useState("member");
  const [passwordError, setPasswordError] = useState("");
  const [householdSize, setHouseholdSize] = useState<number | null>(null);
  const [annualIncome, setAnnualIncome] = useState<number | null>(null);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [totalPoints, setTotalPoints] = useState<number>(0);
  const [amountPaid, setAmountPaid] = useState<number | null>(null);
  const [showQualificationModal, setShowQualificationModal] = useState(false);

  useEffect(() => {
    if (householdSize) {
      const calculatePointsAsync = async () => {
        const points = await calculatePoints(householdSize || 1);
        setTotalPoints(points);
      };
      calculatePointsAsync();
    }
  }, [householdSize]);

  useEffect(() => {
    if (householdSize && annualIncome) {
      const calculatePriceAsync = async () => {
        const prices = await calculateMembershipPrice(
          householdSize,
          annualIncome
        );
        setAmountPaid(prices[0] || null);
      };
      calculatePriceAsync();
    } else {
      setAmountPaid(null);
    }
  }, [householdSize, annualIncome]);

  const handleSignup = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    setIsPending(true);

    if (amountPaid === null) {
      setShowQualificationModal(true);
      setIsPending(false);
      return;
    }

    const formData = new FormData(event.currentTarget);
    const data = Object.fromEntries(formData.entries());
    const authorizedShoppers = Object.keys(data)
      .filter((key) => key.startsWith("authorizedShopper-"))
      .map((key) => data[key]);

    try {
      const response = await fetch(`/api/user-data`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({
          ...data,
          totalPoints,
          authorizedShoppers: authorizedShoppers.join(","),
        }),
      });

      const responseData = await response.json();

      if (!response.ok) {
        setStatus({
          message: responseData?.message,
          success: false,
        });
        showAlert(responseData?.message, "error");
        throw new Error(responseData.message || "An error occurred");
      }

      setStatus({
        message: "Account created successfully!",
        success: true,
      });
      showAlert("Account created successfully!", "success");
      router.push("/membership/create-user?success=true");
    } catch (error) {
      setStatus({
        message: "Failed to create account. Please try again.",
        success: false,
      });
    } finally {
      setIsPending(false);
      refreshData();
    }
  };

  const isStaffAndAdmin = role === "staff" || role === "admin";
  const LabelComponent = isStaffAndAdmin ? Label : RequiredLabel;

  return (
    <>
      <div className="min-h-screen p-4 flex items-center justify-center bg-gray-50">
        <Card className="w-full max-w-2xl">
          <CardHeader>
            {status.message && (
              <div
                className={`flex items-center gap-2 p-3 rounded-md ${
                  status.success
                    ? "bg-green-50 text-green-700"
                    : "bg-red-50 text-red-700"
                }`}
              >
                {status.success ? (
                  <CheckCircle2 className="h-5 w-5" />
                ) : (
                  <AlertCircle className="h-5 w-5" />
                )}
                {status.message}
              </div>
            )}
            <CardTitle className="text-2xl">User Creation</CardTitle>
          </CardHeader>

          <CardContent>
            <form onSubmit={handleSignup} className="space-y-6">
              <div className="space-y-2">
                <RequiredLabel htmlFor="role">Role</RequiredLabel>
                <Select
                  name="role"
                  required
                  defaultValue="member"
                  onValueChange={(value) => setRole(value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select a role" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="member">Member</SelectItem>
                    {user?.user_metadata?.role === "admin" && (
                      <>
                        <SelectItem value="staff">Staff</SelectItem>
                        <SelectItem value="admin">Admin</SelectItem>
                      </>
                    )}
                  </SelectContent>
                </Select>
              </div>
              <div className="grid gap-4 md:grid-cols-3">
                <div className="space-y-2">
                  <RequiredLabel htmlFor="firstName">First Name</RequiredLabel>
                  <Input
                    id="firstName"
                    name="firstName"
                    type="text"
                    required
                    placeholder="John"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="middleInitial">Middle Initial</Label>
                  <Input
                    id="middleInitial"
                    name="middleInitial"
                    type="text"
                    placeholder="C"
                  />
                </div>
                <div className="space-y-2">
                  <RequiredLabel htmlFor="lastName">Last Name</RequiredLabel>
                  <Input
                    id="lastName"
                    name="lastName"
                    type="text"
                    required
                    placeholder="Doe"
                  />
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <RequiredLabel htmlFor="username">Username</RequiredLabel>
                  <Input
                    id="username"
                    name="username"
                    placeholder="Username"
                    required
                  />
                </div>
                <div className="space-y-2">
                  <div className="flex items-center space-x-1">
                    <div data-tooltip-id="email-tooltip">
                      <Info className="w-4 h-4 cursor-pointer" />
                    </div>
                    <RequiredLabel htmlFor="email">Email</RequiredLabel>
                  </div>

                  <Input
                    id="email"
                    name="email"
                    type="email"
                    placeholder="<EMAIL>"
                    required
                  />

                  <Tooltip id="email-tooltip" place="right">
                    You may add a dummy email if user does not have one.
                  </Tooltip>
                </div>
              </div>
              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <RequiredLabel htmlFor="password">Password</RequiredLabel>
                  <div className="relative">
                    <Input
                      id="password"
                      name="password"
                      type={showPassword ? "text" : "password"}
                      required
                      minLength={6}
                      className="pr-10"
                      onChange={(e) => {
                        const confirmPassword =
                          e.currentTarget.form?.querySelector<HTMLInputElement>(
                            '[name="confirmPassword"]'
                          );
                        if (confirmPassword && confirmPassword.value) {
                          setPasswordError(
                            e.target.value === confirmPassword.value
                              ? ""
                              : "Passwords do not match"
                          );
                        }
                      }}
                    />
                    <button
                      type="button"
                      onClick={() => setShowPassword(!showPassword)}
                      className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-500 hover:text-gray-700"
                      tabIndex={-1}
                    >
                      {showPassword ? (
                        <EyeOff className="h-4 w-4" />
                      ) : (
                        <Eye className="h-4 w-4" />
                      )}
                    </button>
                  </div>
                </div>
                <div className="space-y-2">
                  <RequiredLabel htmlFor="confirmPassword">
                    Confirm Password
                  </RequiredLabel>
                  <div className="relative">
                    <Input
                      id="confirmPassword"
                      name="confirmPassword"
                      type={showConfirmPassword ? "text" : "password"}
                      required
                      className="pr-10"
                      onChange={(e) => {
                        const password =
                          e.currentTarget.form?.querySelector<HTMLInputElement>(
                            '[name="password"]'
                          );
                        setPasswordError(
                          e.target.value === password?.value
                            ? ""
                            : "Passwords do not match"
                        );
                      }}
                    />
                    <button
                      type="button"
                      onClick={() =>
                        setShowConfirmPassword(!showConfirmPassword)
                      }
                      className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-500 hover:text-gray-700"
                      tabIndex={-1}
                    >
                      {showConfirmPassword ? (
                        <EyeOff className="h-4 w-4" />
                      ) : (
                        <Eye className="h-4 w-4" />
                      )}
                    </button>
                  </div>
                  {passwordError && (
                    <p className="text-sm text-red-500">{passwordError}</p>
                  )}
                </div>
              </div>
              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <RequiredLabel htmlFor="phone">Phone Number</RequiredLabel>
                  <Input
                    id="phone"
                    name="phone"
                    type="tel"
                    placeholder="(*************"
                    required
                  />
                </div>
              </div>
              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <RequiredLabel htmlFor="address">Address</RequiredLabel>
                  <Textarea
                    id="address"
                    name="address"
                    placeholder="Enter your full address"
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="secondaryAddress">Secondary Address</Label>
                  <Textarea
                    id="secondaryAddress"
                    name="secondaryAddress"
                    placeholder="Enter your Secondary Address"
                  />
                </div>
              </div>

              <div className="grid gap-4 md:grid-cols-3">
                <div className="space-y-2">
                  <LabelComponent htmlFor="city">City</LabelComponent>
                  <Input
                    type="text"
                    id="city"
                    name="city"
                    placeholder="City"
                    required={!isStaffAndAdmin}
                  />
                </div>

                <div className="space-y-2">
                  <LabelComponent htmlFor="state">State</LabelComponent>
                  <Input
                    type="text"
                    id="state"
                    name="state"
                    placeholder="State"
                    required={!isStaffAndAdmin}
                  />
                </div>

                <div className="space-y-2">
                  <LabelComponent htmlFor="zipCode">Zip Code</LabelComponent>
                  <Input
                    type="text"
                    id="zipCode"
                    name="zipCode"
                    placeholder="Zip Code"
                    required={!isStaffAndAdmin}
                  />
                </div>
              </div>

              {!["admin", "staff"].includes(role) && (
                <div className="space-y-2">
                  <RequiredLabel htmlFor="paymentOption">
                    Payment Method
                  </RequiredLabel>
                  <Select name="paymentOption" required defaultValue="cash">
                    <SelectTrigger>
                      <SelectValue placeholder="Select payment method" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="cash">Cash</SelectItem>
                      <SelectItem value="credit">Credit Card</SelectItem>
                      <SelectItem value="check">Check</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              )}

              {!["admin", "staff"].includes(role) && (
                <div className="space-y-4">
                  <Label>Authorized Shoppers</Label>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {[0, 1, 2].map((index) => (
                      <div key={index} className="space-y-2">
                        <Label htmlFor={`authorizedShopper-${index}`}>
                          Shopper #{index + 1}
                        </Label>
                        <Input
                          id={`authorizedShopper-${index}`}
                          name={`authorizedShopper-${index}`}
                          type="text"
                          placeholder={`Authorized Shopper ${index + 1}`}
                        />
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {!["admin", "staff"].includes(role) && (
                <div className="space-y-2">
                  <Label htmlFor="headOfHousehold">Head of Household</Label>
                  <RadioGroup
                    name="isHeadOfHousehold"
                    defaultValue="no"
                    required
                  >
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="yes" id="yes" />
                      <Label htmlFor="yes">Yes</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="no" id="no" />
                      <Label htmlFor="no">No</Label>
                    </div>
                  </RadioGroup>
                </div>
              )}
              {!["admin", "staff"].includes(role) && (
                <React.Fragment>
                  <div className="space-y-2">
                    <RequiredLabel htmlFor="householdSize">
                      Household Size
                    </RequiredLabel>
                    <div className="relative">
                      <Input
                        id="householdSize"
                        name="householdSize"
                        type="number"
                        min="1"
                        step="1"
                        placeholder="1"
                        required
                        onChange={(e) =>
                          setHouseholdSize(
                            e.target.value ? Number(e.target.value) : null
                          )
                        }
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <RequiredLabel htmlFor="annualIncome">
                      Annual Income
                    </RequiredLabel>
                    <div className="relative">
                      <span className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-500">
                        $
                      </span>
                      <Input
                        id="annualIncome"
                        name="annualIncome"
                        type="number"
                        min="0"
                        step="1"
                        placeholder="0.00"
                        className="pl-7"
                        required
                        onChange={(e) =>
                          setAnnualIncome(
                            e.target.value ? Number(e.target.value) : null
                          )
                        }
                      />
                    </div>
                  </div>
                </React.Fragment>
              )}

              {householdSize && annualIncome && (
                <React.Fragment>
                  <div className="space-y-2">
                    <Label htmlFor="amountPaid">Amount Paid</Label>
                    <div className="relative">
                      <span className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-500">
                        $
                      </span>
                      <Input
                        id="amountPaid"
                        name="amountPaid"
                        type="number"
                        required
                        className="pl-7 bg-gray-100"
                        value={amountPaid || ""}
                        disabled
                      />
                    </div>
                    {amountPaid && (
                      <p className="text-sm text-gray-500">
                        Based on household size of {householdSize} and annual
                        income of ${annualIncome.toLocaleString()}
                      </p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="total_points">Total Points</Label>
                    <Input
                      type="number"
                      id="total_points"
                      name="total_points"
                      required
                      min="1"
                      className="w-full bg-gray-100"
                      disabled
                      value={totalPoints}
                    />
                  </div>
                </React.Fragment>
              )}
              <CustomButton
                type="submit"
                className="w-full"
                disabled={isPending || passwordError?.length > 0}
              >
                {isPending ? "Submitting..." : "Sign Up"}
              </CustomButton>
            </form>
          </CardContent>
        </Card>
      </div>

      <QualificationModal
        isOpen={showQualificationModal}
        onClose={() => setShowQualificationModal(false)}
      />
    </>
  );
}
