import { NextApiRequest, NextApiResponse } from "next";
import { getCurrentActiveMembership } from "utils";
import { supabaseClient } from "utils/supabase/client";

export default async function POST(req: NextApiRequest, res: NextApiResponse) {
  try {
    const {
      productId,
      quantity,
      totalPoints,
      unitPoints,
      reason,
      role,
      store_location,
      member_id,
      auth_user,
      membership_id,
      orderId,
      itemIndex,
    } = req.body;

    if (!["admin", "staff"].includes(role)) {
      return res.status(403).json({
        message: "Unauthorized. Only staff or admin can return item data.",
      });
    }

    const { data: userData, error: userError } = await supabaseClient
      .from("users")
      .select("id, user_auth_id, members (id)")
      .eq("user_auth_id", auth_user)
      .single();

    if (userError || !userData) {
      return res.status(404).json({
        message: "User or member not found",
      });
    }

    const memberId = userData?.members[0]?.id;
    if (!memberId) {
      return res.status(404).json({
        message: "Member not associated with this user",
      });
    }

    const { data: membershipData, error: membershipError } =
      await supabaseClient
        .from("memberships")
        .select("*")
        .eq("member_id", String(member_id));

    if (membershipError || !membershipData?.length) {
      return res.status(404).json({
        message: "No active membership found for this member.",
      });
    }

    const currentMembership = getCurrentActiveMembership(membershipData);
    if (!currentMembership) {
      return res.status(404).json({
        message: "No active membership found.",
      });
    }

    const { data: orderData, error: orderError } = await supabaseClient
      .from("orders")
      .select("id, items")
      .eq("id", orderId)
      .single();

    if (orderError || !orderData) {
      return res.status(404).json({
        message: "Order not found",
      });
    }

    const updatedItems = [...orderData.items];
    const currentItem = updatedItems[itemIndex];

    if (!currentItem || currentItem.product_id !== productId) {
      return res.status(400).json({
        message: "Item not found in the specified order",
      });
    }

    const currentReturnedQuantity = currentItem.returned_quantity || 0;
    const newReturnedQuantity = currentReturnedQuantity + quantity;

    updatedItems[itemIndex] = {
      ...currentItem,
      returned_quantity: newReturnedQuantity,
      returned_date: new Date().toISOString(),
      return_reason: reason,
    };

    const allItemsReturned = updatedItems.every(
      (item) => (item.returned_quantity || 0) >= item.quantity
    );

    if (allItemsReturned) {
      const { error: deleteOrderError } = await supabaseClient
        .from("orders")
        .delete()
        .eq("id", orderId);

      if (deleteOrderError) {
        console.error("Error deleting order:", deleteOrderError);
        return res.status(500).json({
          message: "Error deleting order after all items returned",
          error: deleteOrderError.message,
        });
      }
    } else {
      const { error: updateOrderError } = await supabaseClient
        .from("orders")
        .update({
          items: updatedItems,
          updated_at: new Date().toISOString(),
        })
        .eq("id", orderId);

      if (updateOrderError) {
        console.error("Error updating order items:", updateOrderError);
        return res.status(500).json({
          message: "Error updating order items",
          error: updateOrderError.message,
        });
      }
    }

    const { error: returnError } = await supabaseClient
      .from("return_items")
      .insert({
        product_id: productId,
        quantity: quantity,
        total_points: totalPoints,
        unit_points: unitPoints,
        reason,
        store_location,
        member_id,
        membership_id,
      });

    if (returnError) {
      console.error("Error inserting return item:", returnError.message);
      return res.status(500).json({
        message: "Error inserting return item data to the database",
        error: returnError.message,
      });
    }

    const updatedAvailablePoints =
      Number(currentMembership.available_points) + Number(totalPoints);
    const updatedUsedPoints =
      Number(currentMembership.used_points) - Number(totalPoints);

    const { error: updateMembershipError } = await supabaseClient
      .from("memberships")
      .update({
        available_points: updatedAvailablePoints.toString(),
        updated_store_location: store_location,
        updated_by: memberId,
        used_points: updatedUsedPoints,
      })
      .eq("id", currentMembership.id);

    if (updateMembershipError) {
      console.error(
        "Error updating available points:",
        updateMembershipError.message
      );
      return res.status(500).json({
        message: "Error updating available points in the database",
        error: updateMembershipError.message,
      });
    }

    return res.status(200).json({
      message: `Return item processed successfully! ${quantity} item(s) returned.${
        allItemsReturned ? " Order removed as all items were returned." : ""
      }`,
      returnedQuantity: quantity,
      updatedAvailablePoints,
      orderDeleted: allItemsReturned,
    });
  } catch (error) {
    console.error("Unexpected error:", error);
    return res.status(500).json({
      message: "Failed to process the request.",
    });
  }
}
