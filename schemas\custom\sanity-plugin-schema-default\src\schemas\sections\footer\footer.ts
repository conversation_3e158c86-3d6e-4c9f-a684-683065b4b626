import { rootSchema } from "@webriq-pagebuilder/sanity-plugin-schema-default"
import { MdVerticalAlignBottom } from "react-icons/md"

// Images
import variantAImage from "./images/variant_a.jpg"
import variantBImage from "./images/variant_b.jpg"
import variantCImage from "./images/variant_c.jpg"
import variantDImage from "./images/variant_d.jpg"
import variantEImage from "./images/variant_e.png"

import initialValue from "./initialValue"
import { footerSchema } from "./schema"

export const variantsList = [
  {
    title: "Variant A",
    description: "Footer with text and contact details",
    value: "variant_a",
    image: variantAImage.src,
  },
  {
    title: "Variant B",
    description: "Footer with menu and social media logo links",
    value: "variant_b",
    image: variantBImage.src,
  },
  {
    title: "Variant C",
    description: "Footer with copyright, menu and social media logo links",
    value: "variant_c",
    image: variantCImage.src,
  },
  {
    title: "Variant D",
    description:
      "Footer with text, multiple menus, and social media logo links",
    value: "variant_d",
    image: variantDImage.src,
  },
  {
    title: "Variant E",
    description:
      "Footer with text, multiple menus, and social media logo links",
    value: "variant_e",
    image: variantEImage.src,
  },
]

export default rootSchema(
  "footer",
  "Footer",
  MdVerticalAlignBottom,
  variantsList,
  footerSchema,
  initialValue
)
