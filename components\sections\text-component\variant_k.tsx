import { PortableText } from "@portabletext/react";
import { Container } from "@stackshift-ui/container";
import { Flex } from "@stackshift-ui/flex";
import { Heading } from "@stackshift-ui/heading";
import { Section } from "@stackshift-ui/section";
import { TextComponentProps } from ".";
import { MyPortableTextComponents } from "types";

export const textComponentBlockStyling: MyPortableTextComponents = {
  block: {
    h1: ({ children }) => (
      <Heading
        fontSize="3xl"
        weight="bold"
        className="mb-8 leading-normal text-black dark:text-white"
      >
        {children}
      </Heading>
    ),
    h2: ({ children }) => (
      <Heading
        type="h2"
        weight="light"
        fontSize="4xl"
        className="mb-8 !text-white !leading-none dark:text-white px-4"
      >
        {children}
      </Heading>
    ),
    h3: ({ children }) => (
      <Heading
        type="h3"
        fontSize="xl"
        weight="bold"
        className="mb-8 leading-normal text-black dark:text-white"
      >
        {children}
      </Heading>
    ),
    h4: ({ children }) => (
      <Heading
        type="h4"
        weight="bold"
        fontSize="lg"
        className="mb-8 leading-normal text-black dark:text-white"
      >
        {children}
      </Heading>
    ),
    normal: ({ children }) => (
      <p className="py-6 md:leading-normal leading-normal font-light tracking-wide text-white md:text-2xl text-xl px-4">{children}</p>
    ),

    blockquote: ({ children }) => (
      <blockquote className="mb-6 italic leading-loose text-gray-500 px-14">
        - {children}
      </blockquote>
    ),
  },

  code: ({ value }) => (
    <pre data-language={value.language}>
      <code>{value.code}</code>
    </pre>
  ),
  list: {
    bullet: ({ children }) => (
      <ul className="flex flex-col pl-10 mb-8 space-y-4 leading-relaxed list-disc">
        {children}
      </ul>
    ),
    number: ({ children }) => (
      <ol className="flex flex-col pl-10 mb-8 space-y-4 leading-relaxed list-decimal">
        {children}
      </ol>
    ),
  },
  listItem: {
    bullet: ({ children }) => <li className="leading-relaxed">{children}</li>,
    number: ({ children }) => <li className="leading-relaxed">{children}</li>,
  },
  marks: {
    strong: ({ children }) => <strong>{children}</strong>,
    em: ({ children }) => <em>{children}</em>,
    code: ({ children }) => <code>{children}</code>,
    link: ({ children, value }) => (
      <a
        className="hover:text-primary-foreground text-primary"
        href={value.href}
        target="_blank"
        rel="noopener noreferrer"
      >
        {children}
      </a>
    ),
  },

};

export default function TextComponent_K({
  heading,
  firstColumn,
}: TextComponentProps) {
  return (
    <Section className="py-10 relative overflow-hidden">
      <Container maxWidth={1280}>
        <div className="mx-auto max-w-5xl">
        {heading && (
          <Heading
            type="h2"
            weight="thin"
            className="mb-5 md:text-4xl text-left !text-black"
          >
            {heading}
          </Heading>
        )}

        <Flex wrap justify="center" className="relative mx-auto bg-cfc-secondary">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 1000 100"
            preserveAspectRatio="none"
            className="absolute -top-1 w-full left-0 wave-svg h-16"
          >
            <path
              className="elementor-shape-fill"
              d="M1000,4.3V0H0v4.3C0.9,23.1,126.7,99.2,500,100S1000,22.7,1000,4.3z"
            ></path>
          </svg>
          {firstColumn && (
            <div className="mb-2 md:py-40 py-32 text-xs w-full text-center md:px-10 px-4">
              <PortableText
                value={firstColumn}
                components={textComponentBlockStyling}
                onMissingComponent={false} // Disabling warnings / handling unknown types
              />
            </div>
          )}
          <svg
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 1000 100"
            preserveAspectRatio="none"
            className="absolute -bottom-1 w-full left-0 wave-svg-bottom wave-svg h-16"
          >
            <path
              className="elementor-shape-fill"
              d="M1000,4.3V0H0v4.3C0.9,23.1,126.7,99.2,500,100S1000,22.7,1000,4.3z"
            ></path>
          </svg>
        </Flex>
        </div>

      </Container>
    </Section>
  );
}

export { TextComponent_K };
