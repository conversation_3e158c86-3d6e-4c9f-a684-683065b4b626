import React from "react";
import { formatDate } from "utils";
import { useSelectClient } from "context/useSelectClientContext";
import { getCurrentActiveMembership } from "utils";
import ReadOnlyField from "@/components/ui/read-only-field";
import { useClientSearch } from "context/useClientSearchContext";
export default function CurrentMembershipData() {
  const { selectedClient } = useSelectClient();
  const { isSearching } = useClientSearch();
  const status = getCurrentActiveMembership(selectedClient?.memberships)
    ?.status;
  const endDate = getCurrentActiveMembership(selectedClient?.memberships)
    ?.end_date;
  const availablePoints = getCurrentActiveMembership(
    selectedClient?.memberships
  )?.available_points;

  const fields = [
    [
      {
        label: "Status",
        value: status?.charAt(0).toUpperCase() + status?.slice(1) || "N/A",
      },
      {
        label: "End Date",
        value: formatDate(endDate),
      },
    ],
    [
      {
        label: "Available Points",
        value: `${availablePoints ? `${availablePoints}` : "N/A"}`,
      },
    ],
  ];

  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
      {fields.map((row, rowIndex) => (
        <React.Fragment key={rowIndex}>
          {row.map((field, fieldIndex) => (
            <ReadOnlyField
              key={`${rowIndex}-${fieldIndex}`}
              label={field.label}
              value={field.value}
              isLoading={isSearching}
            />
          ))}
        </React.Fragment>
      ))}
    </div>
  );
}
