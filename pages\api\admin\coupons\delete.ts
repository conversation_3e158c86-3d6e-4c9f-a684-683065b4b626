import { NextApiRequest, NextApiResponse } from "next";
import { supabaseClient } from "utils/supabase/client";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  const API_DISABLED = true;

  if (API_DISABLED) {
    return res.status(503).json({ message: "API temporarily disabled." });
  }

  if (req.method !== "DELETE") {
    return res.status(405).json({ message: "Method not allowed" });
  }
  try {
    const { userRole, id } = req.body;

    if (!["staff", "admin"].includes(userRole as string)) {
      return res.status(403).json({ message: "Unauthorized" });
    }

    const { error } = await supabaseClient
      .from("coupons")
      .delete()
      .eq("id", id);

    if (error) {
      return res.status(500).json({
        message: "Error deleting coupon data from the database",
        error: error.message,
      });
    }

    return res.status(200).json({ message: "Coupon deleted successfully" });
  } catch (error) {
    return res.status(500).json({
      message: "Failed to process the request.",
    });
  }
}
