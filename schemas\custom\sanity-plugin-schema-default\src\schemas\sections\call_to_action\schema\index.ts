// import { defaultLogo } from "../../../common/defaults";
import {
  formLinks,
  logo,
  plainText,
  primaryButton,
  signInLink,
  tags,
  title,
  webriqForms,
} from "../../../common/fields";
import { hideIfVariantIn } from "@webriq-pagebuilder/sanity-plugin-schema-default";

export const callToActionSchema = [
  logo(hideIfVariantIn(["variant_c", "variant_e", "variant_f", "variant_g"])),
  title(hideIfVariantIn(["variant_e"])),
  plainText(hideIfVariantIn(["variant_e", "variant_f", "variant_g"])),
  primaryButton(hideIfVariantIn(["variant_b", "variant_c", "variant_e"])),
  webriqForms(hideIfVariantIn(["variant_a"])),
  signInLink(
    hideIfVariantIn([
      "variant_a",
      "variant_b",
      "variant_c",
      "variant_f",
      "variant_g",
    ])
  ),
  formLinks(
    hideIfVariantIn([
      "variant_a",
      "variant_b",
      "variant_c",
      "variant_f",
      "variant_g",
    ])
  ),
  tags(
    "Add Tags",
    "",
    hideIfVariantIn([
      "variant_a",
      "variant_b",
      "variant_d",
      "variant_e",
      "variant_f",
      "variant_g",
    ])
  ),
];
