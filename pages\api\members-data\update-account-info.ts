import { supabaseClient } from "utils/supabase/client";

export default async function PUT(req, res) {
  if (req.method !== "PUT") {
    return res.status(405).json({ message: "Method Not Allowed" });
  }

  const {
    userId,
    memberId,
    firstName,
    lastName,
    middleInitial,
    address,
    secondaryAddress,
    city,
    state,
    zipCode,
    authorizedShoppers,
    contactInfo,
    personalInfo,
    householdMembers,
    ageGroups,
    totalMemberIncome,
    preferredLanguage,
    housingType,
    householdNotes,
    householdSize,
  } = req.body;

  if (
    !userId ||
    !memberId ||
    !firstName ||
    !lastName ||
    !address ||
    !city ||
    !state ||
    !zipCode ||
    !householdMembers ||
    !totalMemberIncome ||
    !householdSize
  ) {
    return res.status(400).json({ message: "Missing required fields" });
  }

  try {
    const { data: user, error: userError } = await supabaseClient
      .from("users")
      .update({
        first_name: firstName,
        last_name: lastName,
      })
      .eq("id", userId);

    if (userError) {
      return res
        .status(500)
        .json({ message: "Error updating user", error: userError.message });
    }

    const { data: member, error: memberError } = await supabaseClient
      .from("members")
      .update({
        first_name: firstName,
        last_name: lastName,
        middle_initial: middleInitial,
        address: address,
        secondary_address: secondaryAddress,
        city: city,
        state: state,
        zip_code: zipCode,
        authorized_shopper: authorizedShoppers.split(","),
        household_size: Number(householdSize),
        annual_income: Number(totalMemberIncome),
        updated_at: new Date().toISOString(),
        contact_info: contactInfo,
      })
      .eq("user_id", userId);

    if (memberError) {
      return res
        .status(500)
        .json({ message: "Error updating member", error: memberError.message });
    }

    const { data: additionalInfo, error: additionalInfoError } =
      await supabaseClient
        .from("additional_info")
        .select()
        .eq("member_id", memberId);

    if (additionalInfoError) {
      return res.status(500).json({
        message: "Error fetching additional info",
        error: additionalInfoError.message,
      });
    }

    if (additionalInfo?.length > 0) {
      const { data: updatedAdditionalInfo, error: updatedAdditionalInfoError } =
        await supabaseClient
          .from("additional_info")
          .update({
            household_members: householdMembers,
            personal_info: personalInfo,
            age_groups: ageGroups,
            preferred_language: preferredLanguage,
            housing_type: housingType,
            household_note_verification: householdNotes,
          })
          .eq("member_id", memberId);

      if (updatedAdditionalInfoError) {
        return res.status(500).json({
          message: "Error updating additional info",
          error: updatedAdditionalInfoError.message,
        });
      }

      return res.status(200).json({
        message: "Additional info updated successfully",
        data: updatedAdditionalInfo,
      });
    } else {
      const {
        data: insertedAdditionalInfo,
        error: insertedAdditionalInfoError,
      } = await supabaseClient.from("additional_info").insert([
        {
          member_id: memberId,
          household_members: householdMembers,
          personal_info: personalInfo,
          age_groups: ageGroups,
          preferred_language: preferredLanguage,
          housing_type: housingType,
          household_note_verification: householdNotes,
        },
      ]);

      if (insertedAdditionalInfoError) {
        return res.status(500).json({
          message: "Error inserting additional info",
          error: insertedAdditionalInfoError.message,
        });
      }

      return res.status(201).json({
        message: "Additional info inserted successfully",
        data: insertedAdditionalInfo,
      });
    }
  } catch (error) {
    console.error("Error updating account info:", error);
    return res
      .status(500)
      .json({ message: "Internal Server Error", error: error.message });
  }
}
