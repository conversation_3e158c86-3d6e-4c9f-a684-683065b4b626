import { Card } from "@stackshift-ui/card";
import { Container } from "@stackshift-ui/container";
import { Flex } from "@stackshift-ui/flex";
import { Heading } from "@stackshift-ui/heading";
import { Image } from "@stackshift-ui/image";
import { Section } from "@stackshift-ui/section";
import { Text } from "@stackshift-ui/text";
import React from "react";
import { FeaturesProps } from ".";
import { ArrayOfImageTitleAndText } from "types";

export default function Features_J({ title, features }: FeaturesProps) {
  return (
    <Section className="py-20 bg-white">
      <Container maxWidth={1280}>
        <Container maxWidth={448} className="mb-16 text-center">
          <CaptionAndTitleSection title={title} />
        </Container>
        <FeatureItems features={features} />
      </Container>
    </Section>
  );
}

function CaptionAndTitleSection({
  title,
}: {
  caption?: string;
  title?: string;
}) {
  return (
    <React.Fragment>
      {title && (
        <Heading
          type="h2"
          fontSize="4xl"
          weight="thin"
          className="mb-5 text-center !text-cfc-secondary"
        >
          {title}
        </Heading>
      )}
    </React.Fragment>
  );
}

function FeatureItems({ features }: { features?: ArrayOfImageTitleAndText[] }) {
  if (!features) return null;

  return (
    <Flex wrap justify="start" className="lg:w-[70%] mx-auto">
      {features.map((feature) => {
        return <FeatureItem feature={feature} key={feature._key} />;
      })}
    </Flex>
  );
}

function FeatureItem({ feature }: { feature: ArrayOfImageTitleAndText }) {
  return (
    <div className="w-full px-3 mb-2 md:w-1/2">
      <div className="flex flex-wrap h-full p-6">
        <div className="w-full lg:w-2/3 text-center mx-auto">
          <Text fontSize="lg" weight="black" className="mb-2 text-cfc-gray">
            {feature?.title}
          </Text>
          <Text className="my-3" muted>
            {feature?.plainText}
          </Text>
          <Text className="italic" muted>
            {feature?.subtitle}
          </Text>
        </div>
      </div>
    </div>
  );
}

export { Features_J };
