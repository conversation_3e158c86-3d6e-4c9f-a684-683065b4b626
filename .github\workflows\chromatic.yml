name: "Chromatic"

on:
  pull_request:
    branches:
      - master
      - main

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
        with:
          fetch-depth: 0

      - name: Use Node.js cache
        uses: actions/cache@v3
        with:
          path: |
            ~/.npm
            **/node_modules
          key: ${{ runner.os }}-node-${{ hashFiles('**/yarn.lock') }}
          restore-keys: |
            ${{ runner.os }}-node-

      - uses: actions/setup-node@v3
        with:
          node-version: 18
          cache: "yarn"

      - run: yarn

      - uses: chromaui/action@latest
        with:
          projectToken: ${{ secrets.CHROMATIC_PROJECT_TOKEN }}
          token: ${{ secrets.GITHUB_TOKEN }}
          onlyChanged: true
        env:
          STORYBOOK_SANITY_PROJECT_ID: ${{ secrets.STORYBOOK_SANITY_PROJECT_ID }}
          STORYBOOK_SANITY_DATASET: ${{ secrets.STORYBOOK_SANITY_DATASET }}
          STORYBOOK_SANITY_API_READ_TOKEN: ${{ secrets.STORYBOOK_SANITY_API_READ_TOKEN }}
          STORYBOOK_SANITY_STUDIO_IN_CSTUDIO: ${{ secrets.STORYBOOK_SANITY_STUDIO_IN_CSTUDIO }}
