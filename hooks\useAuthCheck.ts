import { useEffect, useState } from "react";
import { useRouter } from "next/router";
import { supabaseClient } from "utils/supabase/client";

interface User {
  id: string;
  email?: string;
  [key: string]: any;
}

export const useAuthCheck = () => {
  const [isAuthenticated, setIsAuthenticated] = useState<boolean | null>(null);
  const [user, setUser] = useState<User | null>(null);
  const [token, setToken] = useState<string | null>(null); // ✅ Add token state
  const [loading, setLoading] = useState<boolean>(true);
  const router = useRouter();

  useEffect(() => {
    const checkAuth = async () => {
      try {
        const {
          data: { session },
          error,
        } = await supabaseClient.auth.getSession();

        if (error || !session) {
          setIsAuthenticated(false);
          setUser(null);
          setToken(null);
        } else {
          setIsAuthenticated(true);
          setUser(session.user);
          setToken(session.access_token);
        }
      } catch (error) {
        console.error("Error checking authentication:", error);
        setIsAuthenticated(false);
        setUser(null);
        setToken(null);
      } finally {
        setLoading(false);
      }
    };

    checkAuth();
  }, []);

  useEffect(() => {
    if (!loading && isAuthenticated === false) {
      router.push("/login");
    }
  }, [loading, isAuthenticated, router]);

  return { isAuthenticated, user, token, loading };
};
