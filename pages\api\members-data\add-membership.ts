import { NextApiRequest, NextApiResponse } from "next";
import { addTimeToDate } from "utils";
import { supabaseClient } from "utils/supabase/client";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== "POST") {
    return res.status(405).json({ message: "Method not allowed" });
  }

  try {
    const {
      member_id,
      start_date,
      end_date,
      total_points,
      amount,
      payment_method,
      role,
      userId,
      added_store_location,
      split_payments,
      coupon_code,
      fee_waived,
      credit_card_fee,
    } = req.body;

    if (!role || !["staff", "admin"].includes(role)) {
      return res.status(403).json({
        message: "Unauthorized. Only staff and admin can create memberships.",
      });
    }

    const { data: userData, error: userError } = await supabaseClient
      .from("users")
      .select("id, user_auth_id")
      .eq("user_auth_id", userId)
      .single();

    if (userError) {
      return res.status(404).json({
        message: "User not found",
      });
    }

    const { data: memberData, error: memberError } = await supabaseClient
      .from("members")
      .select("id")
      .eq("user_id", userData?.id)
      .single();

    if (memberError) {
      return res.status(404).json({
        message: "Member not found",
      });
    }

    let couponData;
    if (payment_method === "coupon") {
      const { data, error } = await supabaseClient
        .from("coupons")
        .select("id, point_value, start_date, end_date, status, coupon_limit")
        .ilike("coupon_code", coupon_code)
        .single();

      if (error || !data) {
        return res.status(404).json({
          message: "Coupon not found or invalid.",
        });
      }

      couponData = data;
      const currentDate = new Date().toISOString().split("T")[0];
      const isCouponValid =
        data.start_date <= currentDate &&
        data.end_date >= currentDate &&
        data.status === "active";

      if (!isCouponValid) {
        return res.status(400).json({
          message: "Coupon is not valid or expired.",
        });
      }

      // Check coupon usage count against coupon limit
      if (data.coupon_limit > 0) {
        const { count, error: countError } = await supabaseClient
          .from("payments")
          .select("id", { count: "exact", head: true })
          .eq("coupon_id", data.id)
          .eq("method", "coupon");

        if (countError) {
          console.error("Error checking coupon usage:", countError);
          return res.status(500).json({
            message: "Error validating coupon",
            error: countError.message,
          });
        }

        if (count && count >= data.coupon_limit) {
          return res.status(400).json({
            message: "Coupon has reached its usage limit.",
          });
        }
      }
    }

    const { data: membershipData, error: membershipError } =
      await supabaseClient
        .from("memberships")
        .insert({
          member_id,
          start_date: addTimeToDate(start_date),
          end_date: addTimeToDate(end_date),
          total_points:
            payment_method === "coupon" ? couponData.point_value : total_points,
          available_points:
            payment_method === "coupon" ? couponData.point_value : total_points,
          total_paid: amount,
          status: "active",
          added_by: memberData.id,
          added_store_location,
        })
        .select()
        .single();

    if (membershipError) {
      console.error("Error adding membership:", membershipError);
      return res.status(500).json({
        message: "Error adding membership",
        error: membershipError.message,
      });
    }

    const { error: paymentError } = await supabaseClient
      .from("payments")
      .insert({
        membership_id: membershipData.id,
        amount,
        method: split_payments ? "split" : payment_method,
        date: new Date().toISOString(),
        split_details: split_payments ? split_payments : null,
        added_store_location,
        coupon_id: payment_method === "coupon" ? couponData.id : null,
        fee_waived,
        credit_card_fee,
      });

    if (paymentError) {
      console.error("Error adding payment:", paymentError);
      return res.status(500).json({
        message: "Error adding payment",
        error: paymentError.message,
      });
    }

    return res.status(200).json({
      message: "Membership added successfully",
    });
  } catch (error: any) {
    console.error("Error adding membership:", error);
    return res.status(500).json({
      message: error.message || "Error adding membership",
    });
  }
}
