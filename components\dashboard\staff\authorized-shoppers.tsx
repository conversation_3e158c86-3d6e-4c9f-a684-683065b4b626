import React from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Skeleton } from "@/components/ui/skeleton";
import { useSelectClient } from "context/useSelectClientContext";
import { useClientSearch } from "context/useClientSearchContext";

export default function AuthorizedShoppers({
  isDisabled,
}: {
  isDisabled: boolean;
}) {
  const { selectedClient } = useSelectClient();
  const { isSearching } = useClientSearch();

  return (
    <div className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {[0, 1, 2].map((index) => (
          <div key={index} className="space-y-2">
            <Label htmlFor={`authorizedShopper-${index}`}>
              Shopper #{index + 1}
            </Label>
            {isSearching ? (
              <Skeleton className="w-full h-9" />
            ) : (
              <Input
                id={`authorizedShopper-${index}`}
                name={`authorizedShopper-${index}`}
                defaultValue={
                  (selectedClient?.authorized_shopper &&
                    JSON?.parse(selectedClient?.authorized_shopper)?.[index]) ||
                  ""
                }
                disabled={isDisabled}
              />
            )}
          </div>
        ))}
      </div>
    </div>
  );
}
