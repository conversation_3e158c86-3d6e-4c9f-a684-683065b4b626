import { Card } from "@stackshift-ui/card";
import { Container } from "@stackshift-ui/container";
import { Flex } from "@stackshift-ui/flex";
import { Heading } from "@stackshift-ui/heading";
import { Image } from "@stackshift-ui/image";
import { Section } from "@stackshift-ui/section";
import { Text } from "@stackshift-ui/text";
import { MemberTextProps, TeamsProps } from ".";
import { Team as iTeam } from "types";

export default function Team_E({ caption, title, team }: TeamsProps) {
  return (
    <Section className="py-20 bg-white">
      <Container maxWidth={1280}>
        <CaptionAndTitle caption={caption} title={title} />
        <TeamMemberCard team={team} />
      </Container>
    </Section>
  );
}

function CaptionAndTitle({
  caption,
  title,
}: {
  caption?: string;
  title?: string;
}) {
  return (
    <Container maxWidth={576} className="mb-8 text-center lg:mb-16">
      {title && (
        <Heading
          type="h2"
          fontSize="4xl"
          weight="thin"
          className="mb-5 text-center !text-cfc-secondary"
        >
          {title}
        </Heading>
      )}
    </Container>
  );
}

function TeamMemberCard({ team }: { team?: iTeam[] }) {
  if (!team) return null;

  return (
    <Flex wrap>
      {team &&
        team.map((member, index) => (
          <div className="w-full px-4 mb-20 md:w-1/2 lg:w-[33.3%]" key={index}>
            <div className="items-center p-0 overflow-hidden text-center">
              {member.mainImage?.image && (
                <div>
                  <Image
                    className="h-[320px] w-full object-cover rounded-global mx-auto lg:w-1/2"
                    sizes="100vw"
                    src={`${member?.mainImage?.image}`}
                    width={179}
                    height={320}
                    alt={
                      member?.mainImage?.alt ??
                      `team-member-${member?.name}-profile-image`
                    }
                  />
                </div>
              )}
              <TeamMemberText member={member} />
            </div>
          </div>
        ))}
    </Flex>
  );
}

function TeamMemberText({ member }: MemberTextProps) {
  return (
    <div className="p-4 w-full lg:pl-6 mx-auto text-left">
      <Text
        weight="black"
        fontSize="lg"
        className="mb-4 text-center text-cfc-gray uppercase"
      >
        {member?.name}
      </Text>
      <Text weight="bold" fontSize="base" className="mb-3 text-cfc-gray">
        {member?.jobTitle}
      </Text>
      <a className="text-cfc-primary mb-2" href={`mailto:${member?.email}`}>
        {member?.email}
      </a>
      <Text className="my-4 leading-loose" muted>
        {member?.plainText}
      </Text>
      <Text weight="bold" fontSize="base" className="mb-3 text-cfc-gray">
        Favorite Fruit or Vegetable:{" "}
        <span className="font-normal">{member?.favorite}</span>
      </Text>
    </div>
  );
}

export { Team_E };
