import { Button } from "@stackshift-ui/button";
import { Container } from "@stackshift-ui/container";
import { Flex } from "@stackshift-ui/flex";
import { Heading } from "@stackshift-ui/heading";
import { Image } from "@stackshift-ui/image";
import { Section } from "@stackshift-ui/section";
import { Text } from "@stackshift-ui/text";
import React from "react";
import { ButtonProps, HeaderProps } from ".";

import { PortableText } from "@portabletext/react";
import { PortableTextBlock } from "sanity";
import { MyPortableTextComponents } from "types";

export const defaultBlockStyle: MyPortableTextComponents = {
  block: {
    h1: ({ children }) => (
      <Heading
        fontSize="3xl"
        weight="bold"
        className="mb-8 leading-normal text-black dark:text-white"
      >
        {children}
      </Heading>
    ),
    h2: ({ children }) => (
      <Heading
        type="h2"
        weight="bold"
        fontSize="2xl"
        className="mb-8 text-black dark:text-white"
      >
        {children}
      </Heading>
    ),
    h3: ({ children }) => (
      <Heading
        type="h3"
        fontSize="xl"
        weight="bold"
        className="mb-8 leading-normal text-black dark:text-white"
      >
        {children}
      </Heading>
    ),
    h4: ({ children }) => (
      <Heading
        type="h4"
        weight="bold"
        fontSize="lg"
        className="mb-8 leading-normal text-black dark:text-white"
      >
        {children}
      </Heading>
    ),
    normal: ({ children }) => (
      <Text className="md:text-2xl text-xl leading-snug tracking-normal text-cfc-gray">
        {children}
      </Text>
    ),
    blockquote: ({ children }) => (
      <blockquote className="mb-6 italic leading-loose text-gray-500 px-14">
        - {children}
      </blockquote>
    ),
  },
  code: ({ value }) => (
    <pre data-language={value.language}>
      <code>{value.code}</code>
    </pre>
  ),
  list: {
    bullet: ({ children }) => (
      <ul className="flex flex-col pl-10 mb-8 space-y-4 leading-relaxed list-disc">
        {children}
      </ul>
    ),
    number: ({ children }) => (
      <ol className="flex flex-col pl-10 mb-8 space-y-4 leading-relaxed list-decimal">
        {children}
      </ol>
    ),
  },
  listItem: {
    bullet: ({ children }) => <li className="leading-relaxed">{children}</li>,
    number: ({ children }) => <li className="leading-relaxed">{children}</li>,
  },
  marks: {
    strong: ({ children }) => <strong>{children}</strong>,
    em: ({ children }) => <em>{children}</em>,
    code: ({ children }) => <code>{children}</code>,
    link: ({ children, value }) => (
      <a
        className="hover:text-primary-foreground text-primary"
        href={value.href}
        target="_blank"
        rel="noopener noreferrer"
      >
        {children}
      </a>
    ),
  },
  types: {
    addImage: ({ value }) => (
      <Image
        className="w-full h-full mb-5"
        width={500}
        height={500}
        sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
        src={urlFor(value?.image)}
        alt={value?.alt ?? value?.image?.asset?._ref}
      />
    ),
  },
};

export default function Header_M({
  mainImage,
  title,
  description,
  primaryButton,
  secondaryButton,
  bgImage,
  firstColumn,
}: HeaderProps) {
  return (
    <Section className="relative w-full !px-0 md:py-10">
      <Container maxWidth={1280} className="!px-0">
        <Flex className="w-full md:w-10/12 mx-auto flex flex-col relative !md:px-4">
          <MainImage mainImage={mainImage} />
          <TitleAndDescription title={title} description={description} />
        </Flex>
      </Container>
    </Section>
  );
}

function TitleAndDescription({
  title,
  description,
}: {
  title?: string;
  description?: string;
}) {
  return (
    <div className="w-full h-full flex items-center flex-col justify-end mx-auto p-5">
      {title && (
        <Heading
          type="h1"
          className="mb-5 font-light font-heading leading-none text-center !text-cfc-gray md:text-4xl text-3xl"
        >
          {title}
        </Heading>
      )}

      {description && (
        <Text
          muted
          className="my-3 text-center !md:text-3xl !text-2xl leading-tight !font-light"
        >
          {description}
        </Text>
      )}
    </div>
  );
}

interface MainImageProps {
  mainImage?: {
    image?: string | any;
    alt?: string;
  };
}

function MainImage({ mainImage }: MainImageProps) {
  if (!mainImage?.image) return null;

  return (
    <div className="relative w-full h-full pb-6">
      <Image
        className="p-2 object-cover object-center w-full relative"
        src={`${mainImage.image}`}
        width={1200}
        height={600}
        alt={mainImage.alt ?? "header-main-image"}
      />
    </div>
  );
}

export { Header_M };
